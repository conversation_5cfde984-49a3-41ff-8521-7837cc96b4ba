{"name": "dashboard-sveltekit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "vercelbuild": "bun x vercel build", "vcbuild": "vc --cwd build", "predeploy": "bun x vercel deploy --prebuilt", "deploy": "bun x vercel deploy --prod", "prepare": "svelte-kit sync || echo ''", "check:old": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:old:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "kill:dev": "kill -9 $(lsof -t -i:8000)", "test:unit": "vitest", "test": "npm run test:unit -- --run", "check": "npm run lint && npm run format", "lint": "oxlint --fix", "lint:check": "ox<PERSON>", "format": "dprint fmt", "format:check": "dprint check", "lint:fix": "oxlint --fix && dprint fmt", "clean": "rm -rf node_modules bun.lock .svelte-kit"}, "devDependencies": {"@iconify/svelte": "^5.0.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.27.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@vitest/browser": "^3.2.4", "daisyui": "^5.0.50", "dprint": "^0.50.1", "oxlint": "^1.9.0", "playwright": "^1.54.2", "prettier-plugin-svelte": "^3.4.0", "svead": "^0.0.15", "svelte": "^5.37.3", "svelte-awesome-color-picker": "^4.0.2", "svelte-check": "^4.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vite": "^7.0.6", "vite-plugin-devtools-json": "^0.4.1", "vitest": "^3.2.4", "vitest-browser-svelte": "^1.0.0"}, "dependencies": {"@sveltejs/adapter-vercel": "^5.8.1", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "runed": "^0.31.1", "svelte-i18n": "^4.0.1", "sweetalert2": "^11.22.2", "sweetalert2-neutral": "^11.22.2-neutral", "zod": "^4.0.14"}}