import { browser } from '$app/environment';
import { init, locale, register, waitLocale } from 'svelte-i18n';
import { LogCategory, logger } from '../utils/logger';

const defaultLocale = 'th';

// Register locales with error handling
const registerLocale = async (code: string, loader: () => Promise<any>) => {
  try {
    register(code, loader);
    logger.info(LogCategory.SYSTEM, 'locale_registered', `Locale ${code} registered successfully`);
  }
  catch (error) {
    logger.error(LogCategory.SYSTEM, 'locale_register_error', `Failed to register locale ${code}`, {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

registerLocale('en', () => import('./locales/en.json'));
registerLocale('th', () => import('./locales/th.json'));
registerLocale('lo', () => import('./locales/lo.json'));

// Initialize i18n with error handling
try {
  init({
    fallbackLocale: defaultLocale,
    initialLocale: defaultLocale, // ✅ ใช้ default locale เสมอเพื่อป้องกัน hydration mismatch
    loadingDelay: 200,
    formats: {
      number: {
        currency: { style: 'currency', currency: 'THB' },
        decimal: { minimumFractionDigits: 2, maximumFractionDigits: 2 },
        percent: { style: 'percent' },
      },
      date: {
        short: { month: 'numeric', day: 'numeric', year: '2-digit' },
        long: {
          weekday: 'long',
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        },
        medium: { month: 'short', day: 'numeric', year: 'numeric' },
      },
      time: {
        short: { hour: 'numeric', minute: 'numeric' },
        long: { hour: 'numeric', minute: 'numeric', second: 'numeric' },
        medium: { hour: 'numeric', minute: 'numeric', hour12: false },
      },
    },
  });

  logger.info(LogCategory.SYSTEM, 'i18n_initialized', 'i18n system initialized successfully', {
    defaultLocale,
    initialLocale: defaultLocale,
  });
}
catch (error) {
  logger.error(LogCategory.SYSTEM, 'i18n_init_error', 'Failed to initialize i18n system', {
    error: error instanceof Error ? error.message : 'Unknown error',
  });
}



function isValidLocale(locale: string): boolean {
  return ['th', 'en', 'lo'].includes(locale);
}

export { locale, waitLocale };

export const supportedLocales = [
  { code: 'th', name: 'ไทย', flag: 'emojione:flag-for-thailand' },
  { code: 'en', name: 'English', flag: 'emojione:flag-for-united-states' },
  { code: 'lo', name: 'ລາວ', flag: 'emojione:flag-for-laos' },
];

export function setLocale(newLocale: string) {
  try {
    if (!isValidLocale(newLocale)) {
      logger.warn(
        LogCategory.SYSTEM,
        'invalid_locale',
        `Invalid locale: ${newLocale}, using default`,
      );
      newLocale = defaultLocale;
    }

    locale.set(newLocale);

    if (browser) {
      // เก็บภาษาใน localStorage ทั้งสอง key เพื่อความเข้ากันได้
      localStorage.setItem('locale', newLocale);
      localStorage.setItem('language', newLocale);

      // เก็บใน cookie ด้วยเพื่อให้ server สามารถอ่านได้
      document.cookie = `locale=${newLocale}; path=/; max-age=31536000; SameSite=Lax`;
    }

    logger.info(LogCategory.SYSTEM, 'locale_changed', 'Locale changed successfully', {
      newLocale,
    });
  }
  catch (error) {
    logger.error(LogCategory.SYSTEM, 'locale_change_error', 'Failed to change locale', {
      newLocale,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// ฟังก์ชันสำหรับตั้งค่าภาษาเริ่มต้นจาก server
export function setInitialLocale(serverLocale: string) {
  if (browser && isValidLocale(serverLocale)) {
    // ตรวจสอบว่าภาษาใน localStorage ตรงกับ server หรือไม่
    const keys = ['language', 'locale'];
    let savedLocale: string | null = null;

    for (const key of keys) {
      const lang = localStorage.getItem(key);
      if (lang && isValidLocale(lang)) {
        savedLocale = lang;
        break;
      }
    }

    if (!savedLocale || savedLocale !== serverLocale) {
      // ถ้าไม่ตรงกัน ให้ใช้ภาษาจาก server
      setLocale(serverLocale);
    }
  }
}
