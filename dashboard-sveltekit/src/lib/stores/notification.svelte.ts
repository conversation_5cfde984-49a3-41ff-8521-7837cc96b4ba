import { browser } from '$app/environment';
import type { NotificationFilterData } from '$lib/schemas/notification.schema';
import { notificationService } from '$lib/services/notification';
import type { Notification, NotificationSettings, NotificationStats } from '$lib/types/notification';

import { authStore } from './auth.svelte';
import { siteStore } from './site.svelte';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  settings: NotificationSettings | null;
  stats: NotificationStats[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class NotificationStore {
  private state = $state<NotificationState>({
    notifications: [],
    unreadCount: 0,
    settings: null,
    stats: [],
    isLoading: false,
    error: null,
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      pages: 0,
    },
  });

  // Getters
  get notifications() {
    return this.state.notifications;
  }
  get unreadCount() {
    return this.state.unreadCount;
  }
  get settings() {
    return this.state.settings;
  }
  get stats() {
    return this.state.stats;
  }
  get isLoading() {
    return this.state.isLoading;
  }
  get error() {
    return this.state.error;
  }
  get pagination() {
    return this.state.pagination;
  }

  // Actions
  async loadNotifications(
    params: {
      page?: number;
      limit?: number;
      type?: string;
      status?: string;
    } = {},
  ) {
    if (!browser) return;

    this.state.isLoading = true;
    this.state.error = null;

    try {
      const currentSite = siteStore.site;
      const currentUser = authStore.user;

      if (!currentSite || !currentUser) {
        this.state.error = 'ไม่พบข้อมูลเว็บไซต์หรือผู้ใช้';
        return;
      }

      const filters: Partial<NotificationFilterData> = {
        page: params.page || 1,
        limit: params.limit || 20,
        type: params.type as any,
        status: params.status as any,
      };

      const response = await notificationService.getNotifications(
        currentSite._id,
        filters,
        'session-auth',
      );

      if (response.success && response.data) {
        // Transform API response to match Notification type
        this.state.notifications = response.data.notifications.map((notification: any) => ({
          _id: notification._id,
          siteId: notification.siteId,
          recipientId: notification.userId || '',
          recipientType: 'user' as const,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          priority: notification.priority,
          status: notification.isRead ? 'read' : 'unread',
          channels: {
            inApp: true,
            email: false,
            push: false,
            sms: false,
          },
          deliveryStatus: {
            inApp: { sent: true, delivered: true, read: notification.isRead },
            email: { sent: false, delivered: false, opened: false },
            push: { sent: false, delivered: false, clicked: false },
            sms: { sent: false, delivered: false },
          },
          scheduledAt: notification.scheduledAt ? new Date(notification.scheduledAt) : undefined,
          sentAt: notification.sentAt ? new Date(notification.sentAt) : undefined,
          readAt: notification.readAt ? new Date(notification.readAt) : undefined,
          createdAt: new Date(notification.createdAt || Date.now()),
          updatedAt: new Date(notification.updatedAt || Date.now()),
        }));
        this.state.pagination = {
          page: response.data.pagination.page,
          limit: response.data.pagination.limit,
          total: response.data.pagination.total,
          pages: response.data.pagination.totalPages,
        };

        // Calculate unread count from notifications
        this.state.unreadCount = response.data.notifications.filter(n => n.status === 'unread').length;
      }
      else {
        this.state.error = response.error || 'เกิดข้อผิดพลาดในการโหลดการแจ้งเตือน';
      }
    }
    catch (error) {
      this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
      console.error('Error loading notifications:', error);
    }
    finally {
      this.state.isLoading = false;
    }
  }

  async loadUnreadCount() {
    if (!browser) return;

    try {
      const currentSite = siteStore.site;
      const currentUser = authStore.user;

      if (!currentSite || !currentUser) {
        return;
      }

      // Load notifications with limit 1 to get stats
      const response = await notificationService.getNotifications(
        currentSite._id,
        { limit: 1, status: 'unread' },
        'session-auth',
      );

      if (response.success && response.data) {
        this.state.unreadCount = response.data.pagination.total;
      }
    }
    catch (error) {
      console.error('Error loading unread count:', error);
    }
  }

  // โหลดการแจ้งเตือนส่วนตัว (รวมทั้ง site และ personal)
  async loadUserNotifications(
    params: {
      page?: number;
      limit?: number;
      type?: string;
      status?: string;
    } = {},
  ) {
    if (!browser) return;

    this.state.isLoading = true;
    this.state.error = null;

    try {
      const currentUser = authStore.user;

      if (!currentUser) {
        this.state.error = 'ไม่พบข้อมูลผู้ใช้';
        return;
      }

      // Build query parameters
      const searchParams = new URLSearchParams();
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.type) searchParams.append('type', params.type);
      if (params.status) searchParams.append('status', params.status);

      // Call Route API instead of service directly
      const response = await fetch(`/api/notifications/user?${searchParams.toString()}`, {
        method: 'GET',
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success && result.data) {
        // Transform API response to match Notification type
        this.state.notifications = result.data.notifications.map((notification: any) => ({
          _id: notification._id,
          siteId: notification.siteId,
          recipientId: notification.userId || '',
          recipientType: 'user' as const,
          type: notification.type as any,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          priority: notification.priority,
          status: notification.isRead ? 'read' : 'unread',
          channels: {
            inApp: true,
            email: false,
            push: false,
            sms: false,
          },
          deliveryStatus: {
            inApp: { sent: true, delivered: true, read: notification.isRead },
            email: { sent: false, delivered: false, opened: false },
            push: { sent: false, delivered: false, clicked: false },
            sms: { sent: false, delivered: false },
          },
          scheduledAt: notification.scheduledAt ? new Date(notification.scheduledAt) : undefined,
          sentAt: notification.sentAt ? new Date(notification.sentAt) : undefined,
          readAt: notification.readAt ? new Date(notification.readAt) : undefined,
          createdAt: new Date(notification.createdAt || Date.now()),
          updatedAt: new Date(notification.updatedAt || Date.now()),
        }));

        // Calculate unread count from notifications
        this.state.unreadCount = result.data.notifications.filter((n: any) => !n.isRead).length;

        // Transform pagination
        this.state.pagination = {
          page: result.data.pagination.page,
          limit: result.data.pagination.limit,
          total: result.data.pagination.total,
          pages: result.data.pagination.totalPages,
        };
      }
      else {
        this.state.error = result.error || 'เกิดข้อผิดพลาดขณะโหลดการแจ้งเตือน';
      }
    }
    catch (error) {
      console.error('Error loading user notifications:', error);
      this.state.error = 'เกิดข้อผิดพลาดขณะโหลดการแจ้งเตือน';
    }
    finally {
      this.state.isLoading = false;
    }
  }

  // โหลดจำนวนการแจ้งเตือนที่ยังไม่อ่านของ user
  async loadUserUnreadCount() {
    if (!browser) return;

    try {
      const currentUser = authStore.user;

      if (!currentUser) {
        return;
      }

      const response = await notificationService.getUserUnreadCount('session-auth');

      if (response.success && response.data) {
        this.state.unreadCount = response.data.unreadCount;
      }
    }
    catch (error) {
      console.error('Error loading user unread count:', error);
    }
  }

  async markAsRead(notificationIds: string[]) {
    if (!browser) return;

    try {
      const formData = new FormData();
      formData.append('action', 'markAsRead');
      notificationIds.forEach(id => formData.append('notificationIds', id));

      const response = await fetch('/api/notifications/user', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // อัปเดต state
        this.state.notifications = this.state.notifications.map(notification =>
          notificationIds.includes(notification._id)
            ? { ...notification, status: 'read' as const, readAt: new Date() }
            : notification
        );

        // อัปเดต unread count
        const markedCount = notificationIds.length;
        this.state.unreadCount = Math.max(0, this.state.unreadCount - markedCount);
      }
      else {
        this.state.error = result.error || 'เกิดข้อผิดพลาดในการทำเครื่องหมายว่าอ่านแล้ว';
      }
    }
    catch (error) {
      this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
      console.error('Error marking as read:', error);
    }
  }

  async markAllAsRead() {
    if (!browser) return;

    try {
      const formData = new FormData();
      formData.append('action', 'markAllAsRead');

      const response = await fetch('/api/notifications/user', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // อัปเดต state - ทำเครื่องหมายทั้งหมดว่าอ่านแล้ว
        this.state.notifications = this.state.notifications.map(notification => ({
          ...notification,
          status: 'read' as const,
          readAt: new Date(),
        }));

        // รีเซ็ต unread count
        this.state.unreadCount = 0;
      }
      else {
        this.state.error = result.error || 'เกิดข้อผิดพลาดในการทำเครื่องหมายทั้งหมดว่าอ่านแล้ว';
      }
    }
    catch (error) {
      this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
      console.error('Error marking all as read:', error);
    }
  }

  async deleteNotification(notificationId: string) {
    if (!browser) return;

    try {
      const formData = new FormData();
      formData.append('action', 'delete');
      formData.append('notificationId', notificationId);

      const response = await fetch('/api/notifications/user', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // ลบออกจาก state
        const deletedNotification = this.state.notifications.find(n => n._id === notificationId);
        this.state.notifications = this.state.notifications.filter(n => n._id !== notificationId);

        // อัปเดต unread count ถ้าเป็น unread
        if (deletedNotification?.status === 'unread') {
          this.state.unreadCount = Math.max(0, this.state.unreadCount - 1);
        }
      }
      else {
        this.state.error = result.error || 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน';
      }
    }
    catch (error) {
      this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
      console.error('Error deleting notification:', error);
    }
  }

  async loadStats() {
    if (!browser) return;

    try {
      const currentSite = siteStore.site;

      if (!currentSite) {
        return;
      }

      const response = await notificationService.getNotificationStats(currentSite._id, 'session-auth');

      if (response.success && response.data) {
        // Convert NotificationStatsData to NotificationStats array
        this.state.stats = [
          { _id: 'total', count: response.data.total, unread: response.data.unread },
          { _id: 'read', count: response.data.read, unread: 0 },
          { _id: 'archived', count: response.data.archived, unread: 0 },
        ];
      }
    }
    catch (error) {
      console.error('Error loading stats:', error);
    }
  }

  // Utility methods
  clearError() {
    this.state.error = null;
  }

  reset() {
    this.state.notifications = [];
    this.state.unreadCount = 0;
    this.state.settings = null;
    this.state.stats = [];
    this.state.isLoading = false;
    this.state.error = null;
    this.state.pagination = {
      page: 1,
      limit: 20,
      total: 0,
      pages: 0,
    };
  }

  // Real-time updates (สำหรับใช้กับ WebSocket หรือ SSE ในอนาคต)
  addNotification(notification: Notification) {
    this.state.notifications.unshift(notification);
    if (notification.status === 'unread') {
      this.state.unreadCount += 1;
    }
  }

  updateNotification(notificationId: string, updates: Partial<Notification>) {
    const index = this.state.notifications.findIndex(n => n._id === notificationId);
    if (index !== -1) {
      this.state.notifications[index] = {
        ...this.state.notifications[index],
        ...updates,
      };
    }
  }

  // WebSocket methods
  updateUnreadCount(count: number) {
    this.state.unreadCount = count;
  }

  updateSettings(settings: NotificationSettings) {
    this.state.settings = settings;
  }

  destroy() {
    this.reset();
  }
}

// Export singleton instance
export const notificationStore = new NotificationStore();
