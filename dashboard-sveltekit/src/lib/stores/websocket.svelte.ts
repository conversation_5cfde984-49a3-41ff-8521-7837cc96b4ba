import { browser } from '$app/environment';
import { authStore } from './auth.svelte';
import { notificationStore } from './notification.svelte';

interface WebSocketMessage {
  type: string;
  data?: any;
}

class WebSocketStore {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private pingInterval: NodeJS.Timeout | null = null;
  private isConnecting = false;

  // State
  private _connected = $state(false);
  private _error = $state<string | null>(null);

  // Getters
  get connected() {
    return this._connected;
  }

  get error() {
    return this._error;
  }

  get connecting() {
    return this.isConnecting;
  }

  // เชื่อมต่อ WebSocket
  async connect() {
    if (!browser || this.isConnecting || this._connected) return;

    this.isConnecting = true;
    this._error = null;

    try {
      // รอให้ authentication พร้อม
      let isAuthenticated = authStore.isAuthenticated;
      let attempts = 0;
      const maxAttempts = 20;

      while (!isAuthenticated && attempts < maxAttempts) {
        console.log(`⏳ Waiting for authentication... (attempt ${attempts + 1}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 500));
        isAuthenticated = authStore.isAuthenticated;
        attempts++;
      }

      console.log('🔍 WebSocket connection check:', {
        isAuthenticated,
        attempts,
      });

      if (!isAuthenticated) {
        throw new Error('Authentication ไม่พร้อมหลังจากรอแล้ว');
      }

      // ใช้ backend URL แทน frontend URL
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000';
      const wsProtocol = backendUrl.startsWith('https:') ? 'wss:' : 'ws:';
      const wsHost = backendUrl.replace(/^https?:\/\//, '');
      const wsUrl = `${wsProtocol}//${wsHost}/v1/notifications/ws`;

      console.log('🔌 Connecting to WebSocket:', wsUrl);
      this.ws = new WebSocket(wsUrl);

      // เพิ่ม timeout สำหรับการเชื่อมต่อ
      const connectionTimeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          console.error('🔌 WebSocket connection timeout');
          this._error = 'การเชื่อมต่อใช้เวลานานเกินไป';
          this.isConnecting = false;
          this._connected = false;
          this.ws.close();
        }
      }, 10000); // 10 วินาที

      this.ws.onopen = () => {
        clearTimeout(connectionTimeout);
        console.log('🔌 Notification WebSocket connected');
        this._connected = true;
        this._error = null;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.authenticate();
        this.startPing();
      };

      this.ws.onmessage = event => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        }
        catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = event => {
        clearTimeout(connectionTimeout);
        console.log('🔌 Notification WebSocket disconnected:', event.code, event.reason);
        this._connected = false;
        this.isConnecting = false;
        this.stopPing();

        // ลองเชื่อมต่อใหม่ถ้าไม่ได้ปิดโดยตั้งใจ
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = error => {
        clearTimeout(connectionTimeout);
        console.error('🔌 Notification WebSocket error:', error);
        this._error = 'เกิดข้อผิดพลาดในการเชื่อมต่อ';
        this.isConnecting = false;
        this._connected = false;

        // ไม่หยุด reconnection ทันที แต่ให้ onclose จัดการ
        console.log('🔄 WebSocket error occurred, will retry via onclose handler');
      };
    }
    catch (error) {
      console.error('Error connecting to WebSocket:', error);
      this._error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
      this.isConnecting = false;
    }
  }

  // ยืนยันตัวตน
  private authenticate() {
    if (!this.ws || !authStore.isAuthenticated) return;

    // ใช้ getTokenFromCookies จาก authStore
    const token = this.getTokenFromCookies();
    if (!token) {
      console.error('❌ No token found in cookies');
      return;
    }

    this.ws.send(JSON.stringify({
      type: 'auth',
      data: {
        token,
      },
    }));
  }

  // ดึง token จาก cookies
  private getTokenFromCookies(): string | null {
    if (!browser) return null;

    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'auth_token' || name === 'accessToken') {
        return value;
      }
    }
    return null;
  }

  // จัดการข้อความที่ได้รับ
  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'auth_success':
        console.log('✅ Notification WebSocket authenticated');
        break;

      case 'auth_error':
        console.error('❌ Notification WebSocket auth error:', message.data);
        this._error = message.data?.message || 'การยืนยันล้มเหลว';
        break;

      case 'notification':
        // เพิ่มการแจ้งเตือนใหม่
        notificationStore.addNotification(message.data);
        break;

      case 'unread_count':
        // อัปเดตจำนวนการแจ้งเตือนที่ยังไม่อ่าน
        notificationStore.updateUnreadCount(message.data.count);
        break;

      case 'settings_update':
        // อัปเดตการตั้งค่า
        notificationStore.updateSettings(message.data);
        break;

      case 'pong':
        // ตอบกลับ ping
        break;

      case 'error':
        console.error('❌ Notification WebSocket error:', message.data);
        this._error = message.data?.message || 'เกิดข้อผิดพลาด';
        break;

      default:
        console.log('📨 Unknown WebSocket message type:', message.type);
    }
  }

  // ส่ง ping
  private startPing() {
    this.pingInterval = setInterval(() => {
      if (this.ws && this._connected) {
        this.ws.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000); // ping ทุก 30 วินาที
  }

  // หยุด ping
  private stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  // ลองเชื่อมต่อใหม่
  private scheduleReconnect() {
    // ไม่ reconnect ถ้ากำลัง connecting อยู่หรือเชื่อมต่อแล้ว
    if (this.isConnecting || this._connected) {
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`🔄 Scheduling WebSocket reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      // ตรวจสอบอีกครั้งก่อน reconnect
      if (!this.isConnecting && !this._connected && authStore.isAuthenticated) {
        this.connect();
      }
    }, delay);
  }

  // ปิดการเชื่อมต่อ
  disconnect() {
    this.stopPing();

    if (this.ws) {
      this.ws.close(1000, 'Disconnected by user');
      this.ws = null;
    }

    this._connected = false;
    this._error = null;
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  // ส่งข้อความ
  send(type: string, data?: any) {
    if (this.ws && this._connected) {
      this.ws.send(JSON.stringify({ type, data }));
    }
  }
}

// สร้าง instance
export const websocketStore = new WebSocketStore();
