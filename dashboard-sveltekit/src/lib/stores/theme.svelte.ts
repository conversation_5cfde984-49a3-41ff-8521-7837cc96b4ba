import { browser } from '$app/environment';
import { LogCategory, logger } from '../utils/logger';

export type Theme = 'light' | 'dark' | 'auto';

class ThemeStore {
  private _theme = $state<Theme>('light');
  private _broadcastChannel: BroadcastChannel | null = null;
  private _storageListener: ((event: StorageEvent) => void) | null = null;
  private _mediaQueryListener: ((event: MediaQueryListEvent) => void) | null = null;
  private _currentTabId: string = '';
  private _mediaQuery: MediaQueryList | null = null;

  constructor() {
    if (browser) {
      this._currentTabId = Math.random().toString(36).substring(2, 11);
      this.setupCrossTabSync();
      this.setupSystemThemeDetection();

      // ✅ รอให้ DOM พร้อมก่อนโหลด theme เพื่อป้องกัน hydration mismatch
      setTimeout(() => {
        this.loadTheme();
      }, 0);

      logger.info(LogCategory.SYSTEM, 'theme_store_initialized', 'Theme store initialized', {
        tabId: this._currentTabId,
        initialTheme: this._theme,
      });
    }
  }

  get theme() {
    return this._theme;
  }

  private setupCrossTabSync() {
    // ใช้ BroadcastChannel API สำหรับ modern browsers
    if ('BroadcastChannel' in window) {
      try {
        this._broadcastChannel = new BroadcastChannel('theme-sync');
        this._broadcastChannel.onmessage = event => {
          this.handleThemeMessage(event.data);
        };

        logger.info(
          LogCategory.SYSTEM,
          'theme_broadcast_setup',
          'Theme BroadcastChannel setup successful',
          {
            tabId: this._currentTabId,
          },
        );
      }
      catch (error) {
        logger.error(
          LogCategory.SYSTEM,
          'theme_broadcast_error',
          'Failed to create BroadcastChannel for theme',
          {
            error: error instanceof Error ? error.message : 'Unknown error',
            tabId: this._currentTabId,
          },
        );
      }
    }

    // ใช้ localStorage events สำหรับ fallback
    this._storageListener = event => {
      if (event.key === 'theme-event' && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          this.handleThemeMessage(data);
        }
        catch (error) {
          logger.error(
            LogCategory.SYSTEM,
            'theme_storage_parse_error',
            'Error parsing theme event',
            {
              error: error instanceof Error ? error.message : 'Unknown error',
              tabId: this._currentTabId,
            },
          );
        }
      }
    };

    window.addEventListener('storage', this._storageListener);
  }

  private setupSystemThemeDetection() {
    if (!browser || !window.matchMedia) return;

    try {
      this._mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      this._mediaQueryListener = event => {
        if (this._theme === 'auto') {
          const systemTheme = event.matches ? 'dark' : 'light';
          this.applyTheme(systemTheme);

          logger.info(
            LogCategory.SYSTEM,
            'system_theme_changed',
            'System theme preference changed',
            {
              systemTheme,
              currentTheme: this._theme,
              tabId: this._currentTabId,
            },
          );
        }
      };

      this._mediaQuery.addEventListener('change', this._mediaQueryListener);

      logger.info(
        LogCategory.SYSTEM,
        'system_theme_detection_setup',
        'System theme detection setup successful',
        {
          systemPrefersDark: this._mediaQuery.matches,
          tabId: this._currentTabId,
        },
      );
    }
    catch (error) {
      logger.error(
        LogCategory.SYSTEM,
        'system_theme_detection_error',
        'Failed to setup system theme detection',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          tabId: this._currentTabId,
        },
      );
    }
  }

  private handleThemeMessage(data: { type: string; payload?: any; tabId?: string; }) {
    // ตรวจสอบว่าเป็น event จากแท็บเดียวกันหรือไม่
    if (data.tabId === this._currentTabId) {
      logger.debug(LogCategory.SYSTEM, 'theme_event_ignored', 'Theme event from same tab ignored', {
        tabId: this._currentTabId,
      });
      return;
    }

    if (data.type === 'theme-change' && data.payload) {
      logger.info(
        LogCategory.SYSTEM,
        'theme_sync_received',
        'Theme change synced from another tab',
        {
          fromTabId: data.tabId,
          toTabId: this._currentTabId,
          newTheme: data.payload.theme,
          previousTheme: this._theme,
        },
      );

      this.setTheme(data.payload.theme, false); // ไม่ส่ง event กลับ
    }
  }

  private applyTheme(actualTheme: 'light' | 'dark') {
    if (browser) {
      try {
        document.documentElement.setAttribute('data-theme', actualTheme);

        logger.debug(LogCategory.SYSTEM, 'theme_applied', 'Theme applied to DOM', {
          appliedTheme: actualTheme,
          userTheme: this._theme,
          tabId: this._currentTabId,
        });
      }
      catch (error) {
        logger.error(LogCategory.SYSTEM, 'theme_apply_error', 'Failed to apply theme to DOM', {
          theme: actualTheme,
          error: error instanceof Error ? error.message : 'Unknown error',
          tabId: this._currentTabId,
        });
      }
    }
  }

  private getEffectiveTheme(): 'light' | 'dark' {
    if (this._theme === 'auto') {
      if (this._mediaQuery) {
        return this._mediaQuery.matches ? 'dark' : 'light';
      }
      return 'light'; // fallback
    }
    return this._theme;
  }

  private broadcastThemeEvent(theme: Theme) {
    const message = {
      type: 'theme-change',
      payload: { theme },
      timestamp: Date.now(),
      tabId: this._currentTabId,
    };

    // ส่งผ่าน BroadcastChannel
    if (this._broadcastChannel) {
      try {
        this._broadcastChannel.postMessage(message);
      }
      catch (error) {
        console.error('Failed to send theme message via BroadcastChannel:', error);
      }
    }

    // ส่งผ่าน localStorage (fallback)
    if (browser) {
      try {
        localStorage.setItem('theme-event', JSON.stringify(message));
        setTimeout(() => {
          localStorage.removeItem('theme-event');
        }, 100);
      }
      catch (error) {
        console.error('Failed to send theme message via localStorage:', error);
      }
    }
  }

  private loadTheme() {
    if (browser) {
      try {
        const savedTheme = localStorage.getItem('theme') as Theme;

        if (savedTheme && this.isValidTheme(savedTheme)) {
          this.setTheme(savedTheme, false);

          logger.info(
            LogCategory.SYSTEM,
            'theme_loaded_from_storage',
            'Theme loaded from localStorage',
            {
              savedTheme,
              tabId: this._currentTabId,
            },
          );
        }
        else {
          // ตรวจสอบ system preference
          const systemTheme = this.detectSystemTheme();
          this.setTheme(systemTheme, false);

          logger.info(
            LogCategory.SYSTEM,
            'theme_detected_from_system',
            'Theme detected from system preference',
            {
              systemTheme,
              tabId: this._currentTabId,
            },
          );
        }
      }
      catch (error) {
        logger.error(LogCategory.SYSTEM, 'theme_load_error', 'Failed to load theme', {
          error: error instanceof Error ? error.message : 'Unknown error',
          tabId: this._currentTabId,
        });

        // Fallback to light theme
        this.setTheme('light', false);
      }
    }
  }

  private isValidTheme(theme: string): theme is Theme {
    return ['light', 'dark', 'auto'].includes(theme);
  }

  private detectSystemTheme(): Theme {
    if (!browser || !window.matchMedia) return 'light';

    try {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      return prefersDark ? 'dark' : 'light';
    }
    catch (error) {
      logger.warn(
        LogCategory.SYSTEM,
        'system_theme_detection_failed',
        'Failed to detect system theme preference',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      );
      return 'light';
    }
  }

  setTheme(theme: Theme, broadcast: boolean = true) {
    const previousTheme = this._theme;
    this._theme = theme;

    if (browser) {
      try {
        // คำนวณ theme ที่จะใช้จริง
        const effectiveTheme = this.getEffectiveTheme();

        // Apply theme to DOM
        this.applyTheme(effectiveTheme);

        // Save to localStorage
        localStorage.setItem('theme', theme);

        logger.info(LogCategory.SYSTEM, 'theme_changed', 'Theme changed successfully', {
          previousTheme,
          newTheme: theme,
          effectiveTheme,
          broadcast,
          tabId: this._currentTabId,
        });

        if (broadcast) {
          this.broadcastThemeEvent(theme);

          logger.info(
            LogCategory.SYSTEM,
            'theme_broadcast_sent',
            'Theme change broadcasted to other tabs',
            {
              theme,
              tabId: this._currentTabId,
            },
          );
        }
      }
      catch (error) {
        logger.error(LogCategory.SYSTEM, 'theme_change_error', 'Failed to change theme', {
          theme,
          error: error instanceof Error ? error.message : 'Unknown error',
          tabId: this._currentTabId,
        });
      }
    }
  }

  toggleTheme() {
    let newTheme: Theme;

    // Cycle through: light -> dark -> auto -> light
    switch (this._theme) {
      case 'light':
        newTheme = 'dark';
        break;
      case 'dark':
        newTheme = 'auto';
        break;
      case 'auto':
        newTheme = 'light';
        break;
      default:
        newTheme = 'light';
    }

    logger.info(LogCategory.SYSTEM, 'theme_toggled', 'Theme toggled by user', {
      previousTheme: this._theme,
      newTheme,
      tabId: this._currentTabId,
    });

    this.setTheme(newTheme);
  }

  // Get current effective theme (useful for UI)
  getEffectiveThemeValue(): 'light' | 'dark' {
    return this.getEffectiveTheme();
  }

  // Check if system prefers dark mode
  get systemPrefersDark(): boolean {
    return this._mediaQuery?.matches ?? false;
  }

  destroy() {
    try {
      if (this._storageListener) {
        window.removeEventListener('storage', this._storageListener);
      }

      if (this._mediaQueryListener && this._mediaQuery) {
        this._mediaQuery.removeEventListener('change', this._mediaQueryListener);
      }

      // Note: BroadcastChannel will be closed automatically when tab closes

      logger.info(LogCategory.SYSTEM, 'theme_store_destroyed', 'Theme store destroyed', {
        tabId: this._currentTabId,
      });
    }
    catch (error) {
      logger.error(
        LogCategory.SYSTEM,
        'theme_store_destroy_error',
        'Error during theme store destruction',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          tabId: this._currentTabId,
        },
      );
    }
  }
}

export const themeStore = new ThemeStore();
