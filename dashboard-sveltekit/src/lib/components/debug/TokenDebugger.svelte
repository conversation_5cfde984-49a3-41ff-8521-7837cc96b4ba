<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { getAuthToken, debugTokenInfo, saveTokenToCookies, saveTokenToLocalStorage, clearAllTokens } from '$lib/utils/token-helper';
	import { authStore } from '$lib/stores/auth.svelte';
	import { notificationStore } from '$lib/stores/notification.svelte';

	let tokenInfo = $state({ token: null, source: 'none', isValid: false });
	let manualToken = $state('');
	let debugLogs = $state<string[]>([]);
	let showDebugger = $state(false);

	function addLog(message: string, type: 'info' | 'success' | 'error' = 'info') {
		const timestamp = new Date().toLocaleTimeString();
		const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
		debugLogs = [...debugLogs, logEntry];
	}

	function refreshTokenInfo() {
		if (!browser) return;
		tokenInfo = getAuthToken();
		debugTokenInfo();
		addLog(`Token refreshed: ${tokenInfo.source} - ${tokenInfo.isValid ? 'Valid' : 'Invalid'}`);
	}

	function testNotificationAPI() {
		addLog('Testing notification API...');
		notificationStore.loadUserNotifications({ limit: 5 });
		
		// Check for errors after a delay
		setTimeout(() => {
			if (notificationStore.error) {
				addLog(`API Error: ${notificationStore.error}`, 'error');
			} else {
				addLog(`API Success: ${notificationStore.notifications.length} notifications loaded`, 'success');
			}
		}, 2000);
	}

	function saveManualToken() {
		if (!manualToken) {
			addLog('Please enter a token', 'error');
			return;
		}

		saveTokenToCookies(manualToken);
		saveTokenToLocalStorage(manualToken);
		addLog('Token saved to both cookies and localStorage', 'success');
		refreshTokenInfo();
	}

	function clearTokens() {
		clearAllTokens();
		addLog('All tokens cleared', 'info');
		refreshTokenInfo();
	}

	function clearLogs() {
		debugLogs = [];
	}

	onMount(() => {
		refreshTokenInfo();
		addLog('Token Debugger initialized');
	});
</script>

{#if browser}
	<!-- Debug Toggle Button -->
	<div class="fixed bottom-4 right-4 z-50">
		<button
			onclick={() => showDebugger = !showDebugger}
			class="btn btn-circle btn-primary btn-sm"
			title="Token Debugger"
		>
			🔐
		</button>
	</div>

	<!-- Debug Panel -->
	{#if showDebugger}
		<div class="fixed inset-0 bg-black bg-opacity-50 z-40" onclick={() => showDebugger = false}></div>
		<div class="fixed top-4 right-4 w-96 bg-base-100 rounded-lg shadow-xl z-50 p-4 max-h-96 overflow-y-auto">
			<div class="flex justify-between items-center mb-4">
				<h3 class="font-bold text-lg">🔐 Token Debugger</h3>
				<button onclick={() => showDebugger = false} class="btn btn-ghost btn-xs">✕</button>
			</div>

			<!-- Token Status -->
			<div class="mb-4 p-3 rounded {tokenInfo.token ? 'bg-success text-success-content' : 'bg-error text-error-content'}">
				<div class="text-sm">
					<div><strong>Status:</strong> {tokenInfo.token ? 'Found' : 'Not Found'}</div>
					<div><strong>Source:</strong> {tokenInfo.source}</div>
					<div><strong>Valid:</strong> {tokenInfo.isValid ? 'Yes' : 'No'}</div>
					<div><strong>Length:</strong> {tokenInfo.token?.length || 0}</div>
					<div><strong>Auth Store:</strong> {authStore.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</div>
				</div>
			</div>

			<!-- Actions -->
			<div class="space-y-2 mb-4">
				<button onclick={refreshTokenInfo} class="btn btn-primary btn-sm w-full">
					🔄 Refresh Token Info
				</button>
				<button onclick={testNotificationAPI} class="btn btn-secondary btn-sm w-full">
					🔔 Test Notification API
				</button>
				<button onclick={clearTokens} class="btn btn-error btn-sm w-full">
					🗑️ Clear All Tokens
				</button>
			</div>

			<!-- Manual Token Input -->
			<div class="mb-4">
				<label class="label">
					<span class="label-text">Manual Token:</span>
				</label>
				<input
					bind:value={manualToken}
					type="text"
					placeholder="Paste token here..."
					class="input input-bordered input-sm w-full"
				/>
				<button onclick={saveManualToken} class="btn btn-accent btn-sm w-full mt-2">
					💾 Save Token
				</button>
			</div>

			<!-- Debug Logs -->
			<div class="mb-4">
				<div class="flex justify-between items-center mb-2">
					<span class="label-text">Debug Logs:</span>
					<button onclick={clearLogs} class="btn btn-ghost btn-xs">Clear</button>
				</div>
				<div class="bg-base-200 p-2 rounded text-xs max-h-32 overflow-y-auto">
					{#each debugLogs as log}
						<div class="mb-1 {log.includes('ERROR') ? 'text-error' : log.includes('SUCCESS') ? 'text-success' : ''}">{log}</div>
					{/each}
				</div>
			</div>
		</div>
	{/if}
{/if}

<style>
	/* Custom scrollbar for debug panel */
	.overflow-y-auto::-webkit-scrollbar {
		width: 4px;
	}
	.overflow-y-auto::-webkit-scrollbar-track {
		background: transparent;
	}
	.overflow-y-auto::-webkit-scrollbar-thumb {
		background: hsl(var(--bc) / 0.2);
		border-radius: 2px;
	}
</style>