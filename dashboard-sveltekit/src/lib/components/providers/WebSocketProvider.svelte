<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { websocketStore } from '$lib/stores/websocket.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';

	let intervalId: ReturnType<typeof setInterval>;

	onMount(() => {
		if (!browser) return;

		console.log('🚀 WebSocketProvider mounted');

		let lastState = { isAuthenticated: false, connected: false, connecting: false, hasError: false };

		// ตรวจสอบสถานะและเชื่อมต่อ WebSocket
		function checkAndConnect() {
			const isAuthenticated = authStore.isAuthenticated;
			const connected = websocketStore.connected;
			const connecting = websocketStore.connecting;
			const error = websocketStore.error;

			const currentState = {
				isAuthenticated,
				connected,
				connecting,
				hasError: !!error
			};

			// Log เฉพาะเมื่อสถานะเปลี่ยนแปลง
			const stateChanged = JSON.stringify(currentState) !== JSON.stringify(lastState);
			if (stateChanged) {
				console.log('🔍 WebSocket state changed:', currentState);
				lastState = currentState;
			}

			// เชื่อมต่อเมื่อ authenticated และยังไม่ได้เชื่อมต่อ และไม่ได้กำลังเชื่อมต่ออยู่
			if (isAuthenticated && !connected && !connecting) {
				console.log('🔌 Auto-connecting WebSocket...');
				websocketStore.connect();
			} else if (!isAuthenticated && connected) {
				console.log('🔌 Auto-disconnecting WebSocket...');
				websocketStore.disconnect();
			} else if (isAuthenticated && error && !connecting) {
				console.log('🔄 Retrying WebSocket connection after error...');
				websocketStore.connect();
			}
		}

		// เชื่อมต่อทันทีหลังจาก mount
		checkAndConnect();
		
		// ตรวจสอบทุก 10 วินาที (ลดความถี่เพื่อลด spam)
		intervalId = setInterval(checkAndConnect, 10000);
	});

	onDestroy(() => {
		if (intervalId) {
			clearInterval(intervalId);
		}
	});
</script>

<!-- Slot สำหรับ render children -->
<slot /> 