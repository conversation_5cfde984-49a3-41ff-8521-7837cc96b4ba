<script lang="ts">
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { authStore } from '$lib/stores/auth.svelte';
	import { LogCategory, logger } from '$lib/utils/logger';

	// ✅ Client state management
	let isDropdownOpen = $state(false);

	// ✅ Toggle dropdown
	function toggleDropdown() {
		isDropdownOpen = !isDropdownOpen;
	}

	// ✅ Close dropdown
	function closeDropdown() {
		isDropdownOpen = false;
	}

	// ✅ Handle signout
	async function handleSignout() {
		try {
			await authStore.signout();
			// Redirect จะทำโดย authStore
		} catch (error) {
			logger.error(LogCategory.AUTH, 'signout_error', 'Error during signout', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			// Force redirect to signin
			if (browser) {
				window.location.href = '/signin?signout=error';
			}
		}
	}

	// ✅ Handle profile click
	function handleProfileClick() {
		closeDropdown();
		goto('/dashboard/profile');
	}

	// ✅ Handle wallet click
	function handleWalletClick() {
		closeDropdown();
		goto('/dashboard/topup');
	}
</script>

<div class="relative">
	<!-- User Avatar Button -->
	<button
		type="button"
		onclick={toggleDropdown}
		class="flex items-center gap-2 p-2 rounded-lg hover:bg-base-200 transition-colors"
		aria-expanded={isDropdownOpen}
		aria-haspopup="true"
	>
		<div class="avatar">
			<div class="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
				{#if browser && authStore.user?.avatar}
					<img src={authStore.user.avatar} alt="Avatar" class="w-8 h-8 rounded-full" />
				{:else}
					<Icon icon="solar:user-circle-line-duotone" class="w-5 h-5" />
				{/if}
			</div>
		</div>
		<span class="hidden sm:block text-sm font-medium">
			{browser && authStore.user?.firstName ? authStore.user.firstName : browser && authStore.user?.email ? authStore.user.email.split('@')[0] : 'User'}
		</span>
		<Icon icon="solar:alt-arrow-down-line-duotone" class="w-4 h-4" />
	</button>

	<!-- Dropdown Menu -->
	{#if browser && isDropdownOpen}
		<div
			class="absolute right-0 mt-2 w-56 bg-base-100 rounded-lg shadow-lg border border-base-300 z-50"
			role="menu"
			aria-orientation="vertical"
			aria-labelledby="user-menu-button"
		>
			<div class="py-1">
				<!-- User Info -->
				<div class="px-4 py-2 border-b border-base-300">
					<p class="text-sm font-medium text-base-content">
						{browser && authStore.user?.firstName ? authStore.user.firstName : ''} {browser && authStore.user?.lastName ? authStore.user.lastName : ''}
					</p>
					<p class="text-xs text-base-content/60">{browser && authStore.user?.email ? authStore.user.email : ''}</p>
				</div>

				<!-- Menu Items -->
				<button
					type="button"
					onclick={handleProfileClick}
					class="w-full text-left px-4 py-2 text-sm hover:bg-base-200 flex items-center gap-2"
					role="menuitem"
				>
					<Icon icon="solar:user-circle-line-duotone" class="size-5" />
					{browser ? $t('auth.profile') : 'โปรไฟล์'}
				</button>

				<button
					type="button"
					onclick={handleWalletClick}
					class="w-full text-left px-4 py-2 text-sm hover:bg-base-200 flex items-center gap-2"
					role="menuitem"
				>
					<Icon icon="solar:wallet-money-line-duotone" class="size-6 text-success" />
					{browser ? $t('auth.wallet') : 'กระเป๋าเงิน'}
				</button>

				<hr class="my-1 border-base-300" />

				<button
					type="button"
					onclick={handleSignout}
					class="w-full text-left px-4 py-2 text-sm hover:bg-base-200 flex items-center gap-2 text-error"
					role="menuitem"
				>
					<Icon icon="solar:logout-3-line-duotone" class="size-6 text-error" />
					{browser ? $t('auth.signout') : 'ออกจากระบบ'}
				</button>
			</div>
		</div>
	{/if}

	<!-- Backdrop -->
	{#if browser && isDropdownOpen}
		<div
			class="fixed inset-0 z-40"
			onclick={closeDropdown}
			aria-hidden="true"
		></div>
	{/if}
</div>
