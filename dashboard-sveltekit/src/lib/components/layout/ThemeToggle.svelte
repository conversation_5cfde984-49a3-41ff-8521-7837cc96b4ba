<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { themeStore } from '$lib/stores/theme.svelte';
	import { onMount } from 'svelte';

	// ✅ Get current theme
	const currentTheme = $derived(themeStore.theme);
	const effectiveTheme = $derived(themeStore.getEffectiveThemeValue());
	let isHydrated = $state(false);

	onMount(() => {
		isHydrated = true;
	});

	// ✅ Get tooltip text based on current theme
	function getTooltipText() {
		if (!isHydrated) return 'เปลี่ยนธีม';

		switch (currentTheme) {
			case 'auto':
				return $t('theme.switchToAuto') || 'เปลี่ยนเป็นโหมดอัตโนมัติ';
			case 'dark':
				return $t('theme.switchToDark') || 'เปลี่ยนเป็นโหมดมืด';
			case 'light':
				return $t('theme.switchToLight') || 'เปลี่ยนเป็นโหมดสว่าง';
			default:
				return $t('theme.switchTheme') || 'เปลี่ยนธีม';
		}
	}

	// ✅ Get aria label
	function getAriaLabel() {
		if (!isHydrated) return 'ธีมปัจจุบัน: สว่าง';

		switch (currentTheme) {
			case 'dark':
				return `${$t('theme.currentTheme') || 'ธีมปัจจุบัน'}: ${$t('theme.dark') || 'มืด'}`;
			case 'light':
				return `${$t('theme.currentTheme') || 'ธีมปัจจุบัน'}: ${$t('theme.light') || 'สว่าง'}`;
			case 'auto':
				return `${$t('theme.currentTheme') || 'ธีมปัจจุบัน'}: ${$t('theme.auto') || 'อัตโนมัติ'} (${effectiveTheme === 'dark' ? ($t('theme.dark') || 'มืด') : ($t('theme.light') || 'สว่าง')})`;
			default:
				return $t('theme.toggleTheme') || 'เปลี่ยนธีม';
		}
	}

	// ✅ Toggle theme
	function toggleTheme() {
		themeStore.toggleTheme();
	}
</script>

<button
	type="button"
	onclick={toggleTheme}
	class="btn btn-ghost btn-circle"
	title={getTooltipText()}
	aria-label={getAriaLabel()}
>
	{#if isHydrated}
		{#if currentTheme === 'dark'}
			<Icon icon="solar:moon-bold" class="size-7" />
		{:else if currentTheme === 'light'}
			<Icon icon="solar:sun-bold" class="size-7" />
		{:else}
			<Icon icon="solar:magic-stick-bold" class="size-7" />
		{/if}
	{:else}
		<!-- Server-side fallback - ใช้ icon เดียวกันเพื่อป้องกัน hydration mismatch -->
		<Icon icon="solar:sun-bold" class="size-7" />
	{/if}
</button>
