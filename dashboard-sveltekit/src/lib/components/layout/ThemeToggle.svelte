<script lang="ts">
	import { browser } from '$app/environment';
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { themeStore } from '$lib/stores/theme.svelte';

	// ✅ Get current theme
	const currentTheme = $derived(themeStore.theme);
	const effectiveTheme = $derived(themeStore.getEffectiveThemeValue());

	// ✅ Get tooltip text based on current theme
	function getTooltipText() {
		if (!browser) return 'เปลี่ยนธีม';

		switch (currentTheme) {
			case 'auto':
				return browser ? $t('theme.switchToAuto') || 'เปลี่ยนเป็นโหมดอัตโนมัติ' : 'เปลี่ยนเป็นโหมดอัตโนมัติ';
			case 'dark':
				return browser ? $t('theme.switchToDark') || 'เปลี่ยนเป็นโหมดมืด' : 'เปลี่ยนเป็นโหมดมืด';
			case 'light':
				return browser ? $t('theme.switchToLight') || 'เปลี่ยนเป็นโหมดสว่าง' : 'เปลี่ยนเป็นโหมดสว่าง';
			default:
				return browser ? $t('theme.switchTheme') || 'เปลี่ยนธีม' : 'เปลี่ยนธีม';
		}
	}

	// ✅ Get aria label
	function getAriaLabel() {
		if (!browser) return 'ธีมปัจจุบัน: สว่าง';

		switch (currentTheme) {
			case 'dark':
				return `${browser ? $t('theme.currentTheme') || 'ธีมปัจจุบัน' : 'ธีมปัจจุบัน'}: ${browser ? $t('theme.dark') || 'มืด' : 'มืด'}`;
			case 'light':
				return `${browser ? $t('theme.currentTheme') || 'ธีมปัจจุบัน' : 'ธีมปัจจุบัน'}: ${browser ? $t('theme.light') || 'สว่าง' : 'สว่าง'}`;
			case 'auto':
				return `${browser ? $t('theme.currentTheme') || 'ธีมปัจจุบัน' : 'ธีมปัจจุบัน'}: ${browser ? $t('theme.auto') || 'อัตโนมัติ' : 'อัตโนมัติ'} (${effectiveTheme === 'dark' ? (browser ? $t('theme.dark') || 'มืด' : 'มืด') : (browser ? $t('theme.light') || 'สว่าง' : 'สว่าง')})`;
			default:
				return browser ? $t('theme.toggleTheme') || 'เปลี่ยนธีม' : 'เปลี่ยนธีม';
		}
	}

	// ✅ Toggle theme
	function toggleTheme() {
		themeStore.toggleTheme();
	}
</script>

<button
	type="button"
	onclick={toggleTheme}
	class="btn btn-ghost btn-circle"
	title={getTooltipText()}
	aria-label={getAriaLabel()}
>
	{#if browser}
		{#if currentTheme === 'dark'}
			<Icon icon="solar:moon-bold" class="size-7" />
		{:else if currentTheme === 'light'}
			<Icon icon="solar:sun-bold" class="size-7" />
		{:else}
			<Icon icon="solar:magic-stick-bold" class="size-7" />
		{/if}
	{:else}
		<!-- Server-side fallback - ใช้ icon เดียวกันเพื่อป้องกัน hydration mismatch -->
		<Icon icon="solar:sun-bold" class="size-7" />
	{/if}
</button>
