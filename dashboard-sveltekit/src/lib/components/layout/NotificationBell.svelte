<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onDestroy, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { notificationStore } from '$lib/stores/notification.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';
	import { websocketStore } from '$lib/stores/websocket.svelte';
	import NotificationDropdown from './NotificationDropdown.svelte';

	let showDropdown = $state(false);
	let bellRef = $state<HTMLElement>();
	let dropdownRef = $state<HTMLElement>();
	let intervalId: ReturnType<typeof setInterval>; 


	// Reactive values
	const unreadCount = $derived(notificationStore.unreadCount);
	const isLoading = $derived(notificationStore.isLoading);
	const isAuthenticated = $derived(authStore.isAuthenticated);
	const wsConnected = $derived(websocketStore.connected);
	const wsError = $derived(websocketStore.error);

	onMount(() => {
		if (!browser) return;

		// โหลด unread count เมื่อเริ่มต้น (ถ้ามี authentication)
		if (isAuthenticated) {
			// ใช้การแจ้งเตือนส่วนตัว (รวมทั้ง site และ personal)
			notificationStore.loadUserUnreadCount();

			// อัปเดตทุก 30 วินาที (fallback ถ้า WebSocket ไม่ทำงาน)
			intervalId = setInterval(() => {
				if (isAuthenticated && !wsConnected) {
					notificationStore.loadUserUnreadCount();
				}
			}, 30000);
		}

		// ปิด dropdown เมื่อคลิกข้างนอก
		function handleClickOutside(event: MouseEvent) {
			if (
				showDropdown &&
				bellRef &&
				dropdownRef &&
				!bellRef.contains(event.target as Node) &&
				!dropdownRef.contains(event.target as Node)
			) {
				showDropdown = false;
			}
		}

		document.addEventListener('click', handleClickOutside);

		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});

	onDestroy(() => {
		if (intervalId) {
			clearInterval(intervalId);
		}
	});

	function toggleDropdown() {
		if (!isAuthenticated) {
			return;
		}

		showDropdown = !showDropdown;

		// โหลดการแจ้งเตือนส่วนตัวเมื่อเปิด dropdown
		if (showDropdown) {
			notificationStore.loadUserNotifications({ limit: 10 });
		}
	}

	function handleMarkAllAsRead() {
		notificationStore.markAllAsRead();
	}
</script>

<div class="relative">
	<!-- Notification Bell Button -->
	<button
		bind:this={bellRef}
		onclick={toggleDropdown}
		class="btn btn-ghost btn-circle relative notification-bell transition-smooth hover-lift"
		class:loading={isLoading}
		class:has-notifications={unreadCount > 0}
		class:ws-connected={wsConnected}
		class:ws-error={wsError}
		disabled={isLoading}
	>
		{#if isLoading}
			<span class="loading loading-spinner loading-sm"></span>
		{:else}
			<Icon icon="solar:bell-bold" class="size-6 transition-smooth" />

			<!-- Badge สำหรับ unread count -->
			{#if unreadCount > 0}
				<div
					class="badge badge-error badge-sm absolute -top-1 -right-1 min-w-5 h-5 animate-fade-in"
				>
					{unreadCount > 99 ? '99+' : unreadCount}
				</div>
			{/if}

			<!-- WebSocket status indicator -->
			{#if wsConnected}
				<div class="absolute -bottom-1 -right-1 w-2 h-2 bg-success rounded-full animate-pulse" title="WebSocket เชื่อมต่อแล้ว"></div>
			{:else if wsError}
				<div class="absolute -bottom-1 -right-1 w-2 h-2 bg-error rounded-full" title="WebSocket เกิดข้อผิดพลาด"></div>
			{:else}
				<div class="absolute -bottom-1 -right-1 w-2 h-2 bg-warning rounded-full animate-pulse" title="กำลังเชื่อมต่อ WebSocket..."></div>
			{/if}
		{/if}
	</button>

	<!-- Dropdown -->
	{#if showDropdown}
		<div bind:this={dropdownRef} class="absolute right-0 top-full mt-2 z-50 animate-fade-in">
			<NotificationDropdown
				onClose={() => (showDropdown = false)}
				onMarkAllAsRead={handleMarkAllAsRead}
			/>
		</div>
	{/if}
</div>

<style>
	.badge {
		font-size: 0.7rem;
		line-height: 1;
		padding: 0.1rem 0.3rem;
	}
</style>
