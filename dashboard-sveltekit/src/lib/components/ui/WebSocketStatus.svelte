<script lang="ts">
	import { websocketStore } from '$lib/stores/websocket.svelte';

	// Reactive values
	const connected = $derived(websocketStore.connected);
	const error = $derived(websocketStore.error);

	function reconnect() {
		websocketStore.connect();
	}
</script>

<div class="flex items-center gap-2 text-sm">
	<!-- Status indicator -->
	<div class="flex items-center gap-1">
		<div
			class="w-2 h-2 rounded-full transition-colors"
			class:bg-success={connected}
			class:bg-error={error}
			class:bg-warning={!connected && !error}
		></div>
		<span class="font-medium">
			{#if connected}
				เชื่อมต่อแล้ว
			{:else if error}
				เกิดข้อผิดพลาด
			{:else}
				กำลังเชื่อมต่อ...
			{/if}
		</span>
	</div>

	<!-- Error message -->
	{#if error}
		<div class="text-error text-xs">{error}</div>
	{/if}

	<!-- Reconnect button -->
	{#if error}
		<button
			onclick={reconnect}
			class="btn btn-xs btn-outline btn-error"
			title="ลองเชื่อมต่อใหม่"
		>
			ลองใหม่
		</button>
	{/if}
</div> 