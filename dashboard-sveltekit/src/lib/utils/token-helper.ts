/**
 * Token Helper Utilities
 * ช่วยจัดการ token ใน SvelteKit Dashboard
 */

import { browser } from '$app/environment';

export interface TokenInfo {
  token: string | null;
  source: 'cookies' | 'localStorage' | 'none';
  isValid: boolean;
}

/**
 * ดึง auth token จากหลายแหล่ง
 */
export function getAuthToken(): TokenInfo {
  if (!browser) {
    return { token: null, source: 'none', isValid: false };
  }

  // Method 1: จาก cookies
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    if (key && value) {
      acc[key] = decodeURIComponent(value);
    }
    return acc;
  }, {} as Record<string, string>);

  let token = cookies['auth_token'] || cookies['accessToken'];
  if (token) {
    return { token, source: 'cookies', isValid: isTokenValid(token) };
  }

  // Method 2: จาก localStorage
  try {
    token = localStorage.getItem('auth_token') || localStorage.getItem('accessToken');
    if (token) {
      return { token, source: 'localStorage', isValid: isTokenValid(token) };
    }
  }
  catch (e) {
    // Ignore localStorage errors
  }

  return { token: null, source: 'none', isValid: false };
}

/**
 * ตรวจสอบว่า token ยังใช้งานได้หรือไม่
 */
export function isTokenValid(token: string | null): boolean {
  if (!token) return false;

  try {
    // ตรวจสอบ JWT format
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // ตรวจสอบ payload
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);

    // ตรวจสอบ expiration
    if (payload.exp && payload.exp < now) {
      console.warn('🔐 Token expired');
      return false;
    }

    return true;
  }
  catch (error) {
    console.warn('🔐 Invalid token format:', error);
    return false;
  }
}

/**
 * บันทึก token ลง cookies
 */
export function saveTokenToCookies(token: string): void {
  if (!browser) return;

  const maxAge = 60 * 60 * 24 * 7; // 7 days
  document.cookie = `auth_token=${token}; path=/; max-age=${maxAge}; secure; samesite=strict`;
  console.log('🔐 Token saved to cookies');
}

/**
 * บันทึก token ลง localStorage
 */
export function saveTokenToLocalStorage(token: string): void {
  if (!browser) return;

  try {
    localStorage.setItem('auth_token', token);
    console.log('🔐 Token saved to localStorage');
  }
  catch (e) {
    console.warn('🔐 Failed to save token to localStorage');
  }
}

/**
 * ลบ token ทั้งหมด
 */
export function clearAllTokens(): void {
  if (!browser) return;

  // Clear cookies
  document.cookie = 'auth_token=; path=/; max-age=0';
  document.cookie = 'accessToken=; path=/; max-age=0';

  // Clear localStorage
  try {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('accessToken');
  }
  catch (e) {
    // Ignore errors
  }

  console.log('🔐 All tokens cleared');
}

/**
 * Debug token information
 */
export function debugTokenInfo(): void {
  if (!browser) return;

  const tokenInfo = getAuthToken();
  console.log('🔐 Token Debug Info:', {
    hasToken: !!tokenInfo.token,
    source: tokenInfo.source,
    isValid: tokenInfo.isValid,
    tokenLength: tokenInfo.token?.length || 0,
    tokenPreview: tokenInfo.token?.substring(0, 20) + '...',
  });
}
