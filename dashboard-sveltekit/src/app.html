<!doctype html>
<html lang="th" %sveltekit.theme%>
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		

		<!-- Theme script - ป้องกันการกระพริบ -->
		<script>
			(function () {
				try {
					const savedTheme = localStorage.getItem('theme');
					let effectiveTheme = 'light'; // default fallback

					if (savedTheme) {
						if (savedTheme === 'auto') {
							// ตรวจสอบ system preference สำหรับ auto mode
							if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
								effectiveTheme = 'dark';
							} else {
								effectiveTheme = 'light';
							}
						} else if (savedTheme === 'dark' || savedTheme === 'light') {
							effectiveTheme = savedTheme;
						}
					} else {
						// ไม่มี saved theme ให้ตรวจสอบ system preference
						if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
							effectiveTheme = 'dark';
						}
					}

					document.documentElement.setAttribute('data-theme', effectiveTheme);
				} catch (error) {
					// Fallback ในกรณีที่เกิด error
					document.documentElement.setAttribute('data-theme', 'light');
				}
			})();
		</script>
		
		<!-- ✅ Override CSP for WebSocket support in development -->
		<meta http-equiv="Content-Security-Policy" 
		      content="default-src 'self'; 
		               script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://www.google.com https://www.gstatic.com; 
		               style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://www.gstatic.com; 
		               font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net https://www.gstatic.com; 
		               img-src 'self' data: https: blob: https://www.google.com https://www.gstatic.com; 
		               connect-src 'self' https://api.cloudinary.com https://api.iconify.design https://api.unisvg.com https://api.simplesvg.com https://www.google.com ws://localhost:* wss://localhost:* ws: wss: http://localhost:* https://localhost:*; 
		               frame-src 'self' https://www.google.com https://www.gstatic.com; 
		               media-src 'self' https:; 
		               object-src 'none'; 
		               base-uri 'self'; 
		               form-action 'self';">
		
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover" %sveltekit.theme%>
		<div style="display: contents" %sveltekit.body%>%sveltekit.body%</div>
	</body>
</html>