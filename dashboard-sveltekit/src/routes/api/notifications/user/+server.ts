import { notificationService } from '$lib/services/notification';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals, url }) => {
  try {
    if (!locals.token || !locals.user) {
      return json(
        { success: false, error: 'กรุณาเข้าสู่ระบบ' },
        { status: 401 },
      );
    }

    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const type = url.searchParams.get('type') || undefined;
    const status = url.searchParams.get('status') || undefined;

    const filters = {
      page,
      limit,
      type,
      status,
    };

    // Call service for business logic
    const result = await notificationService.getUserNotifications(filters, locals.token);

    if (!result.success) {
      return json(
        { success: false, error: result.error || 'เกิดข้อผิดพลาดในการโหลดการแจ้งเตือน' },
        { status: 400 },
      );
    }

    return json({
      success: true,
      data: result.data,
    });
  }
  catch (error) {
    console.error('User notifications API error:', error);
    return json(
      { success: false, error: 'เกิดข้อผิดพลาดในการเชื่อมต่อ' },
      { status: 500 },
    );
  }
};

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    if (!locals.token || !locals.user) {
      return json(
        { success: false, error: 'กรุณาเข้าสู่ระบบ' },
        { status: 401 },
      );
    }

    const formData = await request.formData();
    const action = formData.get('action') as string;

    switch (action) {
      case 'markAsRead': {
        const notificationIds = formData.getAll('notificationIds') as string[];

        if (notificationIds.length === 0) {
          return json(
            { success: false, error: 'กรุณาเลือกการแจ้งเตือนที่ต้องการทำเครื่องหมาย' },
            { status: 400 },
          );
        }

        const result = await notificationService.markAsRead(
          { notificationIds },
          locals.token,
        );

        if (!result.success) {
          return json(
            { success: false, error: result.error || 'เกิดข้อผิดพลาดในการทำเครื่องหมาย' },
            { status: 400 },
          );
        }

        return json({
          success: true,
          message: 'ทำเครื่องหมายว่าอ่านแล้วสำเร็จ',
        });
      }

      case 'markAllAsRead': {
        const result = await notificationService.markAllAsRead(locals.token);

        if (!result.success) {
          return json(
            { success: false, error: result.error || 'เกิดข้อผิดพลาดในการทำเครื่องหมายทั้งหมด' },
            { status: 400 },
          );
        }

        return json({
          success: true,
          message: 'ทำเครื่องหมายทั้งหมดว่าอ่านแล้วสำเร็จ',
        });
      }

      case 'delete': {
        const notificationId = formData.get('notificationId') as string;

        if (!notificationId) {
          return json(
            { success: false, error: 'กรุณาระบุ Notification ID' },
            { status: 400 },
          );
        }

        const result = await notificationService.deleteNotification(notificationId, locals.token);

        if (!result.success) {
          return json(
            { success: false, error: result.error || 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน' },
            { status: 400 },
          );
        }

        return json({
          success: true,
          message: 'ลบการแจ้งเตือนสำเร็จ',
        });
      }

      default:
        return json(
          { success: false, error: 'ไม่พบ action ที่ระบุ' },
          { status: 400 },
        );
    }
  }
  catch (error) {
    console.error('User notifications POST error:', error);
    return json(
      { success: false, error: 'เกิดข้อผิดพลาดในการเชื่อมต่อ' },
      { status: 500 },
    );
  }
};
