<script lang="ts">
	import '$lib/styles/app.css';
	import { onMount } from 'svelte';
	import { setInitialLocale } from '$lib/i18n';
	import { languageStore } from '$lib/stores/language.svelte';

	interface Props {
		children: any;
		data: { user?: any; initialLocale?: string };
	}

	const { children, data }: Props = $props();

	// ✅ Initialize on client side only after hydration
	onMount(() => {
		try {
			// ตั้งค่าภาษาเริ่มต้นจาก server ถ้ามี
			if (data?.initialLocale) {
				setInitialLocale(data.initialLocale);
				languageStore.setLanguage(data.initialLocale as any, false);
			}

			// ตั้งค่า user จาก SSR ถ้ามี
			// if (data?.user) {
			// 	authStore.setUserFromSSR(data.user);
			// }
		} catch (error) {
			console.error('Error initializing layout:', error);
		}
	});
</script>

<!-- ✅ Always render children to prevent hydration mismatch -->
{@render children()}
