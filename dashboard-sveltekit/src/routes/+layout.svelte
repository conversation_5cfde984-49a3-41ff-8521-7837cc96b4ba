<script lang="ts">
	import '$lib/styles/app.css';
	import { browser } from '$app/environment';
	import { onMount } from 'svelte';
	import { setInitialLocale, waitLocale } from '$lib/i18n';
	import { languageStore } from '$lib/stores/language.svelte';
	// import Icon from '@iconify/svelte';

	// import { authStore } from "$lib/stores/auth.svelte";

	interface Props {
		children: any;
		data: { user?: any; initialLocale?: string };
	}

	const { children, data }: Props = $props();
	let isLoading = $state(true);

	// ✅ Initialize on client side
	onMount(async () => {
		// ตั้งค่าภาษาเริ่มต้นจาก server ถ้ามี
		if (data?.initialLocale) {
			setInitialLocale(data.initialLocale);
			languageStore.setLanguage(data.initialLocale as any, false);
		}

		// รอให้ i18n โหลดเสร็จ
		await waitLocale();

		// ตั้งค่า user จาก SSR ถ้ามี
		// if (data?.user) {
		// 	authStore.setUserFromSSR(data.user);
		// }

		isLoading = false;
	});
</script>

{#if browser && isLoading}
	<div class="min-h-screen flex items-center justify-center">
		<span class="loading loading-spinner loading-xl sm:w-24 sm:h-24"></span>
	</div>
{:else}
	{@render children()}
{/if}
