<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	// ✅ Props from server
	const { data, form } = $props<{
		data: any;
		form?: any;
	}>();

	const siteId = $derived(page.params.siteId);

	// ✅ Client state management
	let isRentLoading = $state(false);
	let isCancelLoading = $state(false);

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			if (formResult.type === 'rent') {
				showSuccess('เช่าระบบเสริมสำเร็จ!', formResult.message);
			} else if (formResult.type === 'cancel') {
				showSuccess('ยกเลิกระบบเสริมสำเร็จ!', formResult.message);
			}
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleRentResult(result: any) {
		isRentLoading = false;

		if (result.type === 'success') {
			showSuccess('เช่าระบบเสริมสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('เช่าระบบเสริมไม่สำเร็จ', result.data?.message);
		}
	}

	function handleCancelResult(result: any) {
		isCancelLoading = false;

		if (result.type === 'success') {
			showSuccess('ยกเลิกระบบเสริมสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('ยกเลิกระบบเสริมไม่สำเร็จ', result.data?.message);
		}
	}

	// ✅ ใช้ข้อมูลจาก server ที่มีสถานะแล้ว
	const availableAddons = data.addons;

	// ✅ Helper functions สำหรับตรวจสอบสถานะ
	function isAddonActive(addon: any): boolean {
		return addon.isActive === true;
	}

	function isAddonPurchased(addon: any): boolean {
		return addon.isPurchased === true;
	}

	function canActivateAddon(addon: any): boolean {
		return addon.isPurchased === true && addon.isActive === false;
	}

	function canPurchaseAddon(addon: any): boolean {
		return addon.isPurchased !== true;
	}

	// ✅ เช่า addon ด้วย form action
	function rentAddon(addonId: string) {
		const form = document.getElementById(`rent-form-${addonId}`) as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	// ✅ เปิดใช้งาน addon ด้วย form action
	function activateAddon(addonId: string) {
		const form = document.getElementById(`activate-form-${addonId}`) as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	// ✅ ยกเลิก addon ด้วย form action
	function cancelAddon(addonId: string) {
		const form = document.getElementById(`cancel-form-${addonId}`) as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	// ✅ ไปยังหน้าจัดการ addon
	function goToAddon(addon: any) {
		if (isAddonActive(addon)) {
			goto(`/dashboard/${siteId}/contents/${addon.id === 'novel' ? 'novels' : addon.id}`);
		}
	}

	// ✅ Handle activate form result
	function handleActivateResult(result: any) {
		if (result.type === 'success') {
			showSuccess('เปิดใช้งานสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('เปิดใช้งานไม่สำเร็จ', result.data?.message);
		}
	}
</script>

<svelte:head>
	<title>ระบบเสริม - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-3xl font-bold">ระบบเสริม</h1>
			<p class="text-base-content/70 mt-2">จัดการระบบเสริมต่างๆ สำหรับเว็บไซต์ของคุณ</p>
		</div>
	</div>

	<!-- สถานะการสมัครใช้งาน -->
	{#if data.subscription}
		<div class="alert alert-info mb-6">
			<Icon icon="mdi:information" class="w-6 h-6" />
			<div>
				<h3 class="font-bold">แพ็กเกจปัจจุบัน: {data.subscription.planName}</h3>
				<div class="text-sm">วันหมดอายุ: {new Date(data.subscription.expiresAt).toLocaleDateString('th-TH')}</div>
			</div>
		</div>
	{/if}

	<!-- ✅ Hidden Forms for Hybrid Approach -->
	{#each availableAddons as addon}
		<!-- Rent Form -->
		<form
			id="rent-form-{addon.id}"
			method="POST"
			action="?/rentAddon"
			use:enhance={() => {
				isRentLoading = true;
				showLoading('กำลังเช่าระบบเสริม...');

				return async ({ result }) => {
					handleRentResult(result);
				};
			}}
			class="hidden"
		>
			<input type="hidden" name="addonId" value={addon.id} />
		</form>

		<!-- Activate Form -->
		<form
			id="activate-form-{addon.id}"
			method="POST"
			action="?/activateAddon"
			use:enhance={() => {
				showLoading('กำลังเปิดใช้งานระบบเสริม...');

				return async ({ result }) => {
					handleActivateResult(result);
				};
			}}
			class="hidden"
		>
			<input type="hidden" name="addonId" value={addon.id} />
		</form>

		<!-- Cancel Form -->
		<form
			id="cancel-form-{addon.id}"
			method="POST"
			action="?/cancelAddon"
			use:enhance={() => {
				isCancelLoading = true;
				showLoading('กำลังยกเลิกระบบเสริม...');

				return async ({ result }) => {
					handleCancelResult(result);
				};
			}}
			class="hidden"
		>
			<input type="hidden" name="addonId" value={addon.id} />
		</form>
	{/each}

	<!-- รายการ Addons -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		{#each availableAddons as addon}
			<div class="card bg-base-100 shadow-xl border">
				<div class="card-body">
					<div class="flex items-center gap-3 mb-4">
						<div class="p-3 bg-primary/10 rounded-lg">
							<Icon icon={addon.icon} class="w-8 h-8 text-primary" />
						</div>
						<div>
							<h2 class="card-title text-lg">{addon.name}</h2>
							<p class="text-sm text-base-content/70">{addon.description}</p>
						</div>
					</div>

					<!-- ฟีเจอร์ -->
					<div class="mb-4">
						<h4 class="font-semibold mb-2">ฟีเจอร์:</h4>
						<ul class="text-sm space-y-1">
							{#each addon.features as feature}
								<li class="flex items-center gap-2">
									<Icon icon="mdi:check" class="w-4 h-4 text-success" />
									{feature}
								</li>
							{/each}
						</ul>
					</div>

					<!-- ราคา -->
					<div class="flex items-center justify-between mb-4">
						<div class="text-2xl font-bold text-primary">
							฿{addon.price.toLocaleString()}
							<span class="text-sm font-normal text-base-content/70">/เดือน</span>
						</div>
					</div>

					<!-- สถานะและปุ่ม -->
					<div class="card-actions justify-end">
						{#if isAddonActive(addon)}
							<!-- ✅ กำลังใช้งาน: แสดงปุ่มจัดการและยกเลิก -->
							<div class="flex gap-2 w-full">
								<button 
									class="btn btn-success btn-sm flex-1"
									onclick={() => goToAddon(addon)}
								>
									<Icon icon="mdi:cog" class="w-4 h-4" />
									จัดการ
								</button>
								<button 
									class="btn btn-warning btn-sm"
									onclick={() => cancelAddon(addon.id)}
								>
									<Icon icon="mdi:pause" class="w-4 h-4" />
									ปิดใช้งาน
								</button>
							</div>
						{:else if canActivateAddon(addon)}
							<!-- ✅ ซื้อแล้วแต่ยังไม่เปิดใช้งาน: แสดงปุ่มเปิดใช้งาน -->
							<button 
								class="btn btn-primary btn-sm w-full"
								onclick={() => activateAddon(addon.id)}
							>
								<Icon icon="mdi:play" class="w-4 h-4" />
								เปิดใช้งาน
							</button>
						{:else if canPurchaseAddon(addon)}
							<!-- ✅ ยังไม่ได้ซื้อ: แสดงปุ่มซื้อ -->
							<button 
								class="btn btn-primary btn-sm w-full"
								onclick={() => rentAddon(addon.id)}
							>
								<Icon icon="mdi:cart-plus" class="w-4 h-4" />
								เช่าใช้งาน
							</button>
						{:else}
							<!-- ✅ ซื้อแล้วแต่ปิดใช้งาน: แสดงปุ่มเปิดใช้งาน (disabled ถ้าไม่สามารถเปิดได้) -->
							<button 
								class="btn btn-ghost btn-sm w-full"
								disabled
							>
								<Icon icon="mdi:lock" class="w-4 h-4" />
								ไม่สามารถใช้งานได้
							</button>
						{/if}
					</div>

					<!-- สถานะ Badge -->
					<div class="flex justify-center mt-2">
						{#if isAddonActive(addon)}
							<div class="badge badge-success">กำลังใช้งาน</div>
						{:else if isAddonPurchased(addon)}
							<div class="badge badge-warning">ซื้อแล้ว (ยังไม่เปิดใช้งาน)</div>
						{:else}
							<div class="badge badge-ghost">ยังไม่ได้ซื้อ</div>
						{/if}
					</div>
				</div>
			</div>
		{/each}
	</div>

	<!-- ข้อมูลเพิ่มเติม -->
	<div class="mt-8 p-6 bg-base-200 rounded-lg">
		<h3 class="text-xl font-bold mb-4">ข้อมูลสำคัญ</h3>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
			<div>
				<h4 class="font-semibold mb-2">การเช่าใช้งาน:</h4>
				<ul class="space-y-1 text-base-content/70">
					<li>• ระบบเสริมจะเรียกเก็บเงินรายเดือน</li>
					<li>• สามารถยกเลิกได้ทุกเมื่อ</li>
					<li>• ข้อมูลจะถูกเก็บไว้ 30 วันหลังยกเลิก</li>
				</ul>
			</div>
			<div>
				<h4 class="font-semibold mb-2">การใช้งาน:</h4>
				<ul class="space-y-1 text-base-content/70">
					<li>• เช่าแล้วต้องเปิดใช้งานก่อนใช้</li>
					<li>• สามารถปิด/เปิดใช้งานได้ตลอดเวลา</li>
					<li>• ระบบที่ปิดใช้งานจะไม่แสดงในเว็บไซต์</li>
				</ul>
			</div>
		</div>
	</div>
</div>