<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { enhance } from '$app/forms';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	// ✅ Props from server
	const { data, form } = $props<{
		data: any;
		form?: any;
	}>();

	const siteId = $derived(page.params.siteId);

	// ✅ Client state management
	let isDeleteLoading = $state(false);
	let isToggleLoading = $state(false);

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			if (formResult.type === 'delete') {
				showSuccess('ลบข่าวสำเร็จ!', formResult.message);
			} else if (formResult.type === 'toggle') {
				showSuccess('เปลี่ยนสถานะสำเร็จ!', formResult.message);
			}
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleDeleteResult(result: any) {
		isDeleteLoading = false;

		if (result.type === 'success') {
			showSuccess('ลบข่าวสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('ลบข่าวไม่สำเร็จ', result.data?.message);
		}
	}

	function handleToggleResult(result: any) {
		isToggleLoading = false;

		if (result.type === 'success') {
			showSuccess('เปลี่ยนสถานะสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('เปลี่ยนสถานะไม่สำเร็จ', result.data?.message);
		}
	}

	// ✅ ลบข่าวด้วย form action
	function deleteNews(newsId: string) {
		const form = document.getElementById(`delete-form-${newsId}`) as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	// ✅ เปลี่ยนสถานะการเผยแพร่ด้วย form action
	function togglePublish(newsId: string, currentStatus: boolean) {
		const form = document.getElementById(`toggle-form-${newsId}`) as HTMLFormElement;
		if (form) {
			// Update hidden input value
			const publishedInput = form.querySelector('input[name="published"]') as HTMLInputElement;
			if (publishedInput) {
				publishedInput.value = (!currentStatus).toString();
			}
			form.requestSubmit();
		}
	}
</script>

<svelte:head>
	<title>จัดการข่าวสาร - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-3">
				<Icon icon="mdi:newspaper" class="text-primary" />
				จัดการข่าวสาร
			</h1>
			<p class="text-base-content/70 mt-2">สร้างและจัดการข่าวสารของเว็บไซต์</p>
		</div>
		<div class="flex gap-2">
			<a href="/dashboard/{siteId}/contents/news/categories" class="btn btn-outline">
				<Icon icon="mdi:folder-multiple" class="w-4 h-4" />
				จัดการหมวดหมู่
			</a>
			<a href="/dashboard/{siteId}/contents/news/create" class="btn btn-primary">
				<Icon icon="mdi:plus" class="w-4 h-4" />
				สร้างข่าวใหม่
			</a>
		</div>
	</div>

	<!-- สถิติ -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-primary">
				<Icon icon="mdi:newspaper" class="w-8 h-8" />
			</div>
			<div class="stat-title">ข่าวทั้งหมด</div>
			<div class="stat-value text-primary">{data.news?.length || 0}</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-success">
				<Icon icon="mdi:check-circle" class="w-8 h-8" />
			</div>
			<div class="stat-title">เผยแพร่แล้ว</div>
			<div class="stat-value text-success">
				{data.news?.filter((n: any) => n.published).length || 0}
			</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-warning">
				<Icon icon="mdi:clock" class="w-8 h-8" />
			</div>
			<div class="stat-title">แบบร่าง</div>
			<div class="stat-value text-warning">
				{data.news?.filter((n: any) => !n.published).length || 0}
			</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-info">
				<Icon icon="mdi:folder" class="w-8 h-8" />
			</div>
			<div class="stat-title">หมวดหมู่</div>
			<div class="stat-value text-info">{data.categories?.length || 0}</div>
		</div>
	</div>

	<!-- ✅ Hidden Forms for Hybrid Approach -->
	{#if data.news && data.news.length > 0}
		{#each data.news as newsItem}
			<!-- Delete Form -->
			<form
				id="delete-form-{newsItem.id}"
				method="POST"
				action="?/deleteNews"
				use:enhance={() => {
					isDeleteLoading = true;
					showLoading('กำลังลบข่าว...');

					return async ({ result }) => {
						handleDeleteResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="newsId" value={newsItem.id} />
			</form>

			<!-- Toggle Publish Form -->
			<form
				id="toggle-form-{newsItem.id}"
				method="POST"
				action="?/togglePublish"
				use:enhance={() => {
					isToggleLoading = true;
					showLoading('กำลังเปลี่ยนสถานะ...');

					return async ({ result }) => {
						handleToggleResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="newsId" value={newsItem.id} />
				<input type="hidden" name="published" value={newsItem.published.toString()} />
			</form>
		{/each}
	{/if}

	<!-- ตารางข่าว -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<div class="overflow-x-auto">
				<table class="table table-zebra">
					<thead>
						<tr>
							<th>หัวข้อ</th>
							<th>หมวดหมู่</th>
							<th>สถานะ</th>
							<th>วันที่สร้าง</th>
							<th>การดำเนินการ</th>
						</tr>
					</thead>
					<tbody>
						{#if data.news && data.news.length > 0}
							{#each data.news as newsItem}
								<tr>
									<td>
										<div class="flex items-center gap-3">
											{#if newsItem.featuredImage}
												<div class="avatar">
													<div class="mask mask-squircle w-12 h-12">
														<img src={newsItem.featuredImage} alt={newsItem.title} />
													</div>
												</div>
											{/if}
											<div>
												<div class="font-bold">{newsItem.title}</div>
												<div class="text-sm opacity-50 truncate max-w-xs">
													{newsItem.excerpt || newsItem.content?.substring(0, 100) + '...'}
												</div>
											</div>
										</div>
									</td>
									<td>
										{#if newsItem.category}
											<div class="badge badge-outline">{newsItem.category.name}</div>
										{:else}
											<span class="text-base-content/50">ไม่มีหมวดหมู่</span>
										{/if}
									</td>
									<td>
										<div class="form-control">
											<label class="label cursor-pointer">
												<input 
													type="checkbox" 
													class="toggle toggle-success" 
													checked={newsItem.published}
													onchange={() => togglePublish(newsItem.id, newsItem.published)}
												/>
												<span class="label-text ml-2">
													{newsItem.published ? 'เผยแพร่' : 'แบบร่าง'}
												</span>
											</label>
										</div>
									</td>
									<td>{new Date(newsItem.createdAt).toLocaleDateString('th-TH')}</td>
									<td>
										<div class="flex gap-2">
											<a 
												href="/dashboard/{siteId}/contents/news/{newsItem.id}/edit"
												class="btn btn-ghost btn-sm"
											>
												<Icon icon="mdi:pencil" class="w-4 h-4" />
											</a>
											<button 
												class="btn btn-ghost btn-sm text-error"
												onclick={() => deleteNews(newsItem.id)}
											>
												<Icon icon="mdi:delete" class="w-4 h-4" />
											</button>
										</div>
									</td>
								</tr>
							{/each}
						{:else}
							<tr>
								<td colspan="5" class="text-center py-8">
									<div class="flex flex-col items-center gap-4">
										<Icon icon="mdi:newspaper-variant-outline" class="w-16 h-16 text-base-content/30" />
										<div>
											<h3 class="text-lg font-semibold">ยังไม่มีข่าวสาร</h3>
											<p class="text-base-content/70">เริ่มสร้างข่าวสารแรกของคุณ</p>
										</div>
										<a href="/dashboard/{siteId}/contents/news/create" class="btn btn-primary">
											<Icon icon="mdi:plus" class="w-4 h-4" />
											สร้างข่าวใหม่
										</a>
									</div>
								</td>
							</tr>
						{/if}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>