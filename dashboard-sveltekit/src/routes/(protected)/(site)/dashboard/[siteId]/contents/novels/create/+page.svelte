<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { enhance } from '$app/forms';

	const { data, form } = $props();

	const siteId = $derived(page.params.siteId);

	let title = '';
	let description = '';
	let genre = '';
	let tags = '';
	let published = false;
	let coverImage = '';
	let isSubmitting = false;

	// ประเภทนิยายที่มีให้เลือก
	const genres = [
		'แฟนตาซี',
		'โรแมนซ์',
		'ผจญภัย',
		'ระทึกขวัญ',
		'สยองขวัญ',
		'วิทยาศาสตร์',
		'ประวัติศาสตร์',
		'ชีวิตจริง',
		'ตลก',
		'ดราม่า',
		'อื่นๆ'
	];
</script>

<svelte:head>
	<title>สร้างนิยายใหม่ - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-3">
				<Icon icon="mdi:book-plus" class="text-primary" />
				สร้างนิยายใหม่
			</h1>
			<p class="text-base-content/70 mt-2">เริ่มต้นเขียนนิยายเรื่องใหม่</p>
		</div>
		<a href="/dashboard/{siteId}/contents/novels" class="btn btn-ghost">
			<Icon icon="mdi:arrow-left" class="w-4 h-4" />
			ย้อนกลับ
		</a>
	</div>

	{#if form?.message}
		<div class="alert alert-error mb-6">
			<Icon icon="mdi:alert-circle" class="w-6 h-6" />
			<span>{form.message}</span>
		</div>
	{/if}

	<form 
		method="POST" 
		action="?/createNovel"
		use:enhance={() => {
			isSubmitting = true;
			return async ({ result, update }) => {
				isSubmitting = false;
				
				if (result.type === 'success') {
					// Success will redirect automatically
				} else if (result.type === 'failure') {
					// Handle error - update will show the error
					await update();
				}
			};
		}}
		class="grid grid-cols-1 lg:grid-cols-3 gap-6"
	>
		<!-- เนื้อหาหลัก -->
		<div class="lg:col-span-2 space-y-6">
			<!-- ชื่อเรื่อง -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">ชื่อเรื่อง</h2>
					<input 
						type="text" 
						name="title"
						bind:value={title}
						placeholder="กรอกชื่อเรื่อง..." 
						class="input input-bordered w-full text-lg"
						required
					/>
				</div>
			</div>

			<!-- คำอธิบาย -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">คำอธิบายเรื่อง</h2>
					<textarea 
						name="description"
						bind:value={description}
						placeholder="เขียนคำอธิบายเรื่องย่อ..."
						class="textarea textarea-bordered w-full h-48"
						required
					></textarea>
					<div class="text-sm text-base-content/70 mt-2">
						คำอธิบายนี้จะแสดงในหน้ารายการนิยายเพื่อดึงดูดผู้อ่าน
					</div>
				</div>
			</div>
		</div>

		<!-- แถบด้านข้าง -->
		<div class="space-y-6">
			<!-- การเผยแพร่ -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">การเผยแพร่</h2>
					<div class="form-control">
						<label class="label cursor-pointer">
							<span class="label-text">เผยแพร่ทันที</span>
							<input 
								type="checkbox" 
								name="published"
								bind:checked={published}
								class="toggle toggle-primary" 
							/>
						</label>
					</div>
					<div class="text-sm text-base-content/70">
						{published ? 'นิยายจะแสดงในเว็บไซต์ทันที' : 'บันทึกเป็นแบบร่าง'}
					</div>
				</div>
			</div>

			<!-- ประเภท -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">ประเภท</h2>
					<select 
						name="genre"
						bind:value={genre}
						class="select select-bordered w-full"
					>
						<option value="">เลือกประเภท</option>
						{#each genres as genreOption}
							<option value={genreOption}>{genreOption}</option>
						{/each}
					</select>
				</div>
			</div>

			<!-- แท็ก -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">แท็ก</h2>
					<input 
						type="text" 
						name="tags"
						bind:value={tags}
						placeholder="แท็ก1, แท็ก2, แท็ก3..." 
						class="input input-bordered w-full"
					/>
					<div class="text-sm text-base-content/70 mt-2">
						แยกแท็กด้วยเครื่องหมายจุลภาค (,)
					</div>
					{#if tags}
						<div class="flex flex-wrap gap-2 mt-2">
							{#each tags.split(',').map(tag => tag.trim()).filter(tag => tag) as tag}
								<div class="badge badge-outline">{tag}</div>
							{/each}
						</div>
					{/if}
				</div>
			</div>

			<!-- ปกนิยาย -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">ปกนิยาย</h2>
					<input 
						type="url" 
						name="coverImage"
						bind:value={coverImage}
						placeholder="URL รูปปก..." 
						class="input input-bordered w-full"
					/>
					{#if coverImage}
						<div class="mt-4">
							<img 
								src={coverImage} 
								alt="Preview" 
								class="w-full h-48 object-cover rounded-lg"
								on:error={() => coverImage = ''}
							/>
						</div>
					{/if}
				</div>
			</div>

			<!-- ปุ่มบันทึก -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<button 
						type="submit" 
						class="btn btn-primary w-full"
						class:loading={isSubmitting}
						disabled={isSubmitting}
					>
						{#if isSubmitting}
							<span class="loading loading-spinner"></span>
							กำลังบันทึก...
						{:else}
							<Icon icon="mdi:content-save" class="w-4 h-4" />
							{published ? 'เผยแพร่นิยาย' : 'บันทึกแบบร่าง'}
						{/if}
					</button>
					
					{#if !published}
						<div class="text-sm text-center text-base-content/70 mt-2">
							หลังจากบันทึกแล้ว คุณสามารถเพิ่มตอนได้
						</div>
					{/if}
				</div>
			</div>
		</div>
	</form>
</div>