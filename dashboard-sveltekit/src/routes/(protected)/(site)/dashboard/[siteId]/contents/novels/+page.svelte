<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { enhance } from '$app/forms';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	// ✅ Props from server
	const { data, form } = $props<{
		data: any;
		form?: any;
	}>();

	const siteId = $derived(page.params.siteId);

	// ✅ Client state management
	let isDeleteLoading = $state(false);
	let isToggleLoading = $state(false);

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			if (formResult.type === 'delete') {
				showSuccess('ลบนิยายสำเร็จ!', formResult.message);
			} else if (formResult.type === 'toggle') {
				showSuccess('เปลี่ยนสถานะสำเร็จ!', formResult.message);
			}
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleDeleteResult(result: any) {
		isDeleteLoading = false;

		if (result.type === 'success') {
			showSuccess('ลบนิยายสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('ลบนิยายไม่สำเร็จ', result.data?.message);
		}
	}

	function handleToggleResult(result: any) {
		isToggleLoading = false;

		if (result.type === 'success') {
			showSuccess('เปลี่ยนสถานะสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('เปลี่ยนสถานะไม่สำเร็จ', result.data?.message);
		}
	}

	// ✅ ลบนิยายด้วย form action
	function deleteNovel(novelId: string) {
		const form = document.getElementById(`delete-form-${novelId}`) as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	// ✅ เปลี่ยนสถานะการเผยแพร่ด้วย form action
	function togglePublish(novelId: string, currentStatus: boolean) {
		const form = document.getElementById(`toggle-form-${novelId}`) as HTMLFormElement;
		if (form) {
			// Update hidden input value
			const publishedInput = form.querySelector('input[name="published"]') as HTMLInputElement;
			if (publishedInput) {
				publishedInput.value = (!currentStatus).toString();
			}
			form.requestSubmit();
		}
	}
</script>

<svelte:head>
	<title>จัดการนิยาย - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-3">
				<Icon icon="mdi:book-open-page-variant" class="text-primary" />
				จัดการนิยาย
			</h1>
			<p class="text-base-content/70 mt-2">สร้างและจัดการนิยายและเรื่องสั้น</p>
		</div>
		<a href="/dashboard/{siteId}/contents/novels/create" class="btn btn-primary">
			<Icon icon="mdi:plus" class="w-4 h-4" />
			สร้างนิยายใหม่
		</a>
	</div>

	<!-- สถิติ -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-primary">
				<Icon icon="mdi:book-open-page-variant" class="w-8 h-8" />
			</div>
			<div class="stat-title">นิยายทั้งหมด</div>
			<div class="stat-value text-primary">{data.novels?.length || 0}</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-success">
				<Icon icon="mdi:check-circle" class="w-8 h-8" />
			</div>
			<div class="stat-title">เผยแพร่แล้ว</div>
			<div class="stat-value text-success">
				{data.novels?.filter((n: any) => n.published).length || 0}
			</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-warning">
				<Icon icon="mdi:clock" class="w-8 h-8" />
			</div>
			<div class="stat-title">แบบร่าง</div>
			<div class="stat-value text-warning">
				{data.novels?.filter((n: any) => !n.published).length || 0}
			</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-info">
				<Icon icon="mdi:file-document-multiple" class="w-8 h-8" />
			</div>
			<div class="stat-title">ตอนทั้งหมด</div>
			<div class="stat-value text-info">
				{data.novels?.reduce((total: number, novel: any) => total + (novel.chaptersCount || 0), 0) || 0}
			</div>
		</div>
	</div>

	<!-- ✅ Hidden Forms for Hybrid Approach -->
	{#if data.novels && data.novels.length > 0}
		{#each data.novels as novel}
			<!-- Delete Form -->
			<form
				id="delete-form-{novel.id}"
				method="POST"
				action="?/deleteNovel"
				use:enhance={() => {
					isDeleteLoading = true;
					showLoading('กำลังลบนิยาย...');

					return async ({ result }) => {
						handleDeleteResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="novelId" value={novel.id} />
			</form>

			<!-- Toggle Publish Form -->
			<form
				id="toggle-form-{novel.id}"
				method="POST"
				action="?/togglePublish"
				use:enhance={() => {
					isToggleLoading = true;
					showLoading('กำลังเปลี่ยนสถานะ...');

					return async ({ result }) => {
						handleToggleResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="novelId" value={novel.id} />
				<input type="hidden" name="published" value={novel.published.toString()} />
			</form>
		{/each}
	{/if}

	<!-- ตารางนิยาย -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<div class="overflow-x-auto">
				<table class="table table-zebra">
					<thead>
						<tr>
							<th>ชื่อเรื่อง</th>
							<th>ประเภท</th>
							<th>จำนวนตอน</th>
							<th>สถานะ</th>
							<th>วันที่สร้าง</th>
							<th>การดำเนินการ</th>
						</tr>
					</thead>
					<tbody>
						{#if data.novels && data.novels.length > 0}
							{#each data.novels as novel}
								<tr>
									<td>
										<div class="flex items-center gap-3">
											{#if novel.coverImage}
												<div class="avatar">
													<div class="mask mask-squircle w-12 h-12">
														<img src={novel.coverImage} alt={novel.title} />
													</div>
												</div>
											{/if}
											<div>
												<div class="font-bold">{novel.title}</div>
												<div class="text-sm opacity-50 truncate max-w-xs">
													{novel.description?.substring(0, 100) + '...'}
												</div>
											</div>
										</div>
									</td>
									<td>
										{#if novel.genre}
											<div class="badge badge-outline">{novel.genre}</div>
										{:else}
											<span class="text-base-content/50">ไม่ระบุ</span>
										{/if}
									</td>
									<td>
										<div class="flex items-center gap-2">
											<span class="font-semibold">{novel.chaptersCount || 0}</span>
											<span class="text-sm text-base-content/70">ตอน</span>
										</div>
									</td>
									<td>
										<div class="form-control">
											<label class="label cursor-pointer">
												<input 
													type="checkbox" 
													class="toggle toggle-success" 
													checked={novel.published}
													onchange={() => togglePublish(novel.id, novel.published)}
												/>
												<span class="label-text ml-2">
													{novel.published ? 'เผยแพร่' : 'แบบร่าง'}
												</span>
											</label>
										</div>
									</td>
									<td>{new Date(novel.createdAt).toLocaleDateString('th-TH')}</td>
									<td>
										<div class="flex gap-2">
											<a 
												href="/dashboard/{siteId}/contents/novels/{novel.id}/chapters"
												class="btn btn-ghost btn-sm"
												title="จัดการตอน"
											>
												<Icon icon="mdi:file-document-multiple" class="w-4 h-4" />
											</a>
											<a 
												href="/dashboard/{siteId}/contents/novels/{novel.id}/edit"
												class="btn btn-ghost btn-sm"
												title="แก้ไข"
											>
												<Icon icon="mdi:pencil" class="w-4 h-4" />
											</a>
											<button 
												class="btn btn-ghost btn-sm text-error"
												title="ลบ"
												onclick={() => deleteNovel(novel.id)}
											>
												<Icon icon="mdi:delete" class="w-4 h-4" />
											</button>
										</div>
									</td>
								</tr>
							{/each}
						{:else}
							<tr>
								<td colspan="6" class="text-center py-8">
									<div class="flex flex-col items-center gap-4">
										<Icon icon="mdi:book-open-outline" class="w-16 h-16 text-base-content/30" />
										<div>
											<h3 class="text-lg font-semibold">ยังไม่มีนิยาย</h3>
											<p class="text-base-content/70">เริ่มสร้างนิยายแรกของคุณ</p>
										</div>
										<a href="/dashboard/{siteId}/contents/novels/create" class="btn btn-primary">
											<Icon icon="mdi:plus" class="w-4 h-4" />
											สร้างนิยายใหม่
										</a>
									</div>
								</td>
							</tr>
						{/if}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>