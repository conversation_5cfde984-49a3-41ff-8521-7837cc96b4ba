<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { Button, Card, Input, Label, Textarea, Select, Checkbox, Badge, Alert } from '$lib/components/ui';
	import Icon from '@iconify/svelte';

	interface PageData {
		categories?: any[];
		optionSets?: any[];
		error?: string;
	}

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	const formData = $state({
		// Basic info
		name: '',
		type: 'physical' as 'physical' | 'digital' | 'service' | 'subscription',
		saleChannel: 'both' as 'online' | 'offline' | 'both',
		sku: '',

		// Pricing
		price: 0,
		compareAtPrice: 0,
		costPrice: 0,
		cost: 0, // Added for compatibility

		// Inventory
		stock: 0,
		trackStock: true,
		allowBackorder: false,

		// Category and description
		categoryId: '',
		description: '',
		shortDescription: '',

		// Tags and images
		tags: [] as string[],
		images: [] as Array<{
			url: string;
			alt?: string;
			position?: number;
			isMain?: boolean;
		}>,

		// Variants - Updated to use option sets
		hasVariants: false,
		selectedOptionSets: [] as string[], // IDs of selected option sets
		variants: [] as Array<{
			name: string;
			sku?: string;
			price?: number;
			stock?: number;
			attributes: Record<string, string>;
		}>,

		// Digital assets (for digital products)
		digitalAssets: [] as Array<{
			name: string;
			url: string;
			type: string;
			size?: number;
		}>,

		// Shipping
		shipping: {
			weight: 0,
			dimensions: {
				length: 0,
				width: 0,
				height: 0,
			},
			requiresShipping: true,
			shippingClass: '',
		},

		// SEO
		seoTitle: '',
		seoDescription: '',

		// Status
		featured: false,
		isActive: true,
		allowPreOrder: false, // Added for compatibility

		// Custom fields
		customFields: {} as Record<string, any>,
	});

	let newTag = $state('');
	let isSubmitting = $state(false);
	let uploadedImage = $state<string | null>(null);
	let showUploadModal = $state(false);
	let showCopyModal = $state(false);
	let showStatsModal = $state(false);
	let showAddCategoryModal = $state(false);
	let newCategoryInput = $state('');
	let copySearch = $state('');

	const productTypes = [
		{ value: 'physical', label: 'สินค้าจริง' },
		{ value: 'digital', label: 'สินค้าดิจิทัล' },
		{ value: 'service', label: 'บริการ' },
		{ value: 'subscription', label: 'สมาชิก' },
	];

	const saleChannels = [
		{ value: 'online', label: 'ออนไลน์' },
		{ value: 'offline', label: 'ออฟไลน์' },
		{ value: 'both', label: 'ทั้งสองช่องทาง' },
	];

	const statuses = [
		{ value: true, label: 'เปิดขาย' },
		{ value: false, label: 'ปิดขาย' },
	];

	// Get selected option sets
	const selectedOptionSetsData = $derived(() => {
		return formData.selectedOptionSets.map(id =>
			data.optionSets?.find(set => set.id === id)
		).filter(Boolean);
	});

	// Mock candidates for copy functionality
	const copyCandidates = $derived([
		{ _id: '1', name: 'สินค้า A', description: '...', price: 100 },
		{ _id: '2', name: 'สินค้า B', description: '...', price: 200 },
	].filter(p => p.name.includes(copySearch)));

	// mock สถิติ
	const mockStats = {
		sales: 12,
		revenue: 3500,
		views: 120,
		rating: 4.7,
	};

	// Calculate profit margin
	const profitMargin = $derived(() => {
		if (formData.price > 0 && formData.costPrice && formData.costPrice > 0) {
			return (((formData.price - formData.costPrice) / formData.price) * 100).toFixed(2);
		}
		return '0';
	});

	// Calculate discount percentage
	const discountPercentage = $derived(() => {
		if (formData.compareAtPrice && formData.compareAtPrice > 0 && formData.price > 0) {
			return (
				((formData.compareAtPrice - formData.price) / formData.compareAtPrice) *
				100
			).toFixed(2);
		}
		return '0';
	});

	function addTag() {
		if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
			formData.tags = [...formData.tags, newTag.trim()];
			newTag = '';
		}
	}

	function removeTag(index: number) {
		formData.tags = formData.tags.filter((_, i) => i !== index);
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			event.preventDefault();
			addTag();
		}
	}

	// Add option set
	function addOptionSet(optionSetId: string) {
		if (!formData.selectedOptionSets.includes(optionSetId)) {
			formData.selectedOptionSets = [...formData.selectedOptionSets, optionSetId];
			generateVariants();
		}
	}

	// Remove option set
	function removeOptionSet(optionSetId: string) {
		formData.selectedOptionSets = formData.selectedOptionSets.filter(id => id !== optionSetId);
		generateVariants();
	}

	// Generate variants based on selected option sets
	function generateVariants() {
		if (formData.selectedOptionSets.length === 0) {
			formData.variants = [];
			return;
		}

		const optionSets = selectedOptionSetsData();
		if (optionSets.length === 0) return;

		// Generate all combinations
		const combinations = generateCombinations(optionSets);

		formData.variants = combinations.map((combination, index) => {
			const name = combination.map((opt: any) => opt.label).join(' / ');
			const attributes: Record<string, string> = {};

			combination.forEach((opt: any, idx: number) => {
				const optionSet = optionSets[idx];
				if (optionSet) {
					attributes[optionSet.name] = opt.value;
				}
			});

			return {
				name,
				sku: `${formData.name ? formData.name.replace(/\s+/g, '-').toLowerCase() : 'product'}-${combination.map((opt: any) => opt.value).join('-')}`,
				price: formData.price,
				stock: 0,
				attributes,
			};
		});
	}

	// Helper function to generate combinations
	function generateCombinations(optionSets: any[]): any[] {
		if (optionSets.length === 0) return [];
		if (optionSets.length === 1) return optionSets[0].options.map((opt: any) => [opt]);

		const [first, ...rest] = optionSets;
		const restCombinations = generateCombinations(rest);

		const combinations: any[] = [];
		for (const option of first.options) {
			for (const restCombination of restCombinations) {
				combinations.push([option, ...restCombination]);
			}
		}

		return combinations;
	}

	// Add variant manually
	function addVariant() {
		formData.variants = [...formData.variants, {
			name: '',
			sku: '',
			price: formData.price,
			stock: 0,
			attributes: {},
		}];
	}

	// Remove variant
	function removeVariant(index: number) {
		formData.variants = formData.variants.filter((_, i) => i !== index);
	}

	// Add digital asset
	function addDigitalAsset() {
		formData.digitalAssets = [...formData.digitalAssets, {
			name: '',
			url: '',
			type: '',
			size: 0,
		}];
	}

	// Remove digital asset
	function removeDigitalAsset(index: number) {
		formData.digitalAssets = formData.digitalAssets.filter((_, i) => i !== index);
	}

	function handleQuickAction(action: string) {
		if (action === 'upload') showUploadModal = true;
		else if (action === 'copy') showCopyModal = true;
		else if (action === 'stats') showStatsModal = true;
	}

	function handleAddCategory() {
		if (newCategoryInput && !data.categories?.find(c => c.name === newCategoryInput)) {
			// In real app, this would call an API to add the category
			formData.categoryId = newCategoryInput;
			newCategoryInput = '';
			showAddCategoryModal = false;
		}
	}

	function onUploadImage(e: Event) {
		const file = (e.target as HTMLInputElement).files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = ev => {
				uploadedImage = ev.target?.result as string;
			};
			reader.readAsDataURL(file);
		}
	}

	function confirmUploadImage() {
		// mock: เพิ่ม url ไปใน images array
		if (uploadedImage) {
			formData.images = [...formData.images, {
				url: uploadedImage,
				alt: '',
				position: formData.images.length,
				isMain: formData.images.length === 0,
			}];
			showUploadModal = false;
			uploadedImage = null;
		}
	}

	function copyProduct(item: any) {
		formData.name = item.name;
		formData.description = item.description;
		formData.price = item.price;
		showCopyModal = false;
	}

	// Navigate to options management
	function goToOptionsManagement() {
		const siteId = $derived(page.params.siteId);
		goto(`/dashboard/${siteId}/options`);
	}

	async function handleSubmit() {
		isSubmitting = true;
	}

	onMount(() => {
		// Initialize form with default values if needed
	});
</script>

<svelte:head>
	<title>เพิ่มสินค้าใหม่</title>
	<meta name="description" content="เพิ่มสินค้าใหม่เข้าสู่ร้านค้า" />
</svelte:head>

<div class="container mx-auto p-6 space-y-8">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
		<div>
			<h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
				เพิ่มสินค้าใหม่
			</h1>
			<p class="text-gray-600 dark:text-gray-400">
				เพิ่มสินค้าใหม่เข้าสู่ร้านค้า
			</p>
		</div>
		<div class="flex items-center gap-3">
			<Button
				onclick={() => {
					const siteId = $derived(page.params.siteId);
					goto(`/dashboard/${siteId}/products`);
				}}
				variant="outline"
				size="sm"
				class="hover:bg-gray-100 dark:hover:bg-gray-800"
			>
				<Icon icon="mdi:arrow-left" class="w-4 h-4 mr-2" />
				ย้อนกลับ
			</Button>
			<Button
				onclick={handleSubmit}
				loading={isSubmitting}
				color="primary"
				size="sm"
				class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
			>
				<Icon icon="mdi:check" class="w-5 h-5 mr-2" />
				เพิ่มสินค้า
			</Button>
		</div>
	</div>

	<!-- Error Alert -->
	{#if data.error}
		<Alert color="error" class="shadow-lg">
			<Icon icon="mdi:alert-circle" class="w-6 h-6" />
			<span class="font-medium">ข้อผิดพลาด</span>
			<p>{data.error}</p>
		</Alert>
	{/if}

	<!-- Loading State -->
	{#if isSubmitting}
		<div class="flex items-center justify-center p-8">
			<div class="flex items-center gap-3">
				<div class="loading loading-spinner loading-lg text-primary"></div>
				<span class="text-lg">กำลังโหลด...</span>
			</div>
		</div>
	{:else}
		<form method="POST" use:enhance={handleSubmit} class="space-y-8">
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
				<!-- Main Form -->
				<div class="lg:col-span-2 space-y-4">
					<!-- Basic Information -->
					<Card class="overflow-hidden">
						{#snippet header()}
							<div class="flex items-center gap-3">
								<div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
									<Icon icon="mdi:information-outline" class="w-5 h-5 text-white" />
								</div>
								<h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
							</div>
						{/snippet}

						<div class="space-y-4">
							<!-- Product Name -->
							<div>
								<label for="productName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									ชื่อสินค้า <span class="text-red-500">*</span>
								</label>
								<Input id="productName" bind:value={formData.name} placeholder="กรอกชื่อสินค้า" class="w-full" />
							</div>

							<!-- Product Type and Sale Channel -->
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div>
									<label for="productType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										ประเภทสินค้า <span class="text-red-500">*</span>
									</label>
									<Select id="productType" bind:value={formData.type} class="w-full">
										{#each productTypes as type}
											<option value={type.value}>{type.label}</option>
										{/each}
									</Select>
								</div>

								<div>
									<label for="saleChannel" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										ช่องทางการขาย <span class="text-red-500">*</span>
									</label>
									<Select id="saleChannel" bind:value={formData.saleChannel} class="w-full">
										{#each saleChannels as channel}
											<option value={channel.value}>{channel.label}</option>
										{/each}
									</Select>
								</div>
							</div>

							<!-- Description -->
							<div>
								<label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									รายละเอียดสินค้า
								</label>
								<Textarea
									id="description"
									bind:value={formData.description}
									placeholder="อธิบายรายละเอียดสินค้า (ไม่เกิน 5000 ตัวอักษร)"
									rows={4}
									class="w-full"
								/>
							</div>

							<!-- Short Description -->
							<div>
								<label for="shortDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									รายละเอียดสั้น
								</label>
								<Textarea
									id="shortDescription"
									bind:value={formData.shortDescription}
									placeholder="รายละเอียดสั้นของสินค้า (ไม่เกิน 500 ตัวอักษร)"
									rows={2}
									class="w-full"
								/>
							</div>

							<!-- Category -->
							<div>
								<label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									หมวดหมู่
								</label>
								<Select id="category" bind:value={formData.categoryId} class="w-full">
									<option value="">เลือกหรือเพิ่มหมวดหมู่ใหม่</option>
									{#if data.categories}
										{#each data.categories as category}
											<option value={category.id}>{category.name}</option>
										{/each}
									{/if}
								</Select>
								<Button onclick={() => showAddCategoryModal = true} variant="outline" size="sm" class="mt-2">
									เพิ่มหมวดหมู่ใหม่
								</Button>
							</div>
						</div>
					</Card>

					<!-- Pricing -->
					<Card class="overflow-hidden">
						{#snippet header()}
							<div class="flex items-center gap-3">
								<div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
									<Icon icon="mdi:currency-usd" class="w-5 h-5 text-white" />
								</div>
								<h2 class="text-xl font-semibold text-gray-900 dark:text-white">ราคาและต้นทุน</h2>
							</div>
						{/snippet}

						<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div>
								<label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									ราคาขาย <span class="text-red-500">*</span>
								</label>
								<Input
									id="price"
									name="price"
									type="number"
									bind:value={formData.price}
									placeholder="0.00"
									step="0.01"
									min="0"
									class="w-full"
									required
								/>
							</div>

							<div>
								<label for="compareAtPrice" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									ราคาเปรียบเทียบ
								</label>
								<Input
									id="compareAtPrice"
									name="compareAtPrice"
									type="number"
									bind:value={formData.compareAtPrice}
									placeholder="0.00"
									step="0.01"
									min="0"
									class="w-full"
								/>
							</div>

							<div>
								<label for="costPrice" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									ต้นทุน
								</label>
								<Input
									id="costPrice"
									name="costPrice"
									type="number"
									bind:value={formData.costPrice}
									placeholder="0.00"
									step="0.01"
									min="0"
									class="w-full"
								/>
							</div>
						</div>

						<!-- Price Summary -->
						<div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
							<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
								<div>
									<p class="text-gray-600 dark:text-gray-400">ส่วนลด</p>
									<p class="font-semibold text-green-600">{discountPercentage}%</p>
								</div>
								<div>
									<p class="text-gray-600 dark:text-gray-400">กำไร</p>
									<p class="font-semibold text-blue-600">{profitMargin}%</p>
								</div>
								<div>
									<p class="text-gray-600 dark:text-gray-400">ต้นทุนรวม</p>
									<p class="font-semibold text-gray-900 dark:text-white">{formData.costPrice} บาท</p>
								</div>
								<div>
									<p class="text-gray-600 dark:text-gray-400">ราคาขาย</p>
									<p class="font-semibold text-gray-900 dark:text-white">{formData.price} บาท</p>
								</div>
							</div>
						</div>
					</Card>

					<!-- Inventory -->
					<Card class="overflow-hidden">
						{#snippet header()}
							<div class="flex items-center gap-3">
								<div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
									<Icon icon="mdi:package-variant" class="w-5 h-5 text-white" />
								</div>
								<h2 class="text-xl font-semibold text-gray-900 dark:text-white">คลังสินค้า</h2>
							</div>
						{/snippet}

						<div class="space-y-4">
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div>
									<label for="stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										จำนวนในคลัง
									</label>
									<Input
										id="stock"
										name="stock"
										type="number"
										bind:value={formData.stock}
										placeholder="0"
										min="0"
										class="w-full"
									/>
								</div>

								<div class="flex items-center space-x-4">
									<Checkbox bind:checked={formData.trackStock} label="ติดตามสต็อก" />
									<Checkbox bind:checked={formData.allowBackorder} label="อนุญาตสั่งซื้อเมื่อหมด" />
								</div>
							</div>

							<!-- Shipping Info (for physical products) -->
							{#if formData.type === 'physical'}
								<div class="space-y-4">
									<h3 class="text-lg font-medium text-gray-900 dark:text-white">ข้อมูลการจัดส่ง</h3>

									<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
										<div>
											<label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
												น้ำหนัก (กรัม)
											</label>
											<Input
												id="weight"
												name="weight"
												type="number"
												bind:value={formData.shipping.weight}
												placeholder="0"
												min="0"
												class="w-full"
											/>
										</div>

										<div>
											<label for="shippingClass" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
												คลาสการจัดส่ง
											</label>
											<Input
												id="shippingClass"
												name="shippingClass"
												bind:value={formData.shipping.shippingClass}
												placeholder="เช่น standard, express"
												class="w-full"
											/>
										</div>
									</div>

									<!-- Dimensions -->
									<div>
										<label for="dimensions" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
											ขนาด (เซนติเมตร)
										</label>
										<div class="grid grid-cols-3 gap-4">
											<div>
												<label for="length" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">ยาว</label>
												<Input
													id="length"
													name="length"
													type="number"
													bind:value={formData.shipping.dimensions.length}
													placeholder="0"
													min="0"
													class="w-full"
												/>
											</div>
											<div>
												<label for="width" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">กว้าง</label>
												<Input
													id="width"
													name="width"
													type="number"
													bind:value={formData.shipping.dimensions.width}
													placeholder="0"
													min="0"
													class="w-full"
												/>
											</div>
											<div>
												<label for="height" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">สูง</label>
												<Input
													id="height"
													name="height"
													type="number"
													bind:value={formData.shipping.dimensions.height}
													placeholder="0"
													min="0"
													class="w-full"
												/>
											</div>
										</div>
									</div>

									<Checkbox bind:checked={formData.shipping.requiresShipping} label="ต้องการการจัดส่ง" />
								</div>
							{/if}
						</div>
					</Card>

					<!-- Tags -->
					<Card class="overflow-hidden">
						{#snippet header()}
							<div class="flex items-center gap-3">
								<div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
									<Icon icon="mdi:tag-multiple" class="w-5 h-5 text-white" />
								</div>
								<h2 class="text-xl font-semibold text-gray-900 dark:text-white">แท็ก</h2>
							</div>
						{/snippet}

						<div class="space-y-4">
							<div class="flex gap-2">
								<Input
									bind:value={newTag}
									placeholder="เพิ่มแท็ก"
									class="flex-1"
									onkeydown={handleKeyPress}
								/>
								<Button onclick={addTag} variant="outline" size="sm">
									เพิ่ม
								</Button>
							</div>

							{#if formData.tags.length > 0}
								<div class="flex flex-wrap gap-2">
									{#each formData.tags as tag, index}
										<button
											type="button"
											class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/40 cursor-pointer"
											onclick={() => removeTag(index)}
										>
											{tag}
											<Icon icon="mdi:close" class="w-3 h-3" />
										</button>
									{/each}
								</div>
							{/if}
						</div>
					</Card>
				</div>
			</div>

			<!-- Sidebar -->
			<div class="space-y-4">
				<!-- Quick Actions -->
				<Card class="overflow-hidden">
					{#snippet header()}
						<div class="flex items-center gap-3">
							<div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
								<Icon icon="mdi:lightning-bolt" class="w-5 h-5 text-white" />
							</div>
							<h3 class="text-lg font-semibold text-gray-900 dark:text-white">การดำเนินการด่วน</h3>
						</div>
					{/snippet}

					<div class="space-y-3">
						<Button
							onclick={() => handleQuickAction('upload')}
							variant="outline"
							size="sm"
							class="w-full justify-start"
						>
							<Icon icon="mdi:upload" class="w-4 h-4 mr-2" />
							อัปโหลดรูปภาพ
						</Button>

						<Button
							onclick={() => handleQuickAction('copy')}
							variant="outline"
							size="sm"
							class="w-full justify-start"
						>
							<Icon icon="mdi:content-copy" class="w-4 h-4 mr-2" />
							คัดลอกจากสินค้าอื่น
						</Button>

						<Button
							onclick={() => handleQuickAction('stats')}
							variant="outline"
							size="sm"
							class="w-full justify-start"
						>
							<Icon icon="mdi:chart-line" class="w-4 h-4 mr-2" />
							ดูสถิติ
						</Button>
					</div>
				</Card>

				<!-- Product Status -->
				<Card class="overflow-hidden">
					{#snippet header()}
						<div class="flex items-center gap-3">
							<div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
								<Icon icon="mdi:toggle-switch" class="w-5 h-5 text-white" />
							</div>
							<h3 class="text-lg font-semibold text-gray-900 dark:text-white">สถานะสินค้า</h3>
						</div>
					{/snippet}

					<div class="space-y-4">
						<Checkbox bind:checked={formData.isActive} label="เปิดขาย" />
						<Checkbox bind:checked={formData.featured} label="สินค้าแนะนำ" />
						<Checkbox bind:checked={formData.allowPreOrder} label="อนุญาตให้สั่งจองล่วงหน้า" />
					</div>
				</Card>

				<!-- SEO -->
				<Card class="overflow-hidden">
					{#snippet header()}
						<div class="flex items-center gap-3">
							<div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
								<Icon icon="mdi:search-web" class="w-5 h-5 text-white" />
							</div>
							<h3 class="text-lg font-semibold text-gray-900 dark:text-white">SEO</h3>
						</div>
					{/snippet}

					<div class="space-y-4">
						<div>
							<label for="seoTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
								หัวข้อ SEO
							</label>
							<Input
								id="seoTitle"
								bind:value={formData.seoTitle}
								placeholder="หัวข้อสำหรับ SEO"
								class="w-full"
							/>
						</div>

						<div>
							<label for="seoDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
								คำอธิบาย SEO
							</label>
							<Textarea
								id="seoDescription"
								bind:value={formData.seoDescription}
								placeholder="คำอธิบายสำหรับ SEO"
								rows={3}
								class="w-full"
							/>
						</div>
					</div>
				</Card>

				<!-- Submit Button -->
				<Button
					onclick={handleSubmit}
					color="primary"
					size="lg"
					class="w-full shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
					loading={isSubmitting}
				>
					<Icon icon="mdi:check" class="w-5 h-5 mr-2" />
					{isSubmitting ? 'กำลังบันทึก...' : 'บันทึกสินค้า'}
				</Button>
		</div>
	</form>
{/if}

<!-- Modal Dialogs -->
<!-- Upload Image Modal -->
{#if showUploadModal}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">อัปโหลดรูปภาพ</h3>
				<Button onclick={() => showUploadModal = false} variant="ghost" size="sm">
					<Icon icon="mdi:close" class="w-5 h-5" />
				</Button>
			</div>

			<div class="space-y-4">
				<div>
					<input
						type="file"
						accept="image/*"
						onchange={onUploadImage}
						class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
					/>
				</div>

				{#if uploadedImage}
					<div class="mt-4">
						<img src={uploadedImage} alt="Preview" class="w-full h-48 object-cover rounded-lg" />
					</div>
				{/if}

				<div class="flex justify-end gap-2 mt-6">
					<Button onclick={() => showUploadModal = false} variant="outline" size="sm">
						ยกเลิก
					</Button>
					<Button onclick={confirmUploadImage} color="primary" size="sm" disabled={!uploadedImage}>
						เพิ่มรูปภาพ
					</Button>
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- Copy Product Modal -->
{#if showCopyModal}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">คัดลอกจากสินค้าอื่น</h3>
				<Button onclick={() => showCopyModal = false} variant="ghost" size="sm">
					<Icon icon="mdi:close" class="w-5 h-5" />
				</Button>
			</div>

			<div class="space-y-4">
				<Input
					bind:value={copySearch}
					placeholder="ค้นหาสินค้า..."
					class="w-full"
				/>

				<div class="max-h-60 overflow-y-auto space-y-2">
					{#each copyCandidates as item}
						<button 
							type="button"
							class="w-full text-left p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer" 
							onclick={() => copyProduct(item)}
						>
							<h4 class="font-medium text-gray-900 dark:text-white">{item.name}</h4>
							<p class="text-sm text-gray-600 dark:text-gray-400">{item.price} บาท</p>
						</button>
					{/each}
				</div>

				<div class="flex justify-end gap-2 mt-6">
					<Button onclick={() => showCopyModal = false} variant="outline" size="sm">
						ยกเลิก
					</Button>
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- Stats Modal -->
{#if showStatsModal}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">สถิติสินค้า</h3>
				<Button onclick={() => showStatsModal = false} variant="ghost" size="sm">
					<Icon icon="mdi:close" class="w-5 h-5" />
				</Button>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
					<p class="text-2xl font-bold text-blue-600">{mockStats.sales}</p>
					<p class="text-sm text-gray-600 dark:text-gray-400">ยอดขาย</p>
				</div>
				<div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
					<p class="text-2xl font-bold text-green-600">{mockStats.revenue}</p>
					<p class="text-sm text-gray-600 dark:text-gray-400">รายได้</p>
				</div>
				<div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
					<p class="text-2xl font-bold text-purple-600">{mockStats.views}</p>
					<p class="text-sm text-gray-600 dark:text-gray-400">การดู</p>
				</div>
				<div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
					<p class="text-2xl font-bold text-yellow-600">{mockStats.rating}</p>
					<p class="text-sm text-gray-600 dark:text-gray-400">คะแนน</p>
				</div>
			</div>

			<div class="flex justify-end mt-6">
				<Button onclick={() => showStatsModal = false} variant="outline" size="sm">
					ปิด
				</Button>
			</div>
		</div>
	</div>
{/if}

<!-- Add Category Modal -->
{#if showAddCategoryModal}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">เพิ่มหมวดหมู่ใหม่</h3>
				<Button onclick={() => showAddCategoryModal = false} variant="ghost" size="sm">
					<Icon icon="mdi:close" class="w-5 h-5" />
				</Button>
			</div>

			<div class="space-y-4">
				<div>
					<label for="newCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						ชื่อหมวดหมู่
					</label>
					<Input
						id="newCategory"
						bind:value={newCategoryInput}
						placeholder="กรอกชื่อหมวดหมู่ใหม่"
						class="w-full"
					/>
				</div>

				<div class="flex justify-end gap-2 mt-6">
					<Button onclick={() => showAddCategoryModal = false} variant="outline" size="sm">
						ยกเลิก
					</Button>
					<Button onclick={handleAddCategory} color="primary" size="sm" disabled={!newCategoryInput}>
						เพิ่มหมวดหมู่
					</Button>
				</div>
			</div>
		</div>
	</div>
{/if}
</div>