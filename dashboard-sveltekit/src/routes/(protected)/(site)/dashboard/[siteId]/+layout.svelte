<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import Header from '$lib/components/layout/Header.svelte';
	import Sidebar from '$lib/components/layout/Sidebar.svelte';
	import WebSocketProvider from '$lib/components/providers/WebSocketProvider.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';

	const { children, data } = $props();

	// ✅ ตรวจสอบข้อมูลจาก server-side load function
	const hasServerData = !!data?.site;
	const hasServerError = !!data?.error;
	const serverUser = data?.user;

	// ✅ ใช้ข้อมูลจาก server เป็นหลัก แทนการพึ่งพา authStore
	const isAuthenticated = hasServerData || !!serverUser;

	console.log('SiteId Layout:', {
		hasServerData,
		hasServerError,
		serverUser: !!serverUser,
		isAuthenticated,
		siteId: data?.siteId,
		siteName: data?.site?.name,
	});

	onMount(() => {
		// ✅ ตั้งค่า user จาก SSR ถ้ามี
		if (serverUser && !authStore.user) {
			console.log('SiteId Layout: Setting user from SSR');
			authStore.setUserFromSSR(serverUser);
		}

		// ✅ ตั้งค่า site จาก SSR
		if (data?.site && data.site.fullDomain) {
			console.log('SiteId Layout: Setting site from SSR');
			siteStore.setSite(data.site as any);
		}

		// ✅ ไม่ต้องทำ client-side redirect เพราะ server-side จัดการแล้ว
		// Server-side layout จะ redirect อัตโนมัติถ้าไม่มี authentication หรือ site ไม่พบ
	});
</script>
<div class="bg-base-300">
<div class="max-w-[1920px] mx-auto">
	{#if hasServerError}
		<!-- ✅ แสดง error message ถ้ามี server error -->
		<div class="min-h-screen flex items-center justify-center">
			<div class="card bg-base-100 shadow-lg max-w-md">
				<div class="card-body text-center">
					<div
						class="w-16 h-16 bg-warning/20 rounded-full flex items-center justify-center mx-auto mb-4"
					>
						<Icon icon="mdi:alert-circle" class="w-8 h-8 text-warning" />
					</div>
					<h2 class="text-xl font-bold text-base-content mb-2">เกิดข้อผิดพลาด</h2>
					<p class="text-base-content/60 mb-4">{data?.error}</p>
					<div class="flex gap-2 justify-center">
						<button class="btn btn-primary" onclick={() => window.location.reload()}>
							ลองใหม่อีกครั้ง
						</button>
						<button class="btn btn-ghost" onclick={() => goto('/dashboard')}> กลับหน้าหลัก </button>
					</div>
				</div>
			</div>
		</div>
	{:else if !isAuthenticated}
		<!-- ✅ แสดง loading ถ้ายังไม่ได้ authenticated -->
		<div class="min-h-screen flex items-center justify-center">
			<div class="loading loading-spinner loading-lg"></div>
			<span class="ml-3">กำลังตรวจสอบสิทธิ์...</span>
		</div>
	{:else}
		<!-- ✅ แสดงหน้าปกติถ้าทุกอย่างเรียบร้อย -->
		<WebSocketProvider>
			<div class="drawer lg:drawer-open">
				<input id="drawer-toggle" type="checkbox" class="drawer-toggle" />

				<!-- Main Content -->
				<div class="drawer-content flex flex-col">
					<Header {data} />
					<main class="flex-1 p-2 sm:p-4">
						{@render children()}
					</main>
				</div>

				<!-- Sidebar -->
				<Sidebar {data} />
			</div>
		</WebSocketProvider>
	{/if}
</div>
</div>