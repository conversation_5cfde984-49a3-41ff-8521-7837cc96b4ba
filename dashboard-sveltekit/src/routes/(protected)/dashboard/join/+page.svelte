<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte'; 
	import {Input,Card,Button,Badge} from '$lib/components/ui';
	import { showError, showSuccess } from '$lib/utils/sweetalert';

	/**
	 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
	 * - Data loaded server-side using invitation service
	 * - Form actions with route-level validation
	 * - Progressive enhancement with client-side feedback
	 * - Type-safe data flow
	 */

	interface Invitation {
		_id: string;
		siteId: string;
		siteName: string;
		fromUserId: string;
		fromUserName: string;
		fromUserEmail: string;
		role: 'owner' | 'admin' | 'editor' | 'viewer';
		message?: string;
		status: 'pending' | 'accepted' | 'rejected' | 'expired';
		createdAt: string;
		expiresAt: string;
	}

	const { data, form } = $props();
	const invitations = $derived(data.invitations || []);
	let processingId: string | null = $state(null);
	let inviteCodeInput = $state('');
	let isJoiningWithCode = $state(false);

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม role
	function getRoleBadgeVariant(role: string) {
		switch (role) {
			case 'owner':
				return 'error';
			case 'admin':
				return 'primary';
			case 'editor':
				return 'secondary';
			case 'viewer':
				return 'neutral';
			default:
				return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม status
	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case 'pending':
				return 'warning';
			case 'accepted':
				return 'success';
			case 'rejected':
				return 'error';
			case 'expired':
				return 'neutral';
			default:
				return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม status
	function getStatusIcon(status: string) {
		switch (status) {
			case 'pending':
				return 'lucide:clock';
			case 'accepted':
				return 'lucide:check-circle';
			case 'rejected':
				return 'lucide:x-circle';
			case 'expired':
				return 'lucide:x-circle';
			default:
				return 'lucide:clock';
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	}

	// ตรวจสอบว่าคำเชิญหมดอายุหรือไม่
	function isExpired(expiresAt: string) {
		return new Date(expiresAt) < new Date();
	}

	// Handle form result
	$effect(() => {
		if (form?.success) {
			showSuccess(form.message || 'ดำเนินการสำเร็จ');
			// รีโหลดหน้าเพื่อดึงข้อมูลใหม่
			setTimeout(() => {
				window.location.reload();
			}, 1500);
		} else if (form?.error) {
			showError(form.error);
		}
	});
</script>

<!-- ✅ SEO Component -->
<SEO
	title="เข้าร่วมทีมงาน - Dashboard"
	description="จัดการคำเชิญเข้าร่วมทีมงานที่คุณได้รับ ยอมรับหรือปฏิเสธคำเชิญได้ง่ายๆ"
	keywords="เข้าร่วมทีมงาน, คำเชิญ, ทีมงาน, จัดการทีม"
	url="/dashboard/join"
	noindex={true}
/>

<div class="space-y-4">
	<!-- Header -->
	<!-- <div class="text-center mb-8">
		<div class="flex items-center justify-center gap-3 mb-4">
			<Icon icon="solar:users-group-two-rounded-bold" class="size-10 text-primary" />
			<h1 class="text-3xl font-bold text-primary">เข้าร่วมทีมงาน</h1>
		</div>
		<p class="text-lg text-base-content/70 max-w-2xl mx-auto">
			จัดการคำเชิญเข้าร่วมทีมงานที่คุณได้รับ หรือใส่โค้ดเชิญเพื่อเข้าร่วมทีมใหม่
		</p>
		<div class="mt-4">
			<Button
				icon="solar:arrow-left-bold"
				label="กลับไปหน้าหลัก"
				outline={true}
				onclick={() => goto('/dashboard')}
			/>
		</div>
	</div> -->

	<!-- Join with Invite Code -->
	<Card
		title="เข้าร่วมด้วยโค้ดเชิญ"
		titleIcon="solar:key-minimalistic-bold"
		size="full"
		class="mb-6"
	>
		<p class="text-base-content/70 mb-4">
			หากคุณมีโค้ดเชิญจากทีมงาน สามารถใส่โค้ดด้านล่างเพื่อเข้าร่วมได้ทันที
		</p>
		
		<form
			method="POST"
			action="?/joinWithCode"
			use:enhance={() => {
				isJoiningWithCode = true;
				return async ({ result }) => {
					isJoiningWithCode = false;
					if (result.type === 'success') {
						showSuccess(
							'เข้าร่วมทีมงานสำเร็จ',
							(result.data as any)?.message || 'เข้าร่วมทีมงานด้วยโค้ดเชิญสำเร็จ'
						);
						// รีโหลดหน้าเพื่อดึงข้อมูลใหม่
						window.location.reload();
					} else {
						const errorMessage =
							result.type === 'failure'
								? (result.data as any)?.error || 'โค้ดเชิญไม่ถูกต้อง'
								: 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน';
						showError(errorMessage);
					}
				};
			}}
			class="flex gap-3"
		>
			<Input
				type="text"
				name="inviteCode"
				placeholder="ใส่โค้ดเชิญ (เช่น abc123def)"
				class="input input-bordered flex-1"
				bind:value={inviteCodeInput}
				required
			/>
			<Button
				type="submit"
				disabled={isJoiningWithCode || !inviteCodeInput.trim()}
				color="primary"
				icon="solar:login-3-bold"
				label="เข้าร่วม"
				loading={isJoiningWithCode}
			/>
		</form>
	</Card>

	{#if invitations.length === 0}
		<Card
			title="ไม่มีคำเชิญ"
			titleIcon="solar:users-group-two-rounded-bold"
			size="full"
			class="text-center py-12"
		>
			<div class="flex flex-col items-center space-y-4">
				<Icon icon="solar:users-group-two-rounded-bold" class="size-16 text-base-content/30" />
				<div class="space-y-2">
					<h3 class="text-lg font-semibold">ไม่มีคำเชิญ</h3>
					<p class="text-base-content/70">
						คุณยังไม่มีคำเชิญเข้าร่วมทีมงานใดๆ<br />
						เมื่อมีคำเชิญใหม่ คุณจะเห็นรายการที่นี่
					</p>
				</div>
			</div>
		</Card>
	{:else}
		<div class="grid gap-4">
			{#each invitations as invitation (invitation._id)}
				{@const expired = isExpired(invitation.expiresAt)}

				<Card
					title={invitation.siteName}
					titleIcon="solar:buildings-3-bold"
					size="full"
					class="space-y-4"
				>
					<!-- Header with badges -->
					<div class="flex items-start justify-between mb-4">
						<div class="space-y-2">
							<div class="flex items-center gap-2 text-sm text-base-content/70">
								<Icon icon="solar:user-bold" class="size-4" />
								เชิญโดย {invitation.fromUserName}
							</div>
							<div class="flex items-center gap-2 text-sm text-base-content/70">
								<Icon icon="solar:letter-bold" class="size-4" />
								{invitation.fromUserEmail}
							</div>
						</div>
						<div class="flex items-center gap-2">
							<Badge color={getRoleBadgeVariant(invitation.role)}>
								{invitation.role}
							</Badge>
							<Badge color={getStatusBadgeVariant(invitation.status)}>
								<Icon icon={getStatusIcon(invitation.status)} class="size-3 mr-1" />
								{invitation.status === 'pending'
									? 'รอดำเนินการ'
									: invitation.status === 'accepted'
										? 'ยอมรับแล้ว'
										: invitation.status === 'rejected'
											? 'ปฏิเสธแล้ว'
											: 'หมดอายุ'}
							</Badge>
						</div>
					</div>

					<!-- Message if exists -->
					{#if invitation.message}
						<div class="alert alert-info alert-soft">
							<Icon icon="solar:chat-round-dots-bold" class="size-5" />
							<div>
								<div class="font-semibold">ข้อความจากผู้เชิญ</div>
								<div class="text-sm">{invitation.message}</div>
							</div>
						</div>
					{/if}

					<!-- Date information -->
					<div class="flex items-center gap-6 text-sm text-base-content/60">
						<div class="flex items-center gap-2">
							<Icon icon="solar:calendar-add-bold" class="size-4" />
							<span>ส่งเมื่อ: {formatDate(invitation.createdAt)}</span>
						</div>
						<div class="flex items-center gap-2">
							<Icon icon="solar:clock-circle-bold" class="size-4" />
							<span>หมดอายุ: {formatDate(invitation.expiresAt)}</span>
						</div>
					</div>

					<!-- Expired warning -->
					{#if expired}
						<div class="alert alert-error alert-soft">
							<Icon icon="solar:danger-triangle-bold" class="size-5" />
							<div>
								<div class="font-semibold">คำเชิญหมดอายุ</div>
								<div class="text-sm">คำเชิญนี้หมดอายุแล้ว ไม่สามารถดำเนินการได้</div>
							</div>
						</div>
					{/if}

					<!-- Action buttons for pending invitations -->
					{#if invitation.status === 'pending' && !expired}
						<div class="divider"></div>
						<div class="flex gap-3">
							<!-- ✅ Accept Invitation Form -->
							<form
								method="POST"
								action="?/acceptInvitation"
								use:enhance={() => {
									processingId = invitation._id;
									return async ({ result }) => {
										processingId = null;
										if (result.type === 'success') {
											showSuccess(
												'เข้าร่วมทีมงานสำเร็จ',
												(result.data as any)?.message || 'เข้าร่วมทีมงานเรียบร้อยแล้ว'
											);
											// รีโหลดหน้าเพื่อดึงข้อมูลใหม่
											window.location.reload();
										} else {
											const errorMessage =
												result.type === 'failure'
													? (result.data as any)?.error || 'ไม่สามารถเข้าร่วมทีมงานได้'
													: 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน';
											showError(errorMessage);
										}
									};
								}}
								class="flex-1"
							>
								<input type="hidden" name="invitationId" value={invitation._id} />
								<Button
									type="submit"
									disabled={processingId === invitation._id}
									class="w-full"
									color="success"
									icon="solar:check-circle-bold"
									label="ยอมรับคำเชิญ"
									loading={processingId === invitation._id}
								/>
							</form>

							<!-- ✅ Reject Invitation Form -->
							<form
								method="POST"
								action="?/rejectInvitation"
								use:enhance={() => {
									processingId = invitation._id;
									return async ({ result }) => {
										processingId = null;
										if (result.type === 'success') {
											showSuccess(
												'ปฏิเสธคำเชิญสำเร็จ',
												(result.data as any)?.message || 'ปฏิเสธคำเชิญเรียบร้อยแล้ว'
											);
											// รีโหลดหน้าเพื่อดึงข้อมูลใหม่
											window.location.reload();
										} else {
											const errorMessage =
												result.type === 'failure'
													? (result.data as any)?.error || 'ไม่สามารถปฏิเสธคำเชิญได้'
													: 'เกิดข้อผิดพลาดในการปฏิเสธคำเชิญ';
											showError(errorMessage);
										}
									};
								}}
								class="flex-1"
							>
								<input type="hidden" name="invitationId" value={invitation._id} />
								<Button
									type="submit"
									disabled={processingId === invitation._id}
									class="w-full"
									color="error"
									outline={true}
									icon="solar:close-circle-bold"
									label="ปฏิเสธคำเชิญ"
									loading={processingId === invitation._id}
								/>
							</form>
						</div>
					{/if}
				</Card>
			{/each}
		</div>
	{/if}
</div>
