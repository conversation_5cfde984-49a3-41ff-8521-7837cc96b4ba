<script lang="ts">
	import Icon from '@iconify/svelte';
	import { browser } from '$app/environment';
	import { onDestroy, onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import UserMenu from '$lib/components/layout/UserMenu.svelte';
	import WebSocketProvider from '$lib/components/providers/WebSocketProvider.svelte';
	 
	import { authStore } from '$lib/stores/auth.svelte';
	import { languageStore } from '$lib/stores/language.svelte';
	import { themeStore } from '$lib/stores/theme.svelte';

	const { data, children } = $props<{
		data: { user?: any; token?: string };
	}>();

	// ✅ Client state management
	let isInitialized = $state(false);
	let isAuthenticated = $derived(authStore.isAuthenticated);
	let currentUser = $derived(authStore.user);

	// ใช้ browser check สำหรับ page.url.pathname
	const currentPath = $derived(browser ? page.url.pathname : '');

	onMount(async () => {
		// ตั้งค่า user จาก SSR
		if (data?.user) {
			authStore.setUserFromSSR(data.user);
		}

		// รอให้ authStore initialized ด้วย polling
		const checkInitialized = () => {
			if (authStore.isInitialized) {
				isInitialized = true;
				return true;
			}
			return false;
		};

		// ตรวจสอบทันที
		if (checkInitialized()) {
			// ถ้า initialized แล้ว ให้ตรวจสอบ authentication
			if (!isAuthenticated) {
				console.log('Not authenticated, attempting token refresh...');

				// ลอง refresh token ก่อน
				const refreshSuccess = await authStore.refreshToken();

				if (!refreshSuccess) {
					console.log('Token refresh failed, redirecting to signin...');
					goto('/signin');
				}
			}
		} else {
			// ถ้ายังไม่ initialized ให้รอ
			const interval = setInterval(() => {
				if (checkInitialized()) {
					clearInterval(interval);
					
					// ตรวจสอบ authentication หลังจาก initialized
					if (!isAuthenticated) {
						console.log('Not authenticated, attempting token refresh...');

						// ลอง refresh token ก่อน
						authStore.refreshToken().then(refreshSuccess => {
							if (!refreshSuccess) {
								console.log('Token refresh failed, redirecting to signin...');
								goto('/signin');
							}
						});
					}
				}
			}, 100);

			// หยุด polling หลังจาก 5 วินาที
			setTimeout(() => {
				clearInterval(interval);
				if (!isInitialized) {
					console.warn('AuthStore initialization timeout');
					isInitialized = true; // Force initialization
				}
			}, 5000);
		}
	});

	onDestroy(() => {
		// Cleanup stores
		authStore.destroy();
		themeStore.destroy();
		languageStore.destroy();
	});
</script>

		{#if browser && isInitialized && isAuthenticated}
	<WebSocketProvider>
		<div class="space-y-4 h-screen">
			<!-- Header -->
			<header>
				<div class="bg-base-200">
					<div class="container mx-auto">
						<header class="navbar">
							<div class="navbar-start flex-1 flex flex-row gap-1">
								<a
									href="/dashboard"
									class="btn {currentPath === '/dashboard' ? 'btn-primary' : 'btn-ghost'}"
								>
									<Icon icon="solar:home-smile-angle-bold" class="size-6" /> จัดการเว็บไซต์
								</a>

								<a
									href="/dashboard/create"
									class="btn {currentPath === '/dashboard/create'
										? 'btn-primary'
										: 'btn-ghost'}"
								>
									<Icon icon="solar:home-add-angle-bold" class="size-6" /> สร้างเว็บไซต์
								</a>

								<a
									href="/dashboard/join"
									class="btn {currentPath === '/dashboard/join' ? 'btn-primary' : 'btn-ghost'}"
								>
									<Icon icon="solar:users-group-rounded-bold" class="size-6" /> เข้าร่วมเว็บไซต์
								</a> 
							</div>

							<!-- Actions -->
							<div class="navbar-end w-fit">
								<UserMenu />
							</div>
						</header>
					</div>
				</div>
			</header>

			<!-- Main Content -->
			<main class="container mx-auto px-4 sm:px-6 lg:px-8"> 
					{@render children()} 
			</main>
		</div>
	</WebSocketProvider>
		{:else if browser}
	<!-- Loading state -->
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{/if}
