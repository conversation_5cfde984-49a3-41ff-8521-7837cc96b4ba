<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import SEO from '$lib/components/layout/SEO.svelte';
	import { authService } from '$lib/services/auth';
	import { authStore } from '$lib/stores/auth.svelte';
	import { LogCategory, logger } from '$lib/utils/logger';

	const { data, form } = $props();

	// ✅ Client state management
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let recaptchaToken = $state<string | null>(null);
	let recaptchaError = $state<string | null>(null);

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Derived values for translations
	const pageTitle = $derived(browser ? $t('auth.signin') : 'เข้าสู่ระบบ');
	const pageDescription = $derived(browser ? $t('auth.signinDescription') : 'เข้าสู่ระบบเพื่อเข้าถึงระบบจัดการเว็บไซต์');
	const emailLabel = $derived(browser ? $t('auth.email') : 'อีเมล');
	const passwordLabel = $derived(browser ? $t('auth.password') : 'รหัสผ่าน');
	const rememberMeText = $derived(browser ? $t('auth.rememberMe') : 'จดจำฉัน');
	const forgotPasswordText = $derived(browser ? $t('auth.forgotPassword') : 'ลืมรหัสผ่าน?');
	const signinButtonText = $derived(isLoading ? 'กำลังเข้าสู่ระบบ...' : (browser ? $t('auth.signin') : 'เข้าสู่ระบบ'));
	const noAccountText = $derived(browser ? $t('auth.noAccount') : 'ยังไม่มีบัญชี?');
	const signupText = $derived(browser ? $t('auth.signup') : 'สมัครสมาชิก');

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			showSuccess('เข้าสู่ระบบสำเร็จ!', formResult.message);
			// Redirect จะทำโดย server
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			showSuccess('เข้าสู่ระบบสำเร็จ!', result.data?.message);
			// Redirect จะทำโดย server
		} else if (result.type === 'failure') {
			showError('เข้าสู่ระบบไม่สำเร็จ', result.data?.message);
		}
	}

	// ✅ ลบ token เก่าจาก localStorage (ถ้ามี)
	function clearOldTokens() {
		if (browser) {
			const oldToken = localStorage.getItem('auth_token');
			if (oldToken) {
				console.log('Clearing old token from localStorage');
				localStorage.removeItem('auth_token');
			}
		}
	}

	// ✅ Clear old tokens on mount
	onMount(() => {
		clearOldTokens();
	});

	// ✅ Show success message
	function showSuccess(title: string, message?: string) {
		// ใช้ SweetAlert หรือ toast library ที่คุณใช้
		console.log('Success:', title, message);
	}

	// ✅ Show error message
	function showError(title: string, message?: string) {
		// ใช้ SweetAlert หรือ toast library ที่คุณใช้
		console.error('Error:', title, message);
	}
</script>

<SEO title={pageTitle} />

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
	<div class="w-full max-w-md">
		<!-- Header -->
		<div class="text-center mb-8">
			<h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
				{pageTitle}
			</h1>
			<p class="text-gray-600 dark:text-gray-400">
				{pageDescription}
			</p>
		</div>

		<!-- Error Alert -->
		{#if error}
			<div class="alert alert-error mb-6">
				<Icon icon="mdi:alert-circle" class="w-6 h-6" />
				<span>{error}</span>
			</div>
		{/if}

		<!-- Form -->
		<form
			method="POST"
			action="?/signin"
			use:enhance={() => {
				isLoading = true;
				error = null;

				return async ({ result, update }) => {
					isLoading = false;

					if (result.type === 'success') {
						// Success will redirect automatically
						await update();
					} else if (result.type === 'failure') {
						// Handle error - update will show the error
						await update();
					}
				};
			}}
			class="space-y-6"
		>
			<!-- Email -->
			<div class="form-control">
				<label class="label" for="email">
					<span class="label-text">{emailLabel}</span>
				</label>
				<input
					type="email"
					id="email"
					name="email"
					required
					class="input input-bordered w-full"
					placeholder="<EMAIL>"
				/>
			</div>

			<!-- Password -->
			<div class="form-control">
				<label class="label" for="password">
					<span class="label-text">{passwordLabel}</span>
				</label>
				<input
					type="password"
					id="password"
					name="password"
					required
					class="input input-bordered w-full"
					placeholder="••••••••"
				/>
			</div>

			<!-- Remember Me & Forgot Password -->
			<div class="flex items-center justify-between">
				<label class="label cursor-pointer">
					<input type="checkbox" name="rememberMe" class="checkbox checkbox-primary" />
					<span class="label-text ml-2">{rememberMeText}</span>
				</label>
				<a href="/forgot-password" class="text-sm text-primary hover:underline">
					{forgotPasswordText}
				</a>
			</div>

			<!-- Submit Button -->
			<button
				type="submit"
				disabled={isLoading}
				class="btn btn-primary w-full"
			>
				{#if isLoading}
					<span class="loading loading-spinner loading-sm"></span>
				{/if}
				{signinButtonText}
			</button>
		</form>

		<!-- Sign Up Link -->
		<div class="text-center mt-6">
			<span class="text-base-content/60">{noAccountText} </span>
			<a href="/signup" class="text-primary hover:underline font-medium">
				{signupText}
			</a>
		</div>
	</div>
</div>
