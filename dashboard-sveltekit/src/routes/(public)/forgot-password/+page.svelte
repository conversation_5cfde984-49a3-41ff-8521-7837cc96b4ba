<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { t } from 'svelte-i18n';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { authStore } from '$lib/stores/auth.svelte';
	import { LogCategory, logger } from '$lib/utils/logger';
	import SEO from '$lib/components/ui/SEO.svelte';
	import RecaptchaV3 from '$lib/components/ui/RecaptchaV3.svelte';

	const { data, form } = $props();

	// ✅ Client state management
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let recaptchaToken = $state<string | null>(null);
	let recaptchaError = $state<string | null>(null);

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			showSuccess('ส่งอีเมลรีเซ็ตรหัสผ่านสำเร็จ!', formResult.message);
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			showSuccess('ส่งอีเมลรีเซ็ตรหัสผ่านสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('ส่งอีเมลรีเซ็ตรหัสผ่านไม่สำเร็จ', result.data?.message);
		}
	}

	// ✅ Show success message
	function showSuccess(title: string, message?: string) {
		// ใช้ SweetAlert หรือ toast library ที่คุณใช้
		console.log('Success:', title, message);
	}

	// ✅ Show error message
	function showError(title: string, message?: string) {
		// ใช้ SweetAlert หรือ toast library ที่คุณใช้
		console.error('Error:', title, message);
	}
</script>

<SEO title={browser ? $t('auth.forgotPasswordTitle') : 'ลืมรหัสผ่าน'} />

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
	<div class="w-full max-w-md">
		<!-- Header -->
		<div class="text-center mb-8">
			<h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
				{browser ? $t('auth.forgotPasswordTitle') : 'ลืมรหัสผ่าน'}?
			</h1>
			<p class="text-gray-600 dark:text-gray-400">
				{browser ? $t('auth.forgotPasswordDescription') : 'กรอกอีเมลของคุณเพื่อรับลิงก์รีเซ็ตรหัสผ่าน'}
			</p>
		</div>

		<!-- Error Alert -->
		{#if error}
			<div class="alert alert-error mb-6">
				<Icon icon="mdi:alert-circle" class="w-6 h-6" />
				<span>{error}</span>
			</div>
		{/if}

		<!-- Form -->
		<form
			method="POST"
			action="?/forgotPassword"
			use:enhance={() => {
				isLoading = true;
				error = null;

				return async ({ result, update }) => {
					isLoading = false;

					if (result.type === 'success') {
						// Success will redirect automatically
						await update();
					} else if (result.type === 'failure') {
						// Handle error - update will show the error
						await update();
					}
				};
			}}
			class="space-y-6"
		>
			<!-- Email -->
			<div class="form-control">
				<label class="label" for="email">
					<span class="label-text">{browser ? $t('auth.email') : 'อีเมล'}</span>
				</label>
				<input
					type="email"
					id="email"
					name="email"
					required
					class="input input-bordered w-full"
					placeholder="<EMAIL>"
				/>
			</div>

			<!-- Submit Button -->
			<button
				type="submit"
				disabled={isLoading}
				class="btn btn-primary w-full"
			>
				{#if isLoading}
					<span class="loading loading-spinner loading-sm"></span>
				{/if}
				{browser ? $t('auth.sendResetEmail') : 'ส่งอีเมลรีเซ็ตรหัสผ่าน'}
			</button>
		</form>

		<!-- Sign In Link -->
		<div class="text-center mt-6">
			<span class="text-base-content/60">{browser ? $t('auth.rememberPassword') : 'จำรหัสผ่านได้แล้ว?'} </span>
			<a href="/signin" class="text-primary hover:underline font-medium">
				{browser ? $t('auth.signin') : 'เข้าสู่ระบบ'}
			</a>
		</div>
	</div>
</div>
