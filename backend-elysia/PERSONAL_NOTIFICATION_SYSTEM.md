# ระบบการแจ้งเตือนส่วนตัว (Personal Notification System)

## ภาพรวม

ปรับปรุงระบบการแจ้งเตือนให้รองรับการแจ้งเตือนส่วนตัวที่ไม่ต้องมี siteId โดย NotificationBell จะแสดงการแจ้งเตือนของ user ทั้งหมด รวมทั้ง:

- การแจ้งเตือนของเว็บไซต์ (site notifications)
- การแจ้งเตือนส่วนตัว (personal notifications)
- คำเชิญเข้าร่วมเว็บไซต์
- การอัปเดตบัญชี
- การแจ้งเตือนความปลอดภัย
- การบำรุงรักษาระบบ

## การเปลี่ยนแปลงที่ทำ

### 1. Backend Changes

#### `src/modules/notification/notification.model.ts`

- **siteId เป็น optional** ใน INotification, INotificationTemplate, INotificationSettings, INotificationRule
- รองรับการแจ้งเตือนส่วนตัวที่ไม่มี siteId

#### `src/modules/notification/notification.service.ts`

- **createPersonalNotification()** - สร้างการแจ้งเตือนส่วนตัว
- **getUserNotifications()** - ดึงการแจ้งเตือนของ user (รวมทั้ง site และ personal)
- **getUserUnreadCount()** - ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่านของ user
- **notifySiteInvitation()** - การแจ้งเตือนคำเชิญเข้าร่วมเว็บไซต์
- **notifyAccountUpdate()** - การแจ้งเตือนอัปเดตบัญชี
- **notifySecurityAlert()** - การแจ้งเตือนความปลอดภัย
- **notifySystemMaintenance()** - การแจ้งเตือนการบำรุงรักษา

#### `src/modules/notification/notification.routes.ts`

- **GET /user** - ดึงการแจ้งเตือนส่วนตัว
- **GET /user/unread-count** - ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่าน
- ปรับปรุง WebSocket authentication ให้ไม่ต้องใช้ siteId

#### `src/modules/notification/websocket.service.ts`

- **siteId เป็น optional** ใน NotificationConnection
- รองรับการเชื่อมต่อแบบ global (ไม่มี siteId)

### 2. Frontend Changes

#### `dashboard-sveltekit/src/lib/stores/notification.svelte.ts`

- **loadUserNotifications()** - โหลดการแจ้งเตือนส่วนตัว
- **loadUserUnreadCount()** - โหลดจำนวนการแจ้งเตือนที่ยังไม่อ่าน

#### `dashboard-sveltekit/src/lib/services/notification.ts`

- **getUserNotifications()** - API call สำหรับการแจ้งเตือนส่วนตัว
- **getUserUnreadCount()** - API call สำหรับจำนวนการแจ้งเตือนที่ยังไม่อ่าน

#### `dashboard-sveltekit/src/lib/components/layout/NotificationBell.svelte`

- ใช้ **loadUserUnreadCount()** แทน loadUnreadCount()
- ใช้ **loadUserNotifications()** แทน loadNotifications()
- ไม่ต้องรอ site พร้อม

#### `dashboard-sveltekit/src/lib/stores/websocket.svelte.ts`

- ไม่ต้องใช้ siteId ในการเชื่อมต่อ
- เชื่อมต่อเมื่อ authenticated เท่านั้น

#### `dashboard-sveltekit/src/lib/components/providers/WebSocketProvider.svelte`

- ไม่ต้องรอ site พร้อม
- เชื่อมต่อเมื่อ authenticated เท่านั้น

## ประเภทการแจ้งเตือนส่วนตัว

### 1. คำเชิญเข้าร่วมเว็บไซต์

```typescript
notifySiteInvitation(userId, siteName, inviterName, siteId);
```

### 2. การอัปเดตบัญชี

```typescript
notifyAccountUpdate(userId, updateType, details);
```

### 3. การแจ้งเตือนความปลอดภัย

```typescript
notifySecurityAlert(userId, alertType, details);
```

### 4. การบำรุงรักษาระบบ

```typescript
notifySystemMaintenance(userId, maintenanceInfo);
```

## การใช้งาน

### 1. การแจ้งเตือนส่วนตัว

```typescript
// สร้างการแจ้งเตือนส่วนตัว
await notificationService.createPersonalNotification(userId, {
  type: 'membership',
  title: 'คำเชิญเข้าร่วมเว็บไซต์',
  message: 'คุณได้รับคำเชิญเข้าร่วมเว็บไซต์',
  priority: 'high',
  status: 'unread',
  channels: {
    inApp: true,
    email: true,
    push: false,
    sms: false,
  },
});
```

### 2. การดึงการแจ้งเตือนส่วนตัว

```typescript
// ดึงการแจ้งเตือนส่วนตัว
const result = await notificationService.getUserNotifications(userId, {
  page: 1,
  limit: 20,
  type: 'membership',
  status: 'unread',
});
```

### 3. การดึงจำนวนการแจ้งเตือนที่ยังไม่อ่าน

```typescript
// ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่าน
const count = await notificationService.getUserUnreadCount(userId);
```

## WebSocket Integration

### 1. การเชื่อมต่อ

- ไม่ต้องใช้ siteId
- เชื่อมต่อเมื่อ authenticated เท่านั้น
- รองรับการแจ้งเตือนส่วนตัว

### 2. การส่งการแจ้งเตือน

```typescript
// ส่งการแจ้งเตือนผ่าน WebSocket
sendNotificationToUser(userId, notification);
sendUnreadCountToUser(userId, unreadCount);
```

## ประโยชน์ที่ได้

### 1. การทำงานที่ครอบคลุม

- แสดงการแจ้งเตือนทั้งหมดของ user
- รองรับการแจ้งเตือนส่วนตัว
- ไม่ต้องรอ site พร้อม

### 2. User Experience ที่ดีขึ้น

- การแจ้งเตือนที่ครบถ้วน
- ไม่มีการรอที่ยาวนาน
- สถานะที่ชัดเจน

### 3. Developer Experience ที่ดีขึ้น

- API ที่ง่ายต่อการใช้งาน
- การแยกประเภทที่ชัดเจน
- การ debug ที่ง่าย

## การทดสอบ

### 1. ตรวจสอบ Console Logs

```
🔍 WebSocket check: {isAuthenticated: true, connected: false, error: null}
🔌 Auto-connecting WebSocket...
🔌 Notification WebSocket connected
🔌 Notification WebSocket authenticated: userId (user)
```

### 2. ตรวจสอบ UI

- NotificationBell แสดงจำนวนการแจ้งเตือนที่ถูกต้อง
- ไม่มีการรอที่ยาวนาน
- WebSocket status indicator ทำงานถูกต้อง

### 3. ตรวจสอบ Functionality

- การแจ้งเตือนส่วนตัวทำงาน
- การแจ้งเตือนของ site ทำงาน
- Real-time updates ทำงาน

## หมายเหตุ

- การแจ้งเตือนส่วนตัวไม่มี siteId
- WebSocket ไม่ต้องรอ site พร้อม
- NotificationBell แสดงการแจ้งเตือนทั้งหมดของ user
- รองรับ Token Rotation System
