// สร้าง discount ทดสอบ
const API_URL = 'http://localhost:5000';

async function createTestDiscount() {
  try {
    console.log('🧪 สร้าง discount ทดสอบ');
    const response = await fetch(`${API_URL}/v1/discount/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify({
        name: 'ส่วนลดทดสอบ',
        description: 'ส่วนลดสำหรับทดสอบระบบ',
        code: 'TEST123',
        type: 'percentage',
        value: 10,
        maxDiscountAmount: 100,
        target: 'package',
        conditions: {
          minOrderAmount: 500,
          maxOrderAmount: 10000,
          usageLimit: 100,
          usageLimitPerUser: 1
        },
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 วัน
      })
    });

    const result = await response.json();
    console.log('ผลลัพธ์:', result);

  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาด:', error);
  }
}

createTestDiscount(); 