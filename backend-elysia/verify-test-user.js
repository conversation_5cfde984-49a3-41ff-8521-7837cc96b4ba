#!/usr/bin/env bun

/**
 * สคริปต์สำหรับยืนยันอีเมลของผู้ใช้ทดสอบ
 */

import mongoose from 'mongoose';

// เชื่อมต่อฐานข้อมูล
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/webshop';

async function main() {
  try {
    console.log('🔌 เชื่อมต่อฐานข้อมูล...');
    await mongoose.connect(MONGODB_URI);

    // สร้าง User schema
    const userSchema = new mongoose.Schema({
      email: String,
      isEmailVerified: Boolean,
    });

    const User = mongoose.model('User', userSchema);

    // ยืนยันอีเมลของผู้ใช้ทดสอบ
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    for (const email of testEmails) {
      console.log(`📧 ยืนยันอีเมล: ${email}`);

      const result = await User.updateOne(
        { email },
        { $set: { isEmailVerified: true } },
      );

      if (result.matchedCount > 0) {
        console.log(`✅ ยืนยันอีเมลสำเร็จ: ${email}`);
      }
      else {
        console.log(`❌ ไม่พบผู้ใช้: ${email}`);
      }
    }

    console.log('✅ เสร็จสิ้น');
  }
  catch (error) {
    console.error('❌ เกิดข้อผิดพลาด:', error.message);
  }
  finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

main();
