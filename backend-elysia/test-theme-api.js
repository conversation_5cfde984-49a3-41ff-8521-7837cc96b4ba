// Test Theme API
const API_BASE = 'http://localhost:3000';

async function testThemeAPI() {
  console.log('🧪 Testing Theme API...\n');

  try {
    // Test 1: Get theme settings
    console.log('1. Testing GET /site/test-site/theme');
    const getResponse = await fetch(`${API_BASE}/site/test-site/theme`);
    const getData = await getResponse.json();
    console.log('Response:', JSON.stringify(getData, null, 2));
    console.log('✅ GET theme successful\n');

    // Test 2: Update theme settings
    console.log('2. Testing PUT /site/test-site/theme');
    const themeData = {
      siteId: 'test-site',
      layout: 'grid',
      headerStyle: 'centered',
      footerStyle: 'simple',
      showSearch: true,
      showLanguageSelector: true,
      showThemeToggle: true,
      mobileMenuStyle: 'slide',
      desktopMenuStyle: 'horizontal',
      primaryColor: '#3b82f6',
      secondaryColor: '#6b7280',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      accentColor: '#f59e0b',
      fontFamily: 'Inter',
      fontSize: '16px',
      lineHeight: '1.6',
      fontWeight: '400',
      containerPadding: '1rem',
      sectionSpacing: '2rem',
      elementSpacing: '1rem',
      borderRadius: '0.5rem',
      spacing: '1rem',
      customCSS: '',
      isActive: true,
    };

    const putResponse = await fetch(`${API_BASE}/site/test-site/theme`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(themeData),
    });
    const putData = await putResponse.json();
    console.log('Response:', JSON.stringify(putData, null, 2));
    console.log('✅ PUT theme successful\n');

    // Test 3: Reset theme settings
    console.log('3. Testing POST /site/test-site/theme/reset');
    const resetResponse = await fetch(`${API_BASE}/site/test-site/theme/reset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const resetData = await resetResponse.json();
    console.log('Response:', JSON.stringify(resetData, null, 2));
    console.log('✅ Reset theme successful\n');

    console.log('🎉 All theme API tests passed!');
  }
  catch (error) {
    console.error('❌ Theme API test failed:', error);
  }
}

// Run the test
testThemeAPI();
