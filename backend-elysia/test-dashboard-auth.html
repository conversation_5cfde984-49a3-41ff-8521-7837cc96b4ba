<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Dashboard Authentication</h1>
    
    <div>
        <button onclick="testDashboard()">Test /dashboard</button>
        <button onclick="testDashboardWithId()">Test /dashboard/mdvpsme7AqiTc</button>
        <button onclick="testSignin()">Test /signin</button>
        <button onclick="checkCookies()">Check Cookies</button>
        <button onclick="clearCookies()">Clear Cookies</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        async function testDashboard() {
            try {
                const response = await fetch('/dashboard', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                addResult(`Dashboard response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (response.redirected) {
                    addResult(`Redirected to: ${response.url}`, 'info');
                }
            } catch (error) {
                addResult(`Dashboard error: ${error.message}`, 'error');
            }
        }

        async function testDashboardWithId() {
            try {
                const response = await fetch('/dashboard/mdvpsme7AqiTc', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                addResult(`Dashboard with ID response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (response.redirected) {
                    addResult(`Redirected to: ${response.url}`, 'info');
                }
            } catch (error) {
                addResult(`Dashboard with ID error: ${error.message}`, 'error');
            }
        }

        async function testSignin() {
            try {
                const response = await fetch('/signin', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                addResult(`Signin response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
            } catch (error) {
                addResult(`Signin error: ${error.message}`, 'error');
            }
        }

        function checkCookies() {
            const cookies = document.cookie.split(';').map(c => c.trim());
            addResult(`Cookies found: ${cookies.length}`, 'info');
            
            cookies.forEach(cookie => {
                const [name, value] = cookie.split('=');
                if (name === 'auth_token' || name === 'refreshToken' || name === 'session_id') {
                    addResult(`${name}: ${value ? 'exists' : 'empty'}`, 'info');
                }
            });
            
            if (cookies.length === 0 || !cookies.some(c => c.startsWith('auth_token'))) {
                addResult('No auth_token found - you need to login first!', 'error');
            }
        }

        function clearCookies() {
            document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            addResult('Cookies cleared', 'success');
        }

        // Auto-check cookies on load
        window.onload = function() {
            checkCookies();
        };
    </script>
</body>
</html>