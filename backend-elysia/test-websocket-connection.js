#!/usr/bin/env bun

/**
 * ทดสอบการเชื่อมต่อ WebSocket หลังจากแก้ไข CSP
 */

console.log('🚀 ทดสอบการเชื่อมต่อ WebSocket หลังจากแก้ไข CSP');

// Test WebSocket connection
function testWebSocketConnection() {
  return new Promise(resolve => {
    const wsUrl = 'ws://localhost:5000/v1/notifications/ws';
    console.log(`🔌 เชื่อมต่อไปยัง: ${wsUrl}`);

    const ws = new WebSocket(wsUrl);
    let connected = false;

    const timeout = setTimeout(() => {
      if (!connected) {
        console.log('⏰ WebSocket connection timeout');
        ws.close();
        resolve(false);
      }
    }, 5000);

    ws.onopen = () => {
      connected = true;
      clearTimeout(timeout);
      console.log('✅ WebSocket เชื่อมต่อสำเร็จ!');

      // ทดสอบส่งข้อความ
      ws.send(JSON.stringify({
        type: 'ping',
        data: { test: true },
      }));

      setTimeout(() => {
        ws.close();
        resolve(true);
      }, 1000);
    };

    ws.onmessage = event => {
      console.log(`📨 ได้รับข้อความ: ${event.data}`);
    };

    ws.onerror = error => {
      clearTimeout(timeout);
      console.log(`❌ WebSocket Error:`, error);
      resolve(false);
    };

    ws.onclose = event => {
      console.log(`🔌 WebSocket ปิดการเชื่อมต่อ: ${event.code}`);
      if (!connected) {
        resolve(false);
      }
    };
  });
}

// รันการทดสอบ
testWebSocketConnection().then(success => {
  if (success) {
    console.log('🎉 การทดสอบสำเร็จ! WebSocket ทำงานได้แล้ว');
  }
  else {
    console.log('❌ การทดสอบล้มเหลว! ยังมีปัญหาการเชื่อมต่อ');
  }
  process.exit(success ? 0 : 1);
});
