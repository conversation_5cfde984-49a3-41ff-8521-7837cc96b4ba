// ทดสอบ discount validation
const API_URL = 'http://localhost:5000';

async function testDiscountValidation() {
  try {
    // ทดสอบ 1: ไม่มี token (anonymous user)
    console.log('🧪 ทดสอบ 1: ไม่มี token (anonymous user)');
    const response1 = await fetch(`${API_URL}/v1/discount/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        discountCode: 'WELCOME10',
        siteId: 'test-site-id',
        target: 'package',
        orderAmount: 1000,
        items: []
      })
    });

    const result1 = await response1.json();
    console.log('ผลลัพธ์ 1:', result1);

    // ทดสอบ 2: มี token (authenticated user)
    console.log('\n🧪 ทดสอบ 2: มี token (authenticated user)');
    const response2 = await fetch(`${API_URL}/v1/discount/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify({
        discountCode: 'WELCOME10',
        siteId: 'test-site-id',
        target: 'package',
        orderAmount: 1000,
        items: []
      })
    });

    const result2 = await response2.json();
    console.log('ผลลัพธ์ 2:', result2);

  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาด:', error);
  }
}

testDiscountValidation(); 