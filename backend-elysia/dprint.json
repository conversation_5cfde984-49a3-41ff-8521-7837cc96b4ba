{"$schema": "https://dprint.dev/schemas/v0.json", "projectType": "openSource", "incremental": true, "typescript": {"quoteStyle": "preferSingle", "semiColons": "always", "trailingCommas": "onlyMultiLine", "useBraces": "whenNotSingleLine", "bracePosition": "sameLineUnlessHanging", "singleBodyPosition": "maintain", "nextControlFlowPosition": "nextLine", "operatorPosition": "nextLine", "arrowFunction.useParentheses": "preferNone", "conditionalExpression.linePerExpression": false, "enumDeclaration.memberSpacing": "maintain", "memberExpression.linePerExpression": false, "binaryExpression.linePerExpression": false, "jsx.quoteStyle": "preferDouble", "jsx.multiLineParens": "prefer", "jsx.forceNewLinesSurroundingContent": false, "jsx.bracketPosition": "nextLine"}, "json": {"indentWidth": 2}, "markdown": {"lineWidth": 80}, "toml": {}, "dockerfile": {}, "includes": ["**/*.{ts,tsx,js,jsx,json,md,toml,docker<PERSON>le,Dockerfile}"], "excludes": ["node_modules", "build", "dist", "coverage", "logs", "public/uploads", "dashboard-sveltekit", "*.lock", "*.lockb", "bun.lock", ".git"], "plugins": ["https://plugins.dprint.dev/typescript-0.95.0.wasm", "https://plugins.dprint.dev/json-0.19.3.wasm", "https://plugins.dprint.dev/markdown-0.17.8.wasm", "https://plugins.dprint.dev/toml-0.6.3.wasm", "https://plugins.dprint.dev/dockerfile-0.3.2.wasm"]}