<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug WebSocket Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .connection-test {
            border: 2px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .connection-test.connected {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .connection-test.error {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .connection-test.connecting {
            border-color: #ffc107;
            background-color: #fffef8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug WebSocket Connection</h1>
        
        <div class="status info">
            <strong>วัตถุประสงค์:</strong><br>
            ตรวจสอบและแก้ไขปัญหา WebSocket connection ระหว่าง WebSocketStatus และ NotificationBell
        </div>

        <div>
            <button onclick="startTest()">🚀 เริ่มทดสอบ</button>
            <button onclick="stopTest()">⏹️ หยุดทดสอบ</button>
            <button onclick="clearLog()">🗑️ ล้าง Log</button>
        </div>

        <div id="status" class="status info">
            <strong>สถานะ:</strong> พร้อมทดสอบ
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h3>🔌 WebSocket Connection Tests</h3>
            <div id="connections"></div>
        </div>

        <div class="container">
            <h3>📋 Debug Log</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        let testInterval = null;
        let connections = [];
        let testCount = 0;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<strong>สถานะ:</strong> ${message}`;
            statusDiv.className = `status ${type}`;
        }

        function createConnectionTest(id, name) {
            const connectionsDiv = document.getElementById('connections');
            const connectionDiv = document.createElement('div');
            connectionDiv.id = `connection-${id}`;
            connectionDiv.className = 'connection-test';
            connectionDiv.innerHTML = `
                <h4>${name}</h4>
                <div class="status-indicator">
                    <span class="status-text">กำลังเตรียม...</span>
                    <div class="status-details"></div>
                </div>
                <button onclick="reconnectConnection(${id})">🔄 เชื่อมต่อใหม่</button>
            `;
            connectionsDiv.appendChild(connectionDiv);
            return connectionDiv;
        }

        function updateConnectionStatus(id, status, message, details = '') {
            const connectionDiv = document.getElementById(`connection-${id}`);
            if (!connectionDiv) return;

            connectionDiv.className = `connection-test ${status}`;
            const statusText = connectionDiv.querySelector('.status-text');
            const statusDetails = connectionDiv.querySelector('.status-details');
            
            statusText.textContent = message;
            statusDetails.innerHTML = details;
        }

        function testWebSocketConnection(id, name, token = null) {
            return new Promise((resolve) => {
                const wsUrl = 'ws://localhost:5000/v1/notifications/ws';
                log(`🔌 [${name}] เชื่อมต่อไปยัง: ${wsUrl}`);
                
                const ws = new WebSocket(wsUrl);
                let connected = false;
                let authenticated = false;
                
                const connection = {
                    id,
                    name,
                    ws,
                    connected: false,
                    authenticated: false,
                    error: null
                };

                const timeout = setTimeout(() => {
                    if (!connected) {
                        log(`⏰ [${name}] Connection timeout`, 'warning');
                        updateConnectionStatus(id, 'error', 'Connection Timeout', 'เชื่อมต่อใช้เวลานานเกินไป');
                        ws.close();
                        resolve(connection);
                    }
                }, 10000);

                ws.onopen = () => {
                    connected = true;
                    connection.connected = true;
                    clearTimeout(timeout);
                    log(`✅ [${name}] WebSocket เชื่อมต่อสำเร็จ!`, 'success');
                    updateConnectionStatus(id, 'connecting', 'เชื่อมต่อแล้ว', 'กำลังยืนยันตัวตน...');

                    // ทดสอบการยืนยันตัวตน
                    if (token) {
                        ws.send(JSON.stringify({
                            type: 'auth',
                            data: { token }
                        }));
                    } else {
                        // ส่ง ping ทดสอบ
                        ws.send(JSON.stringify({ type: 'ping' }));
                    }
                };

                ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        log(`📨 [${name}] ได้รับข้อความ: ${message.type}`, 'info');

                        switch (message.type) {
                            case 'auth_success':
                                authenticated = true;
                                connection.authenticated = true;
                                log(`✅ [${name}] การยืนยันตัวตนสำเร็จ`, 'success');
                                updateConnectionStatus(id, 'connected', 'เชื่อมต่อและยืนยันตัวตนแล้ว', 
                                    `User: ${message.data?.userId || 'N/A'}<br>Type: ${message.data?.userType || 'N/A'}`);
                                break;

                            case 'auth_error':
                                log(`❌ [${name}] การยืนยันตัวตนล้มเหลว: ${message.message}`, 'error');
                                updateConnectionStatus(id, 'error', 'การยืนยันตัวตนล้มเหลว', message.message);
                                break;

                            case 'pong':
                                log(`🏓 [${name}] ได้รับ pong`, 'success');
                                updateConnectionStatus(id, 'connected', 'เชื่อมต่อแล้ว (ไม่ได้ยืนยันตัวตน)', 'Ping/Pong ทำงานได้');
                                break;

                            case 'unread_count':
                                log(`📊 [${name}] อัปเดตจำนวนที่ยังไม่อ่าน: ${message.data?.count}`, 'info');
                                break;

                            case 'error':
                                log(`❌ [${name}] WebSocket Error: ${message.message}`, 'error');
                                connection.error = message.message;
                                updateConnectionStatus(id, 'error', 'เกิดข้อผิดพลาด', message.message);
                                break;
                        }
                    } catch (error) {
                        log(`❌ [${name}] Error parsing message: ${error.message}`, 'error');
                    }
                };

                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    log(`❌ [${name}] WebSocket Error:`, 'error');
                    connection.error = 'Connection error';
                    updateConnectionStatus(id, 'error', 'เกิดข้อผิดพลาดในการเชื่อมต่อ', 'ตรวจสอบ network หรือ server');
                    resolve(connection);
                };

                ws.onclose = (event) => {
                    clearTimeout(timeout);
                    log(`🔌 [${name}] WebSocket ปิดการเชื่อมต่อ: ${event.code} - ${event.reason}`);
                    
                    if (connected && (authenticated || event.code === 1000)) {
                        updateConnectionStatus(id, 'success', 'ทดสอบสำเร็จ', 
                            `เชื่อมต่อและ${authenticated ? 'ยืนยันตัวตน' : 'ทำงาน'}ได้ปกติ`);
                    } else if (!connected) {
                        updateConnectionStatus(id, 'error', 'เชื่อมต่อไม่ได้', `Code: ${event.code}`);
                    }
                    
                    resolve(connection);
                };

                connections[id] = connection;
            });
        }

        async function reconnectConnection(id) {
            const connection = connections[id];
            if (!connection) return;

            log(`🔄 [${connection.name}] กำลังเชื่อมต่อใหม่...`);
            updateConnectionStatus(id, 'connecting', 'กำลังเชื่อมต่อใหม่...', '');

            if (connection.ws && connection.ws.readyState === WebSocket.OPEN) {
                connection.ws.close();
            }

            // รอสักครู่แล้วเชื่อมต่อใหม่
            setTimeout(async () => {
                await testWebSocketConnection(id, connection.name);
            }, 1000);
        }

        async function startTest() {
            log('🚀 เริ่มทดสอบ WebSocket Connection');
            updateStatus('กำลังทดสอบ...', 'info');

            // ล้าง connections เก่า
            document.getElementById('connections').innerHTML = '';
            connections = [];

            // สร้าง connection tests
            createConnectionTest(0, 'WebSocketStatus Test');
            createConnectionTest(1, 'NotificationBell Test (No Auth)');
            createConnectionTest(2, 'NotificationBell Test (With Auth)');

            // ทดสอบการเชื่อมต่อแบบต่างๆ
            const tests = [
                testWebSocketConnection(0, 'WebSocketStatus Test'),
                testWebSocketConnection(1, 'NotificationBell Test (No Auth)'),
                // ทดสอบกับ token (ถ้ามี)
                testWebSocketConnection(2, 'NotificationBell Test (With Auth)', getTestToken())
            ];

            try {
                const results = await Promise.all(tests);
                
                const successCount = results.filter(r => r.connected).length;
                const authCount = results.filter(r => r.authenticated).length;
                
                log(`📊 ผลการทดสอบ: ${successCount}/${results.length} เชื่อมต่อได้, ${authCount}/${results.length} ยืนยันตัวตนได้`, 'info');
                
                if (successCount === results.length) {
                    updateStatus('ทดสอบสำเร็จ - WebSocket ทำงานได้ปกติ', 'success');
                } else {
                    updateStatus(`ทดสอบบางส่วนล้มเหลว - ${successCount}/${results.length} เชื่อมต่อได้`, 'warning');
                }
                
            } catch (error) {
                log(`❌ เกิดข้อผิดพลาดในการทดสอบ: ${error.message}`, 'error');
                updateStatus('ทดสอบล้มเหลว', 'error');
            }
        }

        function stopTest() {
            log('⏹️ หยุดการทดสอบ');
            
            connections.forEach(connection => {
                if (connection && connection.ws && connection.ws.readyState === WebSocket.OPEN) {
                    connection.ws.close();
                }
            });
            
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
            }
            
            updateStatus('หยุดการทดสอบแล้ว', 'info');
        }

        function getTestToken() {
            // ลองหา token จาก localStorage หรือ cookies
            try {
                const localToken = localStorage.getItem('auth_token') || localStorage.getItem('accessToken');
                if (localToken) return localToken;

                const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                    const [key, value] = cookie.trim().split('=');
                    if (key && value) {
                        acc[key] = decodeURIComponent(value);
                    }
                    return acc;
                }, {});

                return cookies['auth_token'] || cookies['accessToken'] || null;
            } catch (e) {
                return null;
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            updateStatus('พร้อมทดสอบ', 'info');
        }

        // Auto-run initial test
        window.onload = function() {
            log('🌐 หน้าเว็บโหลดเสร็จแล้ว');
            log('💡 คลิก "เริ่มทดสอบ" เพื่อตรวจสอบ WebSocket connection');
        };
    </script>
</body>
</html>