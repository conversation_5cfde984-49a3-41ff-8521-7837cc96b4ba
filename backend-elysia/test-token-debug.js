#!/usr/bin/env bun

/**
 * ทดสอบและ debug token ใน cookies
 */

console.log('🔍 ทดสอบ Token Debug');

// Test login and check token
async function testTokenDebug() {
  console.log('\n1️⃣ ทดสอบ Login และ Token...');

  try {
    const response = await fetch('http://localhost:5000/v1/user/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ Login สำเร็จ');
      console.log('📋 Token Info:');
      console.log(`   Token: ${result.data.token ? 'มี' : 'ไม่มี'}`);
      console.log(`   Token Length: ${result.data.token?.length || 0}`);
      console.log(`   Token Preview: ${result.data.token?.substring(0, 20)}...`);

      // Test API call with token
      console.log('\n2️⃣ ทดสอบ API Call ด้วย Token...');

      const apiResponse = await fetch('http://localhost:5000/v1/notifications/user', {
        headers: {
          'Authorization': `Bearer ${result.data.token}`,
          'Content-Type': 'application/json',
        },
      });

      const apiResult = await apiResponse.json();

      if (apiResponse.ok) {
        console.log('✅ API Call สำเร็จ');
        console.log(`   Notifications: ${apiResult.data?.notifications?.length || 0} รายการ`);
      }
      else {
        console.log('❌ API Call ล้มเหลว:', apiResult.message);
      }

      // Simulate cookie storage
      console.log('\n3️⃣ จำลอง Cookie Storage...');

      // This is how the token should be stored in browser cookies
      const cookieValue = `auth_token=${result.data.token}; Path=/; HttpOnly; Secure; SameSite=Strict`;
      console.log('🍪 Cookie ที่ควรจะมี:');
      console.log(`   ${cookieValue.substring(0, 50)}...`);

      return true;
    }
    else {
      console.log('❌ Login ล้มเหลว:', result.message);
      return false;
    }
  }
  catch (error) {
    console.log('❌ Error:', error.message);
    return false;
  }
}

// รันการทดสอบ
testTokenDebug().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 Token Debug สำเร็จ!');
    console.log('\n💡 สำหรับ SvelteKit Dashboard:');
    console.log('1. ตรวจสอบว่า login ทำงานได้');
    console.log('2. ตรวจสอบว่า token ถูกเก็บใน cookies');
    console.log('3. ตรวจสอบว่า notification API ใช้ token ได้');
  }
  else {
    console.log('❌ Token Debug ล้มเหลว!');
    console.log('\n🔧 ตรวจสอบ:');
    console.log('1. Backend server ทำงานหรือไม่');
    console.log('2. User credentials ถูกต้องหรือไม่');
    console.log('3. Token generation ทำงานหรือไม่');
  }
  console.log('='.repeat(50));
  process.exit(success ? 0 : 1);
});
