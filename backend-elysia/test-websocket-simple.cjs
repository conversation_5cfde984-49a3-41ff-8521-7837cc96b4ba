const WebSocket = require('ws');

// ทดสอบการเชื่อมต่อ WebSocket แบบง่าย
function testWebSocketConnection() {
  console.log('🔌 ทดสอบการเชื่อมต่อ WebSocket...');
  
  const ws = new WebSocket('ws://localhost:5000/v1/notifications/ws');
  
  ws.on('open', () => {
    console.log('✅ WebSocket connected successfully');
    
    // ส่ง ping
    ws.send(JSON.stringify({ type: 'ping' }));
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('📨 Received:', message);
    } catch (error) {
      console.error('❌ Error parsing message:', error);
    }
  });
  
  ws.on('close', (code, reason) => {
    console.log('🔌 WebSocket closed:', code, reason);
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
  });
  
  // ปิดการเชื่อมต่อหลังจาก 5 วินาที
  setTimeout(() => {
    console.log('⏰ Closing connection...');
    ws.close(1000, 'Test completed');
  }, 5000);
}

// เริ่มการทดสอบ
if (require.main === module) {
  console.log('🚀 Starting simple WebSocket test...');
  testWebSocketConnection();
}

module.exports = { testWebSocketConnection }; 