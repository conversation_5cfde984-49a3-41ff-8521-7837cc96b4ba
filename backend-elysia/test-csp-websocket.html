<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ WebSocket CSP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 ทดสอบ WebSocket CSP</h1>
        
        <div class="status info">
            <strong>ข้อมูลการทดสอบ:</strong><br>
            WebSocket URL: ws://localhost:5000/v1/notifications/ws<br>
            วัตถุประสงค์: ทดสอบว่า CSP อนุญาตให้เชื่อมต่อ WebSocket ได้หรือไม่
        </div>

        <div>
            <button onclick="testWebSocket()">🔌 ทดสอบ WebSocket</button>
            <button onclick="clearLog()">🗑️ ล้าง Log</button>
        </div>

        <div id="status" class="status info">
            <strong>สถานะ:</strong> พร้อมทดสอบ
        </div>

        <h3>📋 Log:</h3>
        <div id="log"></div>
    </div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<strong>สถานะ:</strong> ${message}`;
            statusDiv.className = `status ${type}`;
        }

        function testWebSocket() {
            log('🚀 เริ่มทดสอบ WebSocket...');
            updateStatus('กำลังทดสอบ...', 'info');

            try {
                // ปิด connection เก่าถ้ามี
                if (ws) {
                    ws.close();
                }

                const wsUrl = 'ws://localhost:5000/v1/notifications/ws';
                log(`🔌 เชื่อมต่อไปยัง: ${wsUrl}`);
                
                ws = new WebSocket(wsUrl);

                // Timeout สำหรับการเชื่อมต่อ
                const connectionTimeout = setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('⏰ WebSocket connection timeout', 'error');
                        updateStatus('การเชื่อมต่อใช้เวลานานเกินไป', 'error');
                        ws.close();
                    }
                }, 10000);

                ws.onopen = function(event) {
                    clearTimeout(connectionTimeout);
                    log('✅ WebSocket เชื่อมต่อสำเร็จ!', 'success');
                    updateStatus('เชื่อมต่อสำเร็จ', 'success');
                    
                    // ทดสอบส่งข้อความ
                    setTimeout(() => {
                        log('📤 ส่งข้อความทดสอบ...');
                        ws.send(JSON.stringify({
                            type: 'ping',
                            data: { test: true }
                        }));
                    }, 1000);
                };

                ws.onmessage = function(event) {
                    log(`📨 ได้รับข้อความ: ${event.data}`, 'success');
                };

                ws.onerror = function(error) {
                    clearTimeout(connectionTimeout);
                    log(`❌ WebSocket Error: ${error}`, 'error');
                    updateStatus('เกิดข้อผิดพลาดในการเชื่อมต่อ', 'error');
                    
                    // ตรวจสอบ CSP error
                    console.error('WebSocket Error Details:', error);
                };

                ws.onclose = function(event) {
                    clearTimeout(connectionTimeout);
                    log(`🔌 WebSocket ปิดการเชื่อมต่อ: ${event.code} - ${event.reason}`);
                    
                    if (event.code === 1006) {
                        updateStatus('การเชื่อมต่อถูกปฏิเสธ (อาจเป็นปัญหา CSP)', 'error');
                    } else {
                        updateStatus('การเชื่อมต่อปิดแล้ว', 'info');
                    }
                };

            } catch (error) {
                log(`❌ JavaScript Error: ${error.message}`, 'error');
                updateStatus('เกิดข้อผิดพลาดใน JavaScript', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            updateStatus('พร้อมทดสอบ', 'info');
        }

        // ทดสอบอัตโนมัติเมื่อโหลดหน้า
        window.onload = function() {
            log('🌐 หน้าเว็บโหลดเสร็จแล้ว');
            log('💡 คลิกปุ่ม "ทดสอบ WebSocket" เพื่อเริ่มทดสอบ');
        };
    </script>
</body>
</html>