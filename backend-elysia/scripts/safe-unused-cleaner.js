#!/usr/bin/env node

/**
 * Safe Unused Code Cleaner
 * ลบ unused code อย่างปลอดภัย โดยเฉพาะ unused parameters
 */

import { ESLint } from 'eslint';
import fs from 'fs';
import path from 'path';

class SafeUnusedCleaner {
  constructor() {
    this.removedCount = 0;
    this.processedFiles = 0;
    this.modifiedFiles = 0;
  }

  /**
   * รัน ESLint และได้ข้อมูล unused code
   */
  async getUnusedCodeInfo() {
    console.log('🔍 กำลังวิเคราะห์ unused code ด้วย ESLint...');

    const eslint = new ESLint({
      overrideConfigFile: 'eslint.config.js',
    });

    const results = await eslint.lintFiles(['src/**/*.ts']);
    const unusedItems = [];

    for (const result of results) {
      for (const message of result.messages) {
        if (
          message.message.includes('never used')
          || message.message.includes('is defined but never used')
          || message.message.includes('is assigned a value but never used')
          || message.message.includes('is declared but its value is never read')
        ) {
          unusedItems.push({
            file: result.filePath,
            line: message.line,
            column: message.column,
            message: message.message,
            ruleId: message.ruleId,
            severity: message.severity,
          });
        }
      }
    }

    return unusedItems;
  }

  /**
   * เพิ่ม underscore prefix สำหรับ unused parameters (ปลอดภัย)
   */
  addUnderscorePrefix(content, unusedItems, filePath) {
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused parameters
    const unusedParams = unusedItems.filter(item =>
      item.file === filePath
      && (item.message.includes('is defined but never used')
        || item.message.includes('is declared but its value is never read'))
    );

    for (const unusedParam of unusedParams) {
      const lineIndex = unusedParam.line - 1;
      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];

        // Extract parameter name from message
        const paramMatch = unusedParam.message.match(/'([^']+)'/);
        if (!paramMatch) continue;

        const paramName = paramMatch[1];

        // เพิ่ม _ prefix ถ้ายังไม่มี
        if (!paramName.startsWith('_')) {
          const newLine = line.replace(
            new RegExp(`\\b${paramName}\\b`, 'g'),
            `_${paramName}`,
          );

          if (newLine !== line) {
            lines[lineIndex] = newLine;
            console.log(
              `  🔧 เพิ่ม _ prefix: ${paramName} -> _${paramName} ใน ${path.basename(filePath)}:${unusedParam.line}`,
            );
            modified = true;
            removedInThisFile++;
          }
        }
      }
    }

    return { content: lines.join('\n'), modified, removedCount: removedInThisFile };
  }

  /**
   * ลบ unused variables ที่ปลอดภัย
   */
  removeSafeUnusedVariables(content, unusedItems, filePath) {
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused variables ที่ปลอดภัยในการลบ
    const safeUnusedVars = unusedItems.filter(item =>
      item.file === filePath
      && item.message.includes('is assigned a value but never used')
    );

    for (const unusedVar of safeUnusedVars) {
      const lineIndex = unusedVar.line - 1;
      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];

        // ลบเฉพาะ variables ที่ชัดเจนว่าไม่ใช้
        if (
          (line.includes('const ') || line.includes('let '))
            && !line.includes('=')
          || line.includes('= null')
          || line.includes('= undefined')
          || line.includes('= ""')
          || line.includes("= ''")
        ) {
          console.log(`  🗑️  ลบ unused variable: ${line.trim()} ใน ${path.basename(filePath)}:${unusedVar.line}`);
          lines[lineIndex] = '';
          modified = true;
          removedInThisFile++;
        }
        // หรือเพิ่ม _ prefix สำหรับ variables
        else {
          const varMatch = unusedVar.message.match(/'([^']+)'/);
          if (varMatch) {
            const varName = varMatch[1];
            if (!varName.startsWith('_')) {
              const newLine = line.replace(
                new RegExp(`\\b${varName}\\b`),
                `_${varName}`,
              );

              if (newLine !== line) {
                lines[lineIndex] = newLine;
                console.log(
                  `  🔧 เพิ่ม _ prefix: ${varName} -> _${varName} ใน ${path.basename(filePath)}:${unusedVar.line}`,
                );
                modified = true;
                removedInThisFile++;
              }
            }
          }
        }
      }
    }

    return { content: lines.join('\n'), modified, removedCount: removedInThisFile };
  }

  /**
   * ลบ unused imports (ปลอดภัยที่สุด)
   */
  removeUnusedImports(content, unusedItems, filePath) {
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused imports
    const unusedImports = unusedItems.filter(item =>
      item.file === filePath
      && item.message.includes('imported but never used')
    );

    if (unusedImports.length === 0) {
      return { content, modified: false, removedCount: 0 };
    }

    // สร้าง set ของ unused import names
    const unusedImportNames = new Set();
    unusedImports.forEach(item => {
      const match = item.message.match(/'([^']+)' is imported but never used/);
      if (match) {
        unusedImportNames.add(match[1]);
      }
    });

    // ประมวลผลแต่ละบรรทัด
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.trim().startsWith('import ') && line.includes('from ')) {
        // ลบ unused imports จาก named imports
        if (line.includes('{') && line.includes('}')) {
          const importMatch = line.match(/import\s*{\s*([^}]+)\s*}\s*from\s*(['"][^'"]+['"])/);
          if (importMatch) {
            const imports = importMatch[1].split(',').map(imp => imp.trim());
            const usedImports = imports.filter(imp => {
              const cleanImp = imp.replace(/\s+as\s+\w+/, '').trim();
              return !unusedImportNames.has(cleanImp);
            });

            if (usedImports.length === 0) {
              lines[i] = '';
              modified = true;
              removedInThisFile++;
              console.log(`  ❌ ลบ import ทั้งหมด: ${importMatch[2]} ใน ${path.basename(filePath)}`);
            }
            else if (usedImports.length < imports.length) {
              lines[i] = `import { ${usedImports.join(', ')} } from ${importMatch[2]};`;
              modified = true;
              removedInThisFile++;
              console.log(`  🔧 ลบ unused imports บางตัวจาก ${importMatch[2]} ใน ${path.basename(filePath)}`);
            }
          }
        }
      }
    }

    return { content: lines.join('\n'), modified, removedCount: removedInThisFile };
  }

  /**
   * ประมวลผลไฟล์
   */
  async processFile(filePath, unusedItems) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let fileModified = false;
      let totalRemovedInFile = 0;

      // ขั้นตอนที่ 1: ลบ unused imports (ปลอดภัยที่สุด)
      const importResult = this.removeUnusedImports(newContent, unusedItems, filePath);
      if (importResult.modified) {
        newContent = importResult.content;
        fileModified = true;
        totalRemovedInFile += importResult.removedCount;
      }

      // ขั้นตอนที่ 2: เพิ่ม _ prefix สำหรับ unused parameters
      const paramResult = this.addUnderscorePrefix(newContent, unusedItems, filePath);
      if (paramResult.modified) {
        newContent = paramResult.content;
        fileModified = true;
        totalRemovedInFile += paramResult.removedCount;
      }

      // ขั้นตอนที่ 3: ลบ unused variables ที่ปลอดภัย
      const varResult = this.removeSafeUnusedVariables(newContent, unusedItems, filePath);
      if (varResult.modified) {
        newContent = varResult.content;
        fileModified = true;
        totalRemovedInFile += varResult.removedCount;
      }

      // เขียนไฟล์กลับถ้ามีการเปลี่ยนแปลง
      if (fileModified) {
        // ลบบรรทัดว่างที่เกินไป
        const cleanedContent = this.cleanupEmptyLines(newContent);
        fs.writeFileSync(filePath, cleanedContent);
        console.log(`✅ แก้ไข: ${path.relative(process.cwd(), filePath)} (${totalRemovedInFile} รายการ)`);
        this.modifiedFiles++;
      }

      this.removedCount += totalRemovedInFile;
      this.processedFiles++;
      return fileModified;
    }
    catch (error) {
      console.error(`❌ ข้อผิดพลาดในไฟล์ ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * ลบบรรทัดว่างที่เกินไป
   */
  cleanupEmptyLines(content) {
    const lines = content.split('\n');
    const cleanedLines = [];
    let consecutiveEmptyLines = 0;

    for (const line of lines) {
      if (line.trim() === '') {
        consecutiveEmptyLines++;
        if (consecutiveEmptyLines <= 1) {
          cleanedLines.push(line);
        }
      }
      else {
        consecutiveEmptyLines = 0;
        cleanedLines.push(line);
      }
    }

    return cleanedLines.join('\n');
  }

  /**
   * ประมวลผลไฟล์ทั้งหมด
   */
  async processAllFiles() {
    console.log('🛡️  Safe Unused Code Cleaner');
    console.log('============================\n');

    // รับข้อมูล unused code จาก ESLint
    const unusedItems = await this.getUnusedCodeInfo();

    if (unusedItems.length === 0) {
      console.log('🎉 ไม่พบ unused code ที่สามารถลบได้!');
      return;
    }

    console.log(`📋 พบ unused items: ${unusedItems.length} รายการ\n`);

    // จัดกลุ่มตามไฟล์
    const fileGroups = {};
    unusedItems.forEach(item => {
      if (!fileGroups[item.file]) {
        fileGroups[item.file] = [];
      }
      fileGroups[item.file].push(item);
    });

    console.log('🔧 กำลังลบ unused code อย่างปลอดภัย...\n');

    // ประมวลผลแต่ละไฟล์
    for (const [filePath, items] of Object.entries(fileGroups)) {
      console.log(`📁 ประมวลผล: ${path.relative(process.cwd(), filePath)}`);
      await this.processFile(filePath, items);
      console.log('');
    }

    // สรุปผลลัพธ์
    console.log('📊 สรุปผลลัพธ์:');
    console.log(`📁 ประมวลผล: ${this.processedFiles} ไฟล์`);
    console.log(`✅ แก้ไข: ${this.modifiedFiles} ไฟล์`);
    console.log(`🗑️  ลบ/แก้ไข unused items: ${this.removedCount} รายการ`);

    if (this.removedCount > 0) {
      console.log('\n✅ การทำความสะอาดเสร็จสิ้นอย่างปลอดภัย');
      console.log('💡 ระบบควรทำงานได้ปกติ เนื่องจากใช้วิธีการที่ปลอดภัย');
    }
  }
}

// Main function
async function main() {
  const cleaner = new SafeUnusedCleaner();
  await cleaner.processAllFiles();
}

// Check if this is the main module
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export default SafeUnusedCleaner;
