#!/usr/bin/env bun

/**
 * Advanced TypeScript Unused Code Cleaner
 * ใช้ TypeScript Compiler API เพื่อวิเคราะห์และลบ unused code
 */

import * as fs from 'fs';
import { glob } from 'glob';
import * as path from 'path';
import * as ts from 'typescript';

interface UnusedItem {
  file: string;
  name: string;
  line: number;
  column: number;
  type: 'variable' | 'function' | 'interface' | 'type' | 'import';
  canAutoRemove: boolean;
}

class TypeScriptUnusedCleaner {
  private program: ts.Program;
  private checker: ts.TypeChecker;
  private unusedItems: UnusedItem[] = [];

  constructor(private configPath: string = './tsconfig.json') {
    const config = ts.readConfigFile(configPath, ts.sys.readFile);
    const parsedConfig = ts.parseJsonConfigFileContent(
      config.config,
      ts.sys,
      path.dirname(configPath),
    );

    this.program = ts.createProgram(parsedConfig.fileNames, parsedConfig.options);
    this.checker = this.program.getTypeChecker();
  }

  /**
   * วิเคราะห์หา unused code ทั้งหมด
   */
  public analyzeUnusedCode(): UnusedItem[] {
    console.log('🔍 กำลังวิเคราะห์ TypeScript files...');

    this.unusedItems = [];

    for (const sourceFile of this.program.getSourceFiles()) {
      if (!sourceFile.isDeclarationFile && sourceFile.fileName.includes('/src/')) {
        this.analyzeSourceFile(sourceFile);
      }
    }

    return this.unusedItems;
  }

  private analyzeSourceFile(sourceFile: ts.SourceFile) {
    const visit = (node: ts.Node) => {
      // ตรวจสอบ variable declarations
      if (ts.isVariableDeclaration(node)) {
        this.checkVariableUsage(node, sourceFile);
      }

      // ตรวจสอบ function declarations
      if (ts.isFunctionDeclaration(node)) {
        this.checkFunctionUsage(node, sourceFile);
      }

      // ตรวจสอบ interface declarations
      if (ts.isInterfaceDeclaration(node)) {
        this.checkInterfaceUsage(node, sourceFile);
      }

      // ตรวจสอบ type alias declarations
      if (ts.isTypeAliasDeclaration(node)) {
        this.checkTypeUsage(node, sourceFile);
      }

      // ตรวจสอบ import declarations
      if (ts.isImportDeclaration(node)) {
        this.checkImportUsage(node, sourceFile);
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
  }

  private checkVariableUsage(node: ts.VariableDeclaration, sourceFile: ts.SourceFile) {
    if (!node.name || !ts.isIdentifier(node.name)) return;

    const symbol = this.checker.getSymbolAtLocation(node.name);
    if (!symbol) return;

    const references = this.checker.findReferences(symbol, [sourceFile]);
    const isUsed = references && references.some(ref => ref.references.length > 1 // มากกว่า 1 = มี declaration + usage
    );

    if (!isUsed) {
      const pos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
      this.unusedItems.push({
        file: sourceFile.fileName,
        name: node.name.text,
        line: pos.line + 1,
        column: pos.character + 1,
        type: 'variable',
        canAutoRemove: true,
      });
    }
  }

  private checkFunctionUsage(node: ts.FunctionDeclaration, sourceFile: ts.SourceFile) {
    if (!node.name) return;

    const symbol = this.checker.getSymbolAtLocation(node.name);
    if (!symbol) return;

    const references = this.checker.findReferences(symbol, [sourceFile]);
    const isUsed = references && references.some(ref => ref.references.length > 1);

    if (!isUsed) {
      const pos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
      this.unusedItems.push({
        file: sourceFile.fileName,
        name: node.name.text,
        line: pos.line + 1,
        column: pos.character + 1,
        type: 'function',
        canAutoRemove: !this.isExported(node),
      });
    }
  }

  private checkInterfaceUsage(node: ts.InterfaceDeclaration, sourceFile: ts.SourceFile) {
    const symbol = this.checker.getSymbolAtLocation(node.name);
    if (!symbol) return;

    const references = this.checker.findReferences(symbol, [sourceFile]);
    const isUsed = references && references.some(ref => ref.references.length > 1);

    if (!isUsed) {
      const pos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
      this.unusedItems.push({
        file: sourceFile.fileName,
        name: node.name.text,
        line: pos.line + 1,
        column: pos.character + 1,
        type: 'interface',
        canAutoRemove: !this.isExported(node),
      });
    }
  }

  private checkTypeUsage(node: ts.TypeAliasDeclaration, sourceFile: ts.SourceFile) {
    const symbol = this.checker.getSymbolAtLocation(node.name);
    if (!symbol) return;

    const references = this.checker.findReferences(symbol, [sourceFile]);
    const isUsed = references && references.some(ref => ref.references.length > 1);

    if (!isUsed) {
      const pos = sourceFile.getLineAndCharacterOfPosition(node.getStart());
      this.unusedItems.push({
        file: sourceFile.fileName,
        name: node.name.text,
        line: pos.line + 1,
        column: pos.character + 1,
        type: 'type',
        canAutoRemove: !this.isExported(node),
      });
    }
  }

  private checkImportUsage(node: ts.ImportDeclaration, sourceFile: ts.SourceFile) {
    if (!node.importClause) return;

    // ตรวจสอบ named imports
    if (node.importClause.namedBindings && ts.isNamedImports(node.importClause.namedBindings)) {
      for (const element of node.importClause.namedBindings.elements) {
        const symbol = this.checker.getSymbolAtLocation(element.name);
        if (!symbol) continue;

        const references = this.checker.findReferences(symbol, [sourceFile]);
        const isUsed = references && references.some(ref => ref.references.length > 1);

        if (!isUsed) {
          const pos = sourceFile.getLineAndCharacterOfPosition(element.getStart());
          this.unusedItems.push({
            file: sourceFile.fileName,
            name: element.name.text,
            line: pos.line + 1,
            column: pos.character + 1,
            type: 'import',
            canAutoRemove: true,
          });
        }
      }
    }
  }

  private isExported(node: ts.Node): boolean {
    return node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword) ?? false;
  }

  /**
   * สร้างรายงาน unused code
   */
  public generateReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalUnused: this.unusedItems.length,
        canAutoRemove: this.unusedItems.filter(item => item.canAutoRemove).length,
        byType: {
          variable: this.unusedItems.filter(item => item.type === 'variable').length,
          function: this.unusedItems.filter(item => item.type === 'function').length,
          interface: this.unusedItems.filter(item => item.type === 'interface').length,
          type: this.unusedItems.filter(item => item.type === 'type').length,
          import: this.unusedItems.filter(item => item.type === 'import').length,
        },
      },
      items: this.unusedItems,
    };

    fs.writeFileSync('ts-unused-report.json', JSON.stringify(report, null, 2));
    console.log('📄 สร้างรายงาน TypeScript unused code: ts-unused-report.json');
  }

  /**
   * แสดงสรุปผลลัพธ์
   */
  public printSummary(): void {
    console.log('\n📊 สรุป TypeScript Unused Code:');
    console.log(`🔍 ทั้งหมด: ${this.unusedItems.length} รายการ`);
    console.log(`✅ ลบได้อัตโนมัติ: ${this.unusedItems.filter(item => item.canAutoRemove).length} รายการ`);
    console.log(`⚠️  ต้องตรวจสอบด้วยมือ: ${this.unusedItems.filter(item => !item.canAutoRemove).length} รายการ`);

    const byType = {
      variable: this.unusedItems.filter(item => item.type === 'variable').length,
      function: this.unusedItems.filter(item => item.type === 'function').length,
      interface: this.unusedItems.filter(item => item.type === 'interface').length,
      type: this.unusedItems.filter(item => item.type === 'type').length,
      import: this.unusedItems.filter(item => item.type === 'import').length,
    };

    console.log('\n📋 แยกตามประเภท:');
    Object.entries(byType).forEach(([type, count]) => {
      if (count > 0) {
        console.log(`  ${type}: ${count} รายการ`);
      }
    });
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  const cleaner = new TypeScriptUnusedCleaner();

  switch (command) {
    case 'analyze':
      cleaner.analyzeUnusedCode();
      cleaner.printSummary();
      break;
    case 'report':
      cleaner.analyzeUnusedCode();
      cleaner.generateReport();
      cleaner.printSummary();
      break;
    default:
      console.log(`
🔧 TypeScript Unused Code Cleaner

การใช้งาน:
  bun run scripts/ts-unused-cleaner.ts analyze  - วิเคราะห์ unused code
  bun run scripts/ts-unused-cleaner.ts report   - สร้างรายงานแบบละเอียด

ตัวอย่าง:
  bun run scripts/ts-unused-cleaner.ts analyze
      `);
  }
}

if (import.meta.main) {
  main().catch(console.error);
}
