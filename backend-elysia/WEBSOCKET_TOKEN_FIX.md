# การแก้ไขปัญหา WebSocket Token

## ปัญหาที่พบ

```
websocket.svelte.ts:113 Error connecting to WebSocket: Error: Token ไม่พร้อม
    at WebSocketStore.connect (websocket.svelte.ts:46:19)
    at checkAndConnect (WebSocketProvider.svelte:28:20)
    at WebSocketProvider.svelte:37:4
```

## สาเหตุของปัญหา

1. **WebSocket พยายามเชื่อมต่อก่อนที่ token จะพร้อม** - authStore ใช้ httpOnly cookies แต่ WebSocket พยายามเข้าถึง token โดยตรง
2. **Timing issue** - WebSocketProvider ทำงานก่อนที่ authentication จะเสร็จสิ้น
3. **Token access method ไม่ถูกต้อง** - พยายามเข้าถึง `authStore.token` ที่ไม่มีอยู่

## วิธีแก้ไข

### 1. ปรับปรุง WebSocketStore Token Access

#### `dashboard-sveltekit/src/lib/stores/websocket.svelte.ts`

```typescript
// รอให้ authentication พร้อม
let isAuthenticated = authStore.isAuthenticated;
let attempts = 0;
const maxAttempts = 20;

while (!isAuthenticated && attempts < maxAttempts) {
  console.log(
    `⏳ Waiting for authentication... (attempt ${attempts + 1}/${maxAttempts})`,
  );
  await new Promise(resolve => setTimeout(resolve, 500));
  isAuthenticated = authStore.isAuthenticated;
  attempts++;
}

console.log('🔍 WebSocket connection check:', {
  isAuthenticated,
  attempts,
});

if (!isAuthenticated) {
  throw new Error('Authentication ไม่พร้อมหลังจากรอแล้ว');
}
```

### 2. ปรับปรุง Authentication Method

```typescript
// ยืนยันตัวตน
private authenticate() {
  if (!this.ws || !authStore.isAuthenticated) return;

  // ใช้ getTokenFromCookies จาก authStore
  const token = this.getTokenFromCookies();
  if (!token) {
    console.error('❌ No token found in cookies');
    return;
  }

  this.ws.send(JSON.stringify({
    type: 'auth',
    data: {
      token,
    },
  }));
}

// ดึง token จาก cookies
private getTokenFromCookies(): string | null {
  if (!browser) return null;
  
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'accessToken') {
      return value;
    }
  }
  return null;
}
```

### 3. ปรับปรุง WebSocketProvider

#### `dashboard-sveltekit/src/lib/components/providers/WebSocketProvider.svelte`

```typescript
// ตรวจสอบสถานะและเชื่อมต่อ WebSocket
function checkAndConnect() {
  const isAuthenticated = authStore.isAuthenticated;

  console.log('🔍 WebSocket check:', {
    isAuthenticated,
    connected: websocketStore.connected,
    error: websocketStore.error,
  });

  // เชื่อมต่อเมื่อ authenticated
  if (isAuthenticated && !websocketStore.connected) {
    console.log('🔌 Auto-connecting WebSocket...');
    websocketStore.connect();
  }
  else if (!isAuthenticated && websocketStore.connected) {
    console.log('🔌 Auto-disconnecting WebSocket...');
    websocketStore.disconnect();
  }
}
```

## การเปลี่ยนแปลงที่ทำ

### 1. Backend Changes

- ไม่มีการเปลี่ยนแปลงใน backend เพราะใช้ httpOnly cookies

### 2. Frontend Changes

#### `dashboard-sveltekit/src/lib/stores/websocket.svelte.ts`

- **เปลี่ยนจากการรอ token เป็นการรอ authentication**
- **เพิ่ม getTokenFromCookies()** เพื่อดึง token จาก cookies
- **ปรับปรุง authenticate()** ให้ใช้ token จาก cookies
- **เพิ่มการรอ authentication** แทนการรอ token โดยตรง

#### `dashboard-sveltekit/src/lib/components/providers/WebSocketProvider.svelte`

- **ลบการตรวจสอบ hasToken** เพราะใช้ isAuthenticated แทน
- **ปรับปรุง checkAndConnect()** ให้ตรวจสอบ authentication เท่านั้น

## การทดสอบ

### 1. ตรวจสอบ Console Logs

```
⏳ Waiting for authentication... (attempt 1/20)
⏳ Waiting for authentication... (attempt 2/20)
🔍 WebSocket connection check: {isAuthenticated: true, attempts: 3}
🔌 Connecting to WebSocket: ws://localhost:5000/v1/notifications/ws
🔌 Notification WebSocket connected
✅ Notification WebSocket authenticated
```

### 2. ตรวจสอบ UI

- WebSocket เชื่อมต่อหลังจาก authentication พร้อม
- ไม่มี error "Token ไม่พร้อม"
- WebSocket status indicator แสดงสถานะที่ถูกต้อง

### 3. ตรวจสอบ Functionality

- Real-time notifications ทำงาน
- Token ถูกส่งผ่าน cookies อย่างถูกต้อง
- Authentication ทำงานเสถียร

## ประโยชน์ที่ได้

### 1. การทำงานที่เสถียร

- รอให้ authentication พร้อมก่อนเชื่อมต่อ
- ไม่มีการเชื่อมต่อที่ล้มเหลว
- การจัดการ token ที่ปลอดภัย

### 2. User Experience ที่ดีขึ้น

- ไม่มี error ที่ทำให้สับสน
- การเชื่อมต่อที่เสถียร
- สถานะที่ชัดเจน

### 3. Developer Experience ที่ดีขึ้น

- การ debug ที่ง่ายขึ้น
- Logs ที่ชัดเจน
- การจัดการ state ที่ดีขึ้น

## หมายเหตุ

- ใช้ httpOnly cookies สำหรับ token management
- รอให้ authentication พร้อมก่อนเชื่อมต่อ WebSocket
- ไม่มีการเข้าถึง token โดยตรงจาก authStore
- รองรับ Token Rotation System
