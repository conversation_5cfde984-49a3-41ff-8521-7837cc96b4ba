<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Refresh Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ทดสอบ Refresh Token</h1>
        
        <div>
            <button class="button" onclick="testRefreshToken()">ทดสอบ Refresh Token</button>
            <button class="button" onclick="checkCookies()">ตรวจสอบ Cookies</button>
            <button class="button" onclick="clearResults()">ล้างผลลัพธ์</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function clearResults() {
            document.getElementById('result').textContent = '';
        }

        function checkCookies() {
            const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                acc[key] = value;
                return acc;
            }, {});

            const cookieInfo = {
                'auth_token': cookies.auth_token ? 'present' : 'missing',
                'refreshToken': cookies.refreshToken ? 'present' : 'missing',
                'session_id': cookies.session_id ? 'present' : 'missing',
                'remember_me': cookies.remember_me ? 'present' : 'missing',
                'csrf_token': cookies.csrf_token ? 'present' : 'missing'
            };

            showResult(`Cookies Status:\n${JSON.stringify(cookieInfo, null, 2)}`, 'info');
        }

        async function testRefreshToken() {
            try {
                showResult('กำลังทดสอบ Refresh Token...', 'info');

                const response = await fetch('/dashboard/profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=testToken',
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.success) {
                    showResult(`✅ Refresh Token สำเร็จ!\n\nข้อมูล:\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(`❌ Refresh Token ล้มเหลว!\n\nข้อผิดพลาด:\n${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ เกิดข้อผิดพลาดในการทดสอบ:\n${error.message}`, 'error');
            }
        }

        // ตรวจสอบ cookies เมื่อโหลดหน้า
        window.onload = function() {
            checkCookies();
        };
    </script>
</body>
</html> 