import { config } from '@/core/config/environment';
import { getRateLimitConfig } from '@/core/config/rate-limit';
import { publicCSRFProtection } from '@/core/middleware/csrf';
import { logger } from '@/core/utils/logger';
import { bearer } from '@elysiajs/bearer';
import { cors } from '@elysiajs/cors';
import { serverTiming } from '@elysiajs/server-timing';
import { staticPlugin } from '@elysiajs/static';
import { Elysia } from 'elysia';
import { helmet } from 'elysia-helmet';
import { rateLimit } from 'elysia-rate-limit';
import { loggingPlugin } from './logging';

/**
 * Security plugins (helmet, cors, rate limiting, CSRF protection)
 */
export function setupSecurityPlugins(app: Elysia) {
  return app
    .use(
      helmet({
        crossOriginResourcePolicy: {
          policy: 'cross-origin',
        },
      }),
    )
    .use(bearer())
    // .use(publicCSRFProtection) // ✅ เพิ่ม CSRF protection - temporarily disabled
    // .use(
    //   rateLimit({
    //     ...getRateLimitConfig(config.NODE_ENV),
    //     generator: request => {
    //       // Use IP address as the default identifier
    //       return request.headers.get('x-forwarded-for') || request.headers.get('cf-connecting-ip') || 'unknown';
    //     },
    //   }),
    // )
    .use(
      cors({
        exposeHeaders: ['Content-Range', 'X-Pagination', 'X-Response-Time'],
        methods: ['GET', 'PUT', 'DELETE', 'POST', 'OPTIONS', 'PATCH'],
        maxAge: 3600,
        origin: true,
        credentials: true,
        allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token'], // ✅ เพิ่ม CSRF header
      }),
    ) as unknown as Elysia;
}

/**
 * Utility plugins (static files, server timing, logging)
 */
export function setupUtilityPlugins(app: Elysia) {
  return app.use(staticPlugin()).use(serverTiming()) as unknown as Elysia;
}

/**
 * Request/Response logging middleware using custom logging plugin
 */
export function setupLoggingMiddleware(app: Elysia) {
  return app.use(loggingPlugin()) as unknown as Elysia;
}

/**
 * Setup all plugins in the correct order
 */
export function setupAllPlugins(app: Elysia) {
  return setupLoggingMiddleware(setupUtilityPlugins(setupSecurityPlugins(app))) as Elysia;
}

export { recaptchaMiddleware, recaptchaPlugin, verifyRecaptcha } from '@/core/plugins/recaptcha';
export { authPlugin } from './auth';
