// import { checkUser } from '@/core/middleware/checkUser';
import { Customer } from '@/modules/customer/customer.model';
import { User } from '@/modules/user/user.model';
import { Elysia } from 'elysia';
import { standardResponseSchema } from '../schemas/response.schema';
import { extractBearerToken, verifyCustomerToken, verifyUserToken } from '../services/jwt.service';
import { HttpError } from '../utils/error';

// Authentication plugin สำหรับ User
export const userAuthPlugin = new Elysia({ name: 'user-auth' })
  // .use(checkUser)
  .derive(async ({ headers }) => {
    console.log('🔐 Auth Plugin: Processing request');
    const bearer = extractBearerToken(headers['authorization']);
    console.log('🔐 Auth Plugin: Bearer token:', bearer ? 'YES' : 'NO');

    if (!bearer) {
      console.log('🔐 Auth Plugin: No bearer token');
      return { user: null };
    }

    const userPayload = await verifyUser<PERSON>oken(bearer);
    console.log('🔐 Auth Plugin: User payload:', userPayload ? 'YES' : 'NO');

    if (!userPayload) {
      console.log('🔐 Auth Plugin: Invalid token');
      return { user: null };
    }

    const userData = await User.findById(userPayload.userId);
    console.log('🔐 Auth Plugin: User data:', userData ? 'YES' : 'NO');
    return { user: userData };
  })
  .guard({ response: standardResponseSchema })
  .onBeforeHandle(({ user }) => {
    console.log('🔐 Auth Plugin: Before handle - user:', user ? 'YES' : 'NO');
    if (!user) {
      console.log('🔐 Auth Plugin: No user - throwing 401');
      throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
    }
  })
  .as('scoped');

// Authentication plugin สำหรับ Customer
export const customerAuthPlugin = new Elysia({ name: 'customer-auth' })
  // .use(checkUser)
  .derive(async ({ headers }) => {
    const bearer = extractBearerToken(headers['authorization']);

    if (!bearer) {
      return { customer: null };
    }

    const customerPayload = await verifyCustomerToken(bearer);
    if (!customerPayload) {
      return { customer: null };
    }

    const customerData = await Customer.findById(customerPayload.customerId);
    return { customer: customerData };
  })
  .guard({ response: standardResponseSchema })
  .onBeforeHandle(({ customer }) => {
    if (!customer) {
      throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
    }
  })
  .as('scoped');

// Authentication plugin ที่ใช้ guard pattern
export const authPlugin = new Elysia({ name: 'auth' })
  .derive(({ headers }) => {
    const bearer = extractBearerToken(headers['authorization']);
    return { bearer };
  })
  .onBeforeHandle(({ bearer, set }) => {
    if (!bearer) {
      set.status = 401;
      return 'Unauthorized';
    }
  })
  .get('/', ({ bearer }) => `Token: ${bearer}`)
  .as('scoped');

// Authentication plugin สำหรับ JWT ที่ใช้ guard pattern (legacy - ใช้สำหรับ user)
export const jwtAuthPlugin = new Elysia({ name: 'jwt-auth' })
  .derive(async ({ headers }) => {
    const bearer = extractBearerToken(headers['authorization']);

    if (!bearer) {
      return { user: null };
    }

    const userPayload = await verifyUserToken(bearer);
    return { user: userPayload };
  })
  .guard({ response: standardResponseSchema })
  .onBeforeHandle(({ user }) => {
    if (!user) {
      throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
    }
  })
  .as('scoped');

// Optional authentication plugin (ไม่บังคับต้องมี token)
export const optionalAuthPlugin = new Elysia({ name: 'optional-auth' }).derive(async ({ headers }) => {
  const bearer = extractBearerToken(headers.authorization);

  if (!bearer) {
    return { user: null };
  }

  const userPayload = await verifyUserToken(bearer);
  return { user: userPayload };
});

// Role-based authentication plugin ที่ใช้ guard pattern
export const roleAuthPlugin = new Elysia({ name: 'role-auth' })
  .derive(async ({ headers }) => {
    const bearer = extractBearerToken(headers.authorization);

    if (!bearer) {
      return { user: null };
    }

    const userPayload = await verifyUserToken(bearer);

    if (!userPayload) {
      return { user: null };
    }

    return {
      user: userPayload,
      hasRole: (role: string) => userPayload.role === role,
      hasAnyRole: (roles: string[]) => roles.includes(userPayload.role || ''),
      isAdmin: userPayload.role === 'admin',
      isOwner: userPayload.role === 'owner',
    };
  })
  .guard({ response: standardResponseSchema })
  .onBeforeHandle(({ user }) => {
    if (!user) {
      throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
    }
  })
  .as('scoped');

// Export all plugins
export const authPlugins = {
  auth: authPlugin,
  jwt: jwtAuthPlugin,
  user: userAuthPlugin,
  customer: customerAuthPlugin,
  optional: optionalAuthPlugin,
  role: roleAuthPlugin,
};

// Export types for backward compatibility
// export type { CustomerJWTPayload, JWTPayload, UserJWTPayload };
