import { logger } from '@/core/utils/logger';
import { Elysia } from 'elysia';

// Interface for request with timing
interface RequestWithTiming extends Request {
  startTime?: number;
}

/**
 * Custom logging plugin for request/response logging
 */
export function loggingPlugin() {
  return new Elysia()
    .onRequest(({ request }) => {
      const startTime = Date.now();
      const ip = request.headers.get('x-forwarded-for')
        || request.headers.get('cf-connecting-ip')
        || request.headers.get('x-real-ip')
        || 'unknown';
      const userAgent = request.headers.get('user-agent') || 'unknown';
      const referer = request.headers.get('referer') || 'direct';
      const method = request.method;
      const url = new URL(request.url);
      const path = url.pathname;
      const query = url.search;

      logger.info('Request started', {
        method,
        path,
        query: query || undefined,
        ip,
        userAgent,
        referer,
        timestamp: new Date().toISOString(),
      });

      // Store start time for response logging
      (request as RequestWithTiming).startTime = startTime;
    })
    .onAfterResponse(({ request, set }) => {
      const duration = Date.now() - ((request as RequestWithTiming).startTime || Date.now());
      const ip = request.headers.get('x-forwarded-for')
        || request.headers.get('cf-connecting-ip')
        || request.headers.get('x-real-ip')
        || 'unknown';
      const method = request.method;
      const url = new URL(request.url);
      const path = url.pathname;
      const statusCode = (set.status as number) || 200;
      const statusText = typeof set.status === 'string' ? set.status : 'OK';

      // Determine log level based on status code
      const logLevel = statusCode >= 500 ? 'error'
        : statusCode >= 400 ? 'warn'
        : statusCode >= 300 ? 'info' : 'info';

      logger[logLevel]('Request completed', {
        method,
        path,
        ip,
        statusCode,
        statusText,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
      });
    })
    .onError(({ error, request }) => {
      const ip = request.headers.get('x-forwarded-for')
        || request.headers.get('cf-connecting-ip')
        || request.headers.get('x-real-ip')
        || 'unknown';
      const method = request.method;
      const url = new URL(request.url);
      const path = url.pathname;

      logger.error('Request error', {
        method,
        path,
        ip,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
    });
}
