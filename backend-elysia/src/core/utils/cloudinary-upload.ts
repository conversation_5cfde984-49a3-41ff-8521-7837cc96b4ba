import { config } from '../config/environment';

// Interface สำหรับ upload options
export interface UploadOptions {
  folder?: string;
  publicId?: string;
  transformation?: any;
  resourceType?: 'image' | 'video' | 'raw' | 'auto';
  format?: string;
  quality?: string | number;
  width?: number;
  height?: number;
  crop?: string;
  secure?: boolean;
}

// Interface สำหรับ signed upload options
export interface SignedUploadOptions extends UploadOptions {
  timestamp?: number;
  eager?: string;
  tags?: string[];
}

// Interface สำหรับผลลัพธ์การ upload
export interface UploadResult {
  publicId: string;
  secureUrl: string;
  url: string;
  format: string;
  resourceType: string;
  width?: number;
  height?: number;
  bytes: number;
  signature?: string;
}

class CloudinaryUploadUtil {
  constructor() {
    // ตรวจสอบว่า Cloudinary ได้ถูก config แล้วหรือไม่
    if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
      throw new Error('Cloudinary configuration is missing');
    }
  }

  /**
   * แปลงข้อมูลภาพให้เป็นรูปแบบที่ Cloudinary รองรับ
   */
  private async processFileInput(file: string | Buffer | File | any): Promise<string | Buffer> {
    // ถ้าเป็น string และเป็น base64
    if (typeof file === 'string') {
      if (file.startsWith('data:')) {
        // Base64 with data URL prefix
        const base64Data = file.replace(/^data:image\/\w+;base64,/, '');
        return Buffer.from(base64Data, 'base64');
      }
      else if (file.match(/^[A-Za-z0-9+/]+=*$/)) {
        // Pure base64 string
        return Buffer.from(file, 'base64');
      }
      else {
        // File path
        return file;
      }
    }

    // ถ้าเป็น File object (จาก browser)
    if (file && typeof file.arrayBuffer === 'function') {
      const arrayBuffer = await file.arrayBuffer();
      return Buffer.from(arrayBuffer);
    }

    // ถ้าเป็น Buffer แล้ว
    if (Buffer.isBuffer(file)) {
      return file;
    }

    throw new Error('รูปแบบไฟล์ไม่รองรับ');
  }

  /**
   * อัพโหลดไฟล์แบบ public (ไม่ต้องมี signature)
   * @param file - รองรับ: File path (string), Buffer, Base64 string, หรือ File object
   */
  async uploadPublic(file: string | Buffer | File, options: UploadOptions = {}): Promise<UploadResult> {
    try {
      // แปลงไฟล์ให้เป็นรูปแบบที่ Cloudinary รองรับ
      const processedFile = await this.processFileInput(file);

      const uploadOptions = {
        folder: options.folder || 'uploads',
        resource_type: options.resourceType || 'auto',
        public_id: options.publicId,
        transformation: options.transformation,
        format: options.format,
        quality: options.quality,
        width: options.width,
        height: options.height,
        crop: options.crop,
        secure: options.secure !== false, // default เป็น true
      };

      const result = await cloudinary.uploader.upload(processedFile, uploadOptions);

      return {
        publicId: result.public_id,
        secureUrl: result.secure_url,
        url: result.url,
        format: result.format,
        resourceType: result.resource_type,
        width: result.width,
        height: result.height,
        bytes: result.bytes,
      };
    }
    catch (error) {
      console.error('Error uploading file to Cloudinary:', error);
      throw new Error(`ไม่สามารถอัพโหลดไฟล์ได้: ${error.message}`);
    }
  }

  /**
   * อัพโหลดไฟล์แบบ signed (มี signature สำหรับความปลอดภัย)
   * @param file - รองรับ: File path (string), Buffer, Base64 string, หรือ File object
   */
  async uploadSigned(file: string | Buffer | File, options: SignedUploadOptions = {}): Promise<UploadResult> {
    try {
      // แปลงไฟล์ให้เป็นรูปแบบที่ Cloudinary รองรับ
      const processedFile = await this.processFileInput(file);
      const timestamp = options.timestamp || Math.round(new Date().getTime() / 1000);

      const uploadOptions = {
        folder: options.folder || 'uploads',
        resource_type: options.resourceType || 'auto',
        public_id: options.publicId,
        timestamp,
        transformation: options.transformation,
        format: options.format,
        quality: options.quality,
        width: options.width,
        height: options.height,
        crop: options.crop,
        eager: options.eager,
        tags: options.tags,
        secure: options.secure !== false,
      };

      const result = await cloudinary.uploader.upload(processedFile, uploadOptions);

      return {
        publicId: result.public_id,
        secureUrl: result.secure_url,
        url: result.url,
        format: result.format,
        resourceType: result.resource_type,
        width: result.width,
        height: result.height,
        bytes: result.bytes,
        signature: result.signature,
      };
    }
    catch (error) {
      console.error('Error uploading signed file to Cloudinary:', error);
      throw new Error(`ไม่สามารถอัพโหลดไฟล์แบบ signed ได้: ${error.message}`);
    }
  }

  /**
   * สร้าง signed URL สำหรับ upload จาก client-side
   */
  generateSignedUploadUrl(options: SignedUploadOptions = {}): {
    signature: string;
    timestamp: number;
    cloudName: string;
    apiKey: string;
    uploadUrl: string;
  } {
    const timestamp = options.timestamp || Math.round(new Date().getTime() / 1000);

    const params = {
      timestamp,
      folder: options.folder || 'uploads',
      resource_type: options.resourceType || 'auto',
      ...(options.publicId && { public_id: options.publicId }),
      ...(options.transformation && { transformation: options.transformation }),
      ...(options.format && { format: options.format }),
      ...(options.quality && { quality: options.quality }),
      ...(options.eager && { eager: options.eager }),
      ...(options.tags && { tags: options.tags.join(',') }),
    };

    const signature = cloudinary.utils.api_sign_request(params, config.cloudinaryApiSecret);

    return {
      signature,
      timestamp,
      cloudName: config.cloudinaryCloudName,
      apiKey: config.cloudinaryApiKey,
      uploadUrl: `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/upload`,
    };
  }

  /**
   * ลบไฟล์จาก Cloudinary
   */
  async deleteFile(publicId: string, resourceType: 'image' | 'video' | 'raw' = 'image'): Promise<void> {
    try {
      await cloudinary.uploader.destroy(publicId, { resource_type: resourceType });
    }
    catch (error) {
      console.error('Error deleting file from Cloudinary:', error);
      throw new Error(`ไม่สามารถลบไฟล์ได้: ${error.message}`);
    }
  }

  /**
   * อัพโหลดรูปโปรไฟล์ (ปรับขนาดอัตโนมัติ)
   * @param file - รองรับ: File path, Buffer, Base64 string, หรือ File object
   */
  async uploadProfileImage(file: string | Buffer | File, userId: string): Promise<UploadResult> {
    return this.uploadPublic(file, {
      folder: 'profiles',
      publicId: `profile_${userId}`,
      resourceType: 'image',
      transformation: {
        width: 400,
        height: 400,
        crop: 'fill',
        gravity: 'face',
        quality: 'auto:good',
        format: 'webp',
      },
    });
  }

  /**
   * อัพโหลดรูปปก (ปรับขนาดอัตโนมัติ)
   * @param file - รองรับ: File path, Buffer, Base64 string, หรือ File object
   */
  async uploadCoverImage(file: string | Buffer | File, userId: string): Promise<UploadResult> {
    return this.uploadPublic(file, {
      folder: 'covers',
      publicId: `cover_${userId}`,
      resourceType: 'image',
      transformation: {
        width: 1200,
        height: 400,
        crop: 'fill',
        quality: 'auto:good',
        format: 'webp',
      },
    });
  }

  /**
   * ตรวจสอบและแปลงข้อมูลรูปภาพ
   */
  validateImageInput(file: any): { isValid: boolean; type: string; size?: number; } {
    if (typeof file === 'string') {
      if (file.startsWith('data:image/')) {
        return { isValid: true, type: 'base64-dataurl' };
      }
      else if (file.match(/^[A-Za-z0-9+/]+=*$/)) {
        return { isValid: true, type: 'base64' };
      }
      else {
        return { isValid: true, type: 'filepath' };
      }
    }

    if (Buffer.isBuffer(file)) {
      return { isValid: true, type: 'buffer', size: file.length };
    }

    if (file && typeof file.arrayBuffer === 'function') {
      return { isValid: true, type: 'file', size: file.size };
    }

    return { isValid: false, type: 'unknown' };
  }
}

// Export singleton instance
export const cloudinaryUpload = new CloudinaryUploadUtil();
