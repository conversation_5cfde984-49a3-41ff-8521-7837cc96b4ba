/**
 * ฟังก์ชั่น utility สำหรับการทำ pagination
 */

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResult {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginationOptions {
  defaultLimit?: number;
  maxLimit?: number;
}

/**
 * ฟังก์ชั่นสำหรับแปลงพารามิเตอร์ pagination เป็นค่าที่ใช้ในการ query
 * @param params พารามิเตอร์ pagination จาก request
 * @param options ตัวเลือกเพิ่มเติม
 * @returns ค่า pagination ที่ใช้ในการ query
 */
export function getPaginationParams(
  params: PaginationParams = {},
  options: PaginationOptions = {},
): { page: number; limit: number; skip: number; } {
  const defaultLimit = options.defaultLimit || 20;
  const maxLimit = options.maxLimit || 100;

  let page = Number(params.page) || 1;
  let limit = Number(params.limit) || defaultLimit;

  // ป้องกันค่าติดลบ
  page = page < 1 ? 1 : page;

  // จำกัดขนาดสูงสุดของ limit
  limit = limit < 1 ? defaultLimit : limit;
  limit = limit > maxLimit ? maxLimit : limit;

  const skip = (page - 1) * limit;

  return { page, limit, skip };
}

/**
 * ฟังก์ชั่นสำหรับสร้างผลลัพธ์ pagination
 * @param page หน้าปัจจุบัน
 * @param limit จำนวนรายการต่อหน้า
 * @param total จำนวนรายการทั้งหมด
 * @returns ข้อมูล pagination สำหรับส่งกลับให้ client
 */
export function createPaginationResult(page: number, limit: number, total: number): PaginationResult {
  return {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
  };
}

/**
 * ฟังก์ชั่นสำหรับสร้าง MongoDB aggregation stages สำหรับ pagination
 * @param params พารามิเตอร์ pagination
 * @param options ตัวเลือกเพิ่มเติม
 * @returns MongoDB aggregation stages สำหรับ pagination
 */
export function createPaginationStages(
  params: PaginationParams = {},
  options: PaginationOptions = {},
): { paginationStages: any[]; paginationParams: { page: number; limit: number; skip: number; }; } {
  const { page, limit, skip } = getPaginationParams(params, options);

  const paginationStages = [{ $skip: skip }, { $limit: limit }];

  return { paginationStages, paginationParams: { page, limit, skip } };
}

/**
 * ฟังก์ชั่นสำหรับสร้าง MongoDB aggregation stage สำหรับนับจำนวนรายการทั้งหมด
 * @param matchStage stage สำหรับ filter ข้อมูล (ถ้ามี)
 * @returns MongoDB aggregation pipeline สำหรับนับจำนวนรายการทั้งหมด
 */
export function createCountPipeline(matchStage: any = {}): any[] {
  return [matchStage, { $count: 'total' }];
}
