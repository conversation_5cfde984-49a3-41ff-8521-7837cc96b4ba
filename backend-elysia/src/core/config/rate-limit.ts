/**
 * Rate limiting configuration for different API endpoints
 */

// Time constants in seconds
export const TIME = {
  SECOND: 1,
  MINUTE: 60,
  HOUR: 60 * 60,
  DAY: 24 * 60 * 60,
};

// Rate limit configuration interface
export interface RateLimitConfig {
  max: number;
  duration: number;
  message?: string;
}

// Default rate limit configuration
export const defaultRateLimit: RateLimitConfig = {
  max: 100,
  duration: TIME.MINUTE,
  message: 'Too many requests, please try again later.',
};

// Authentication endpoints (login, register, etc.)
export const authRateLimit: RateLimitConfig = {
  max: 10,
  duration: TIME.MINUTE * 5,
  message: 'Too many authentication attempts, please try again later.',
};

// Sensitive operations (password reset, email verification, etc.)
export const sensitiveRateLimit: RateLimitConfig = {
  max: 5,
  duration: TIME.MINUTE * 15,
  message: 'Too many sensitive operations, please try again later.',
};

// Public API endpoints
export const publicApiRateLimit: RateLimitConfig = {
  max: 200,
  duration: TIME.MINUTE,
  message: 'Rate limit exceeded for public API.',
};

// Admin API endpoints
export const adminApiRateLimit: RateLimitConfig = {
  max: 300,
  duration: TIME.MINUTE,
  message: 'Rate limit exceeded for admin API.',
};

// Environment-specific rate limit configurations
export const rateLimitConfig: Record<string, RateLimitConfig> = {
  production: {
    max: 100,
    duration: TIME.MINUTE,
    message: 'Rate limit exceeded. Please try again later.',
  },
  development: {
    max: 500,
    duration: TIME.MINUTE * 5,
    message: 'Rate limit exceeded in development mode.',
  },
};

// Get rate limit configuration based on environment
export const getRateLimitConfig = (env = 'development'): RateLimitConfig => {
  return rateLimitConfig[env] || rateLimitConfig.development;
};

// Path-specific rate limit configurations
export const pathRateLimits: Record<string, RateLimitConfig> = {
  // Authentication endpoints
  '/website/*/customer/login': authRateLimit,
  '/website/*/customer/register': authRateLimit,
  '/dashboard/user/login': authRateLimit,
  '/dashboard/user/register': authRateLimit,

  // Sensitive operations
  '/website/*/customer/forgot-password': sensitiveRateLimit,
  '/dashboard/user/forgot-password': sensitiveRateLimit,

  // Admin endpoints
  '/admin/*': adminApiRateLimit,

  // Public API endpoints
  '/website/*': publicApiRateLimit,
};
