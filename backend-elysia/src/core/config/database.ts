import { logger } from '@/core/utils/logger';
import mongoose from 'mongoose';
import { config } from './environment';

export const connectDB = async () => {
  try {
    // Configure mongoose options for better performance and reliability
    const options = {
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      bufferCommands: false, // Disable mongoose buffering
    };

    await mongoose.connect(config.mongodbUri, options);

    logger.info('Database connection established', {
      database: 'MongoDB',
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name,
      readyState: mongoose.connection.readyState,
    });

    // Handle connection events
    mongoose.connection.on('error', error => {
      logger.error('Database connection error', {
        error: error.message,
        stack: error.stack,
      });
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('Database disconnected', {
        database: 'MongoDB',
      });
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('Database reconnected', {
        database: 'MongoDB',
      });
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('Database connection closed due to app termination');
        process.exit(0);
      }
      catch (error) {
        logger.error('Error closing database connection', {
          error: error instanceof Error ? error.message : String(error),
        });
        process.exit(1);
      }
    });
  }
  catch (error) {
    logger.error('Failed to connect to database', {
      database: 'MongoDB',
      uri: config.mongodbUri.replace(/\/\/.*@/, '//***:***@'), // Hide credentials in logs
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    process.exit(1);
  }
};
