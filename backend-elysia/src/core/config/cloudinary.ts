import { config } from './environment';

// ตั้งค่า Cloudinary
cloudinary.config({
  cloud_name: config.cloudinaryCloudName,
  api_key: config.cloudinaryApiKey,
  api_secret: config.cloudinaryApiSecret,
});

// ฟังก์ชันสำหรับอัพโหลดรูปภาพ
export const uploadImage = async (filepath: string) => {
  try {
    const result = await cloudinary.uploader.upload(filepath, {
      folder: 'profile_pictures',
      resource_type: 'auto',
    });

    return result.secure_url;
  }
  catch (error) {
    console.error('Error uploading image:', error);
    throw Error('ไม่สามารถอัพโหลดรูปภาพได้');
  }
};

// ฟังก์ชันสำหรับลบรูปภาพ
export const deleteImage = async (publicId: string) => {
  try {
    await cloudinary.uploader.destroy(publicId);
  }
  catch (error) {
    console.error('Error deleting image:', error);
    throw Error('ไม่สามารถลบรูปภาพได้');
  }
};
