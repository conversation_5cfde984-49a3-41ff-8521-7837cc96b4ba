import { config } from '@/core/config/environment';
import type { <PERSON>rrorContext, ErrorHandlerResult, ErrorResponse, ValidationErrorDetail } from '@/core/types/error.types';
import { hasMessage, isGenericError, isHttpError, isValidationError } from '@/core/types/error.types';
import { logger } from '@/core/utils/logger';
import type { Context } from 'elysia';
/**
 * ดึงข้อมูล context จาก request
 */
function getErrorContext(request: Request): ErrorContext {
  return {
    ip: request.headers.get('x-forwarded-for') || request.headers.get('cf-connecting-ip') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
    url: request.url,
    method: request.method,
  };
}

/**
 * แปลง status code เป็นข้อความภาษาไทย
 */
function getThaiStatusMessage(status: number): string {
  const statusMessages: Record<number, string> = {
    400: 'ข้อมูลไม่ถูกต้อง',
    401: 'ไม่ได้รับอนุญาต',
    403: 'ไม่มีสิทธิ์เข้าถึง',
    404: 'ไม่พบข้อมูล',
    409: 'ข้อมูลขัดแย้ง',
    422: 'ข้อมูลไม่สมบูรณ์',
    429: 'คำขอมากเกินไป',
    500: 'เกิดข้อผิดพลาดในระบบ',
    502: 'เซิร์ฟเวอร์ไม่พร้อมใช้งาน',
    503: 'บริการไม่พร้อมใช้งาน',
  };
  return statusMessages[status] || 'เกิดข้อผิดพลาด';
}

/**
 * จัดการ validation error
 */
function handleValidationError(error: unknown, context: ErrorContext): ErrorHandlerResult {
  let message = 'ข้อมูลไม่ถูกต้อง';
  let detail: ErrorHandlerResult['detail'];

  logger.warn('Validation error', {
    ...context,
    status: 400,
    error: error instanceof Error ? error.message : String(error),
  });

  // พยายามแปลง error message เป็น JSON
  if (hasMessage(error)) {
    try {
      const parsed = JSON.parse(error.message);
      if (Array.isArray(parsed.errors)) {
        detail = parsed.errors;
        const summaries = parsed.errors
          .map((e: ValidationErrorDetail) => e.summary)
          .filter(Boolean);
        if (summaries.length > 0) {
          message = summaries.join(', ');
        }
      }
      else if (parsed.summary) {
        message = parsed.summary;
        detail = parsed;
      }
    }
    catch {
      detail = { message: error.message };
    }
  }

  // ถ้า detail เป็น array ให้รวม summary
  if (Array.isArray(detail)) {
    const summaries = detail
      .map((e: ValidationErrorDetail) => e.summary)
      .filter(Boolean);
    if (summaries.length > 0) {
      message = summaries.join(', ');
    }
  }

  // ถ้ามี all property ใน error
  if (error && typeof error === 'object' && 'all' in error) {
    detail = (error as { all: ValidationErrorDetail[]; }).all;
  }

  return { status: 400, message, detail };
}

/**
 * จัดการ HTTP error (error ที่มี status และ message)
 */
function handleHttpError(error: unknown, context: ErrorContext): Omit<ErrorHandlerResult, 'detail'> {
  const httpError = error as { status?: number; statusCode?: number; message?: string; };
  const status = httpError.status || httpError.statusCode || 500;
  const message = httpError.message || 'Internal Server Error';

  if (status >= 500) {
    logger.error('Server error', {
      ...context,
      status,
      message,
      error: error instanceof Error ? error.stack : String(error),
    });
  }
  else if (status >= 400) {
    logger.warn('Client error', {
      ...context,
      status,
      message,
    });
  }

  return { status, message };
}

/**
 * จัดการ generic error
 */
function handleGenericError(error: unknown, context: ErrorContext): Omit<ErrorHandlerResult, 'detail'> {
  const status = 500;
  let message = 'Internal Server Error';

  if (hasMessage(error)) {
    message = error.message;
  }

  logger.error('Unhandled error', {
    ...context,
    status,
    message,
    error: error instanceof Error ? error.stack : String(error),
  });

  return { status, message };
}

/**
 * จัดการ unknown error
 */
function handleUnknownError(error: unknown, context: ErrorContext): Omit<ErrorHandlerResult, 'detail'> {
  const status = 500;
  const message = 'Internal Server Error';

  logger.error('Unknown error', {
    ...context,
    status,
    error: String(error),
  });

  return { status, message };
}

/**
 * Main error handler สำหรับ Elysia
 */
export function createErrorHandler() {
  return ({
    code,
    error,
    set,
    request,
  }: {
    code: string | number;
    error: unknown;
    set: Context['set'];
    request: Request;
  }): ErrorResponse => {
    const context = getErrorContext(request);

    let status = 500;
    let message = 'Internal Server Error';
    let detail: ErrorHandlerResult['detail'];

    // กำหนด status จาก code หรือ error
    if (typeof code === 'number') {
      status = code;
    }
    else if (error && typeof error === 'object') {
      const errorObj = error as { statusCode?: number; status?: number; };
      status = errorObj.statusCode || errorObj.status || 500;
    }

    // จัดการ error ตามประเภท
    if (isValidationError(error)) {
      // Validation error
      const result = handleValidationError(error, context);
      status = result.status;
      message = result.message;
      detail = result.detail;
    }
    else if (isHttpError(error)) {
      // HTTP error with status and message
      const result = handleHttpError(error, context);
      status = result.status;
      message = result.message;
    }
    else if (isGenericError(error)) {
      // Generic error with message
      const result = handleGenericError(error, context);
      status = result.status;
      message = result.message;
    }
    else {
      // Unknown error
      const result = handleUnknownError(error, context);
      status = result.status;
      message = result.message;
    }

    // Set HTTP status
    set.status = status;

    // สร้าง error response
    const errorResponse: ErrorResponse = {
      success: false,
      message,
      statusMessage: getThaiStatusMessage(status),
      timestamp: new Date().toISOString(),
    };

    // เพิ่ม detail ถ้ามี
    if (detail) {
      errorResponse.detail = detail;
    }

    // เพิ่ม stack trace ใน development mode
    if (config.isDev && error instanceof Error && error.stack) {
      errorResponse.stack = error.stack;
    }

    return errorResponse;
  };
}
