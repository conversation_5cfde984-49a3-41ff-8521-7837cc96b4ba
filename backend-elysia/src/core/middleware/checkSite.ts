import { standardResponseSchema } from '@/core/schemas/response.schema';
import { HttpError } from '@/core/utils/error';
import { logger } from '@/core/utils/logger';
import { ISite, Site } from '@/modules/site/site.model';
import { Elysia } from 'elysia';

// Interface สำหรับ site check result
export interface SiteCheckResult {
  site: ISite | null;
  isExpired: boolean;
  daysUntilExpiry: number;
  isActive: boolean;
  hasAccess: boolean;
}

// Interface สำหรับ site check options
export interface SiteCheckOptions {
  checkExpiry?: boolean;
  checkActive?: boolean;
  requireSite?: boolean;
  throwOnExpired?: boolean;
  throwOnInactive?: boolean;
}

// Interface สำหรับ context parameter
export interface SiteCheckContext {
  params: {
    siteId?: string;
    fullDomain?: string;
  };
  store: Record<string, unknown>;
}

/**
 * ค้นหา site จาก ID หรือ domain
 */
export const getSiteByIdOrDomain = async (siteId?: string, fullDomain?: string): Promise<ISite | null> => {
  try {
    if (!siteId && !fullDomain) {
      return null;
    }

    logger.debug('Site lookup', { siteId, fullDomain });

    // หา site จาก database
    const site = siteId ? await Site.findById(siteId) : await Site.findOne({ fullDomain });

    logger.debug('Site found', {
      siteId: site?._id,
      domain: site?.fullDomain,
      found: !!site,
    });

    return site;
  }
  catch (error) {
    logger.error('Site lookup failed', {
      siteId,
      fullDomain,
      error: error instanceof Error ? error.message : String(error),
    });
    return null;
  }
};

/**
 * ตรวจสอบสถานะของ site และเพิ่มข้อมูลลงใน store
 */
export const checkSiteStatus = async (site: ISite | null, options: SiteCheckOptions = {}): Promise<SiteCheckResult> => {
  const { checkExpiry = false, checkActive = false, throwOnExpired = false, throwOnInactive = false } = options;

  let isExpired = false;
  let daysUntilExpiry = 0;
  let isActive = true;

  // ตรวจสอบ expiry
  if (checkExpiry && site?.expiredAt) {
    const now = new Date();
    const expiredAt = new Date(site.expiredAt);
    isExpired = expiredAt < now;
    daysUntilExpiry = Math.ceil((expiredAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (throwOnExpired && isExpired) {
      throw new HttpError(403, 'ไซต์หมดอายุแล้ว');
    }
  }

  // ตรวจสอบ active status
  if (checkActive) {
    isActive = site?.isActive || false;

    if (throwOnInactive && !isActive) {
      throw new HttpError(403, 'ไซต์ไม่เปิดใช้งาน');
    }
  }

  return {
    site,
    isExpired,
    daysUntilExpiry,
    isActive,
    hasAccess: !isExpired && isActive,
  };
};

/**
 * Function สำหรับใช้ใน beforeHandle
 */
export const getResultSite = async (c: SiteCheckContext, options: SiteCheckOptions = {}) => {
  const { params = {}, store } = c;
  const { siteId, fullDomain } = params;
  const { requireSite = true } = options;

  try {
    logger.debug('Site check started', { siteId, fullDomain, options });

    const site = await getSiteByIdOrDomain(siteId, fullDomain);

    if (requireSite && !site) {
      throw new HttpError(404, 'ไม่พบเว็บไซต์ อาจถูกลบหรือไม่เคยมีอยู่');
    }

    // ตรวจสอบสถานะของ site
    const siteStatus = await checkSiteStatus(site, options);

    // เพิ่มข้อมูลลงใน store
    store.site = siteStatus.site;
    store.isExpired = siteStatus.isExpired;
    store.daysUntilExpiry = siteStatus.daysUntilExpiry;
    store.isActive = siteStatus.isActive;
    store.hasAccess = siteStatus.hasAccess;

    logger.debug('Site check completed', {
      siteId: site?._id,
      domain: site?.fullDomain,
      isExpired: siteStatus.isExpired,
      isActive: siteStatus.isActive,
      hasAccess: siteStatus.hasAccess,
    });

    return site;
  }
  catch (error) {
    logger.warn('Site check failed', {
      siteId,
      fullDomain,
      options,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
};

/**
 * สร้าง site middleware ที่ยืดหยุ่น
 */
export const createSiteMiddleware = (options: SiteCheckOptions = {}) => {
  return new Elysia({ name: `site-check-${JSON.stringify(options)}` })
    .derive(async (context: SiteCheckContext) => {
      const { params, store } = context;
      await getResultSite({ params, store }, options);
      return {};
    })
    .guard({ response: standardResponseSchema })
    .as('scoped');
};

/**
 * Pre-configured site middlewares
 */

// Basic site check (required)
export const requireSite = createSiteMiddleware({
  requireSite: true,
});

// Site check with expiry validation
export const requireActiveSite = createSiteMiddleware({
  requireSite: true,
  checkExpiry: true,
  checkActive: true,
  throwOnExpired: true,
  throwOnInactive: true,
});

// Optional site check
export const optionalSite = createSiteMiddleware({
  requireSite: false,
});

// Site check with status info (no throwing)
export const siteWithStatus = createSiteMiddleware({
  requireSite: true,
  checkExpiry: true,
  checkActive: true,
  throwOnExpired: false,
  throwOnInactive: false,
});

/**
 * Legacy plugins for backward compatibility
 */
export const checkSite = new Elysia({ name: 'checkSite' })
  .derive(async (context: SiteCheckContext) => {
    const { params, store } = context;
    const site = await getResultSite({ params, store }, { requireSite: true });
    return { site };
  })
  .guard({ response: standardResponseSchema })
  .as('scoped');

export const sitePlugin = new Elysia({ name: 'site' })
  .derive(async (context: SiteCheckContext) => {
    const { params, store } = context;
    const site = await getResultSite({ params, store }, { requireSite: true });
    return { site };
  })
  .guard({ response: standardResponseSchema })
  .as('scoped');

/**
 * Helper functions for manual site checks
 */
export const findSiteById = (siteId: string) => getSiteByIdOrDomain(siteId);
export const findSiteByDomain = (domain: string) => getSiteByIdOrDomain(undefined, domain);

/**
 * Site validation helpers
 */
export const isSiteExpired = (site: ISite | null): boolean => {
  if (!site?.expiredAt) return false;
  const now = new Date();
  const expiredAt = new Date(site.expiredAt);
  return expiredAt < now;
};

export const isSiteActive = (site: ISite | null): boolean => {
  return site?.isActive || false;
};

export const getSiteExpiryDays = (site: ISite | null): number => {
  if (!site?.expiredAt) return 0;
  const now = new Date();
  const expiredAt = new Date(site.expiredAt);
  return Math.ceil((expiredAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
};
