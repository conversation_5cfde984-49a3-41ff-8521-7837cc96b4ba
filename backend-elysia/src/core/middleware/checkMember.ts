import { AccessCheckOptions, AccessControlService } from '@/core/services/access-control.service';
import { logger } from '@/core/utils/logger';
import { Elysia } from 'elysia';
// Re-export types for backward compatibility
export interface CustomerCheckResult {
  isCustomer: boolean;
  customerId?: string;
  role?: string;
  permissions?: string[];
}

export interface CustomerCheckOptions extends AccessCheckOptions {
  requireCustomer?: boolean;
}

// Helper function สำหรับตรวจสอบ customer (wrapper for backward compatibility)
export const checkCustomer = async (siteId: string, customerId: string): Promise<CustomerCheckResult> => {
  const accessResult = await AccessControlService.checkCustomerAccess(siteId, customerId);

  return {
    isCustomer: accessResult.hasAccess,
    customerId: accessResult.customerId,
    role: accessResult.role,
    permissions: accessResult.permissions,
  };
};

// Function สำหรับใช้ใน beforeHandle
export const checkSiteCustomer = async (c: any, options: CustomerCheckOptions = {}) => {
  const { params, user, set, store } = c;
  const siteId = params?.siteId;
  const customerId = user?.customerId || user?.userId; // Support both customerId and userId

  const { requireCustomer = true } = options;

  if (!siteId || !customerId) {
    set.status = 400;
    throw new Error('ข้อมูลไม่ครบถ้วน');
  }

  try {
    const accessResult = await AccessControlService.checkCustomerAccess(siteId, customerId);

    if (!accessResult.hasAccess) {
      if (requireCustomer) {
        set.status = 403;
        throw new Error('คุณไม่ใช่สมาชิกของไซต์นี้');
      }
      return;
    }

    const validation = AccessControlService.validateAccess(accessResult, options);

    if (!validation.isValid) {
      set.status = 403;
      throw new Error(validation.error || 'ไม่มีสิทธิ์เข้าถึง');
    }

    // เพิ่มข้อมูลลงใน store
    store.customerId = accessResult.customerId;
    store.customerRole = accessResult.role;
    store.customerPermissions = accessResult.permissions;
    store.isCustomer = accessResult.hasAccess;
  }
  catch (error) {
    logger.warn('Site customer check failed', {
      siteId,
      customerId,
      options,
      error: error instanceof Error ? error.message : String(error),
    });

    throw error;
  }
};

// Helper functions สำหรับตรวจสอบ customer เฉพาะ
export const isSiteCustomer = async (siteId: string, customerId: string): Promise<boolean> => {
  return await AccessControlService.isSiteCustomer(siteId, customerId);
};

export const isSiteCustomerWithRole = async (siteId: string, customerId: string, role: string): Promise<boolean> => {
  const accessResult = await AccessControlService.checkCustomerAccess(siteId, customerId);
  return accessResult.hasAccess && accessResult.role === role;
};

// Plugin สำหรับใช้ใน .use()
export const customerPlugin = new Elysia({ name: 'customer' }).derive(async (context: any) => {
  const { params, user, store } = context;
  if (params?.siteId && user?.userId) {
    const customerCheck = await checkCustomer(params.siteId, user.userId);
    store.customerId = customerCheck.customerId;
    store.customerRole = customerCheck.role;
    store.customerPermissions = customerCheck.permissions;
    store.isCustomer = customerCheck.isCustomer;
  }
});
