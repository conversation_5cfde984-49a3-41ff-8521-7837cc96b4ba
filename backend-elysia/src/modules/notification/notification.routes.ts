import { config } from '@/core/config/environment';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { bearer } from '@elysiajs/bearer';
import { <PERSON><PERSON>, t } from 'elysia';
import * as jose from 'jose';
import {
  BulkNotificationSchema,
  ExpiryNotificationSchema,
  LowStockNotificationSchema,
  MarkAsReadSchema,
  MembershipNotificationSchema,
  NewProductNotificationSchema,
  NotificationQuerySchema,
  NotificationTemplateSchema,
  OrderPurchasedNotificationSchema,
  TopupNotificationSchema,
} from './notification.schema';
import * as notificationService from './notification.service';
import {
  addConnection,
  removeConnection,
  sendNotificationToUser,
  sendSettingsUpdateToUser,
  sendUnreadCountToUser,
} from './websocket.service';
// Middleware สำหรับตรวจสอบ JWT
const authMiddleware = async (bearer: string | undefined) => {
  if (!bearer) throw new HttpError(401, 'ไม่พบ token');

  try {
    const { payload } = await jose.jwtVerify(bearer, new TextEncoder().encode(config.jwtSecret));
    return payload;
  }
  catch (_error) {
    throw new HttpError(401, 'Token ไม่ถูกต้องหรือหมดอายุ');
  }
};

// WebSocket routes (ไม่ต้องใช้ auth plugin)
const notificationWebSocketRoutes = new Elysia()
  .use(bearer())
  .onError(({ code, error, set }: any) => {
    console.error('WebSocket Error:', error);
    set.status = 500;
    return {
      success: false,
      message: error.message || 'เกิดข้อผิดพลาด',
      statusMessage: 'เกิดข้อผิดพลาดในระบบ',
      timestamp: new Date().toISOString(),
    };
  })
  // WebSocket endpoint สำหรับ real-time notifications
  .ws('/ws', {
    body: t.Object({
      type: t.String(),
      data: t.Optional(t.Any()),
    }),

    async open(_ws) {
      console.log('Notification WebSocket connection opened');
    },

    async message(ws, message) {
      try {
        const { type, data } = message;

        switch (type) {
          case 'auth':
            await handleAuth(ws, data);
            break;

          case 'ping':
            ws.send(JSON.stringify({ type: 'pong' }));
            break;

          default:
            ws.send(
              JSON.stringify({
                type: 'error',
                message: 'ประเภทข้อความไม่ถูกต้อง',
              }),
            );
        }
      }
      catch (error) {
        console.error('Notification WebSocket message error:', error);
        ws.send(
          JSON.stringify({
            type: 'error',
            message: error instanceof HttpError ? error.message : 'เกิดข้อผิดพลาด',
          }),
        );
      }
    },

    async close(ws) {
      const connectionId = (ws as any).connectionId;
      if (connectionId) {
        removeConnection(connectionId);
      }
      console.log('Notification WebSocket connection closed');
    },
  });

// API routes (ใช้ auth plugin)
const notificationAPIRoutes = new Elysia()
  .use(userAuthPlugin)
  .use(bearer())
  .onError(({ error, set }) => {
    console.error('Notification API Error:', error);
    set.status = 500;
    return {
      success: false,
      message: (error as any)?.message || 'เกิดข้อผิดพลาด',
      statusMessage: 'เกิดข้อผิดพลาดในระบบ',
      timestamp: new Date().toISOString(),
    };
  })
  // ดึงการแจ้งเตือนทั้งหมด (รวมทั้ง site และ personal)
  .get(
    '/user',
    async ({ query, user }: any) => {
      const { page = '1', limit = '20', type, status } = query;
      const userId = user._id;

      try {
        const result = await notificationService.getUserNotifications(
          userId,
          parseInt(page),
          parseInt(limit),
          type,
          status,
        );

        return {
          success: true,
          message: 'ดึงการแจ้งเตือนสำเร็จ',
          statusMessage: 'สำเร็จ',
          timestamp: new Date().toISOString(),
          data: result,
        };
      }
      catch (error) {
        console.error('Error getting user notifications:', error);
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการแจ้งเตือน');
      }
    },
    {
      query: NotificationQuerySchema,
    },
  )
  // ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่านของ user
  .get(
    '/user/unread-count',
    async ({ user }: any) => {
      const userId = user._id;

      try {
        const count = await notificationService.getUserUnreadCount(userId);

        return {
          success: true,
          message: 'ดึงจำนวนการแจ้งเตือนสำเร็จ',
          statusMessage: 'สำเร็จ',
          timestamp: new Date().toISOString(),
          data: { unreadCount: count },
        };
      }
      catch (error) {
        console.error('Error getting user unread count:', error);
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงจำนวนการแจ้งเตือน');
      }
    },
  )
  // ดึงการแจ้งเตือนของ site
  .get(
    '/',
    async ({ query, user }: any) => {
      const { page = '1', limit = '20', type, status } = query;

      const result = await notificationService.getNotifications(
        user.siteId,
        user._id,
        Number(page),
        Number(limit),
        type,
        status,
      );

      return {
        success: true,
        message: 'ดึงการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      query: NotificationQuerySchema,
    },
  )
  // ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่าน
  .get('/unread-count', async ({ user }: any) => {
    const count = await notificationService.getUnreadCount(user.siteId, user._id);

    return {
      success: true,
      message: 'ดึงจำนวนการแจ้งเตือนสำเร็จ',
      statusMessage: 'สำเร็จ',
      timestamp: new Date().toISOString(),
      data: count,
    };
  })
  // ทำเครื่องหมายว่าอ่านแล้ว
  .post(
    '/mark-read',
    async ({ body, user }: any) => {
      const { notificationIds } = body;

      const result = await notificationService.markAsRead(user.siteId, notificationIds);

      return {
        success: true,
        message: 'ทำเครื่องหมายว่าอ่านแล้วสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: MarkAsReadSchema,
    },
  )
  // ลบการแจ้งเตือน
  .delete(
    '/:notificationId',
    async ({ params, user }: any) => {
      const { notificationId } = params;

      const result = await notificationService.deleteNotification(user.siteId, notificationId, user._id);

      return {
        success: true,
        message: 'ลบการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      params: t.Object({
        notificationId: t.String(),
      }),
    },
  )
  // ดึงสถิติการแจ้งเตือน
  .get('/stats', async ({ user }: any) => {
    const stats = await notificationService.getNotificationStats(user.siteId, user._id);

    return {
      success: true,
      message: 'ดึงสถิติการแจ้งเตือนสำเร็จ',
      statusMessage: 'สำเร็จ',
      timestamp: new Date().toISOString(),
      data: stats,
    };
  })
  // จัดการ Templates
  .get(
    '/templates',
    async ({ query, user }: any) => {
      const { type } = query;
      const templates = await notificationService.getNotificationTemplates(user.siteId, type);

      return {
        success: true,
        message: 'ดึงเทมเพลตการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: templates,
      };
    },
    {
      query: t.Object({
        type: t.Optional(
          t.Union([
            t.Literal('order'),
            t.Literal('product'),
            t.Literal('promotion'),
            t.Literal('system'),
            t.Literal('chat'),
            t.Literal('affiliate'),
            t.Literal('topup'),
            t.Literal('membership'),
            t.Literal('expiry'),
            t.Literal('inventory'),
            t.Literal('payment'),
            t.Literal('security'),
            t.Literal('marketing'),
          ]),
        ),
      }),
    },
  )
  .post(
    '/templates',
    async ({ body, user }: any) => {
      const template = await notificationService.createNotificationTemplate(user.siteId, body);

      return {
        success: true,
        message: 'สร้างเทมเพลตการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: template,
      };
    },
    {
      body: NotificationTemplateSchema,
    },
  )
  // จัดการการตั้งค่า
  .get('/settings', async ({ user }: any) => {
    const settings = await notificationService.getNotificationSettings(user.siteId, user._id);

    return {
      success: true,
      message: 'ดึงการตั้งค่าการแจ้งเตือนสำเร็จ',
      statusMessage: 'สำเร็จ',
      timestamp: new Date().toISOString(),
      data: settings,
    };
  })
  .put(
    '/settings',
    async ({ body, user }: any) => {
      const settings = await notificationService.updateNotificationSettings(user.siteId, user._id, body);

      return {
        success: true,
        message: 'อัปเดตการตั้งค่าการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: settings,
      };
    },
    {
      body: t.Object({
        preferences: t.Optional(
          t.Object({
            orderUpdates: t.Optional(t.Boolean()),
            productAlerts: t.Optional(t.Boolean()),
            promotions: t.Optional(t.Boolean()),
            systemMessages: t.Optional(t.Boolean()),
            chatMessages: t.Optional(t.Boolean()),
            affiliateUpdates: t.Optional(t.Boolean()),
            topupAlerts: t.Optional(t.Boolean()),
            membershipUpdates: t.Optional(t.Boolean()),
            expiryWarnings: t.Optional(t.Boolean()),
            inventoryAlerts: t.Optional(t.Boolean()),
            paymentNotifications: t.Optional(t.Boolean()),
            securityAlerts: t.Optional(t.Boolean()),
            marketingMessages: t.Optional(t.Boolean()),
          }),
        ),
        channels: t.Optional(
          t.Object({
            inApp: t.Optional(t.Boolean()),
            email: t.Optional(t.Boolean()),
            push: t.Optional(t.Boolean()),
            sms: t.Optional(t.Boolean()),
          }),
        ),
        quietHours: t.Optional(
          t.Object({
            enabled: t.Optional(t.Boolean()),
            startTime: t.Optional(t.String()),
            endTime: t.Optional(t.String()),
            timezone: t.Optional(t.String()),
          }),
        ),
      }),
    },
  )
  // ส่งการแจ้งเตือนจำนวนมาก
  .post(
    '/bulk',
    async ({ body, user }: any) => {
      const { recipients, notificationData } = body;

      const result = await notificationService.sendBulkNotifications(user.siteId, recipients, notificationData);

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนจำนวนมากสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: BulkNotificationSchema,
    },
  )
  // === API สำหรับการแจ้งเตือนเฉพาะ ===

  // แจ้งเตือนเติมเงิน
  .post(
    '/topup',
    async ({ body, user }: any) => {
      const { userId, amount, balance } = body;

      const result = await notificationService.notifyTopup(user.siteId, userId, amount, balance);

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนเติมเงินสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: TopupNotificationSchema,
    },
  )
  // แจ้งเตือนสมาชิกใหม่
  .post(
    '/new-member',
    async ({ body, user }: any) => {
      const { adminUsers, customerName, customerId } = body;

      const result = await notificationService.notifyNewMember(user.siteId, adminUsers, customerName, customerId);

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนสมาชิกใหม่สำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: MembershipNotificationSchema,
    },
  )
  // แจ้งเตือนวันหมดอายุ
  .post(
    '/expiry',
    async ({ body, user }: any) => {
      const { userId, expiryDate, daysLeft } = body;

      const result = await notificationService.notifyExpiry(user.siteId, userId, new Date(expiryDate), daysLeft);

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนวันหมดอายุสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: ExpiryNotificationSchema,
    },
  )
  // แจ้งเตือนสต็อกต่ำ
  .post(
    '/low-stock',
    async ({ body, user }: any) => {
      const { adminUsers, productName, productId, stockLevel, threshold } = body;

      const result = await notificationService.notifyLowStock(
        user.siteId,
        adminUsers,
        productName,
        productId,
        stockLevel,
        threshold,
      );

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนสต็อกต่ำสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: LowStockNotificationSchema,
    },
  )
  // แจ้งเตือนสินค้าใหม่
  .post(
    '/new-product',
    async ({ body, user }: any) => {
      const { customerIds, productName, productId, productImage } = body;

      const result = await notificationService.notifyNewProduct(
        user.siteId,
        customerIds,
        productName,
        productId,
        productImage,
      );

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนสินค้าใหม่สำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: NewProductNotificationSchema,
    },
  )
  // แจ้งเตือนมีคำสั่งซื้อ
  .post(
    '/order-purchased',
    async ({ body, user }: any) => {
      const { adminUsers, customerName, orderId, amount } = body;

      const result = await notificationService.notifyOrderPurchased(
        user.siteId,
        adminUsers,
        customerName,
        orderId,
        amount,
      );

      return {
        success: true,
        message: 'ส่งการแจ้งเตือนคำสั่งซื้อสำเร็จ',
        statusMessage: 'สำเร็จ',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      body: OrderPurchasedNotificationSchema,
    },
  );

// WebSocket handlers
async function handleAuth(ws: any, data: any) {
  try {
    const { token } = data;

    if (!token) {
      ws.send(JSON.stringify({
        type: 'auth_error',
        message: 'Token จำเป็น',
      }));
      return;
    }

    const payload = await authMiddleware(token);
    const userId = payload.sub as string;
    const userType = payload.userType as 'user' | 'customer';

    // เพิ่ม connection (ไม่ต้องใช้ siteId)
    const connectionId = addConnection(ws, userId, userType, 'global');
    (ws as any).connectionId = connectionId;

    // ส่งข้อมูลการยืนยัน
    ws.send(JSON.stringify({
      type: 'auth_success',
      data: {
        userId,
        userType,
      },
    }));

    // ส่งจำนวนการแจ้งเตือนที่ยังไม่อ่าน
    const unreadCount = await notificationService.getUserUnreadCount(userId);
    sendUnreadCountToUser(userId, unreadCount);

    console.log(`Notification WebSocket authenticated: ${userId} (${userType})`);
  }
  catch (error) {
    console.error('Notification WebSocket auth error:', error);
    ws.send(JSON.stringify({
      type: 'auth_error',
      message: error instanceof HttpError ? error.message : 'การยืนยันล้มเหลว',
    }));
  }
}

export const notificationRoutes = new Elysia({ prefix: '/notifications' })
  .use(notificationWebSocketRoutes)
  .use(notificationAPIRoutes);
