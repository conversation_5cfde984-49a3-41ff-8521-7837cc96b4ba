// Interface สำหรับ WebSocket connection
interface NotificationConnection {
  ws: any; // WebSocket instance from Elysia
  userId: string;
  userType: 'user' | 'customer';
  siteId?: string; // Optional สำหรับการแจ้งเตือนส่วนตัว
}

// เก็บ connections ทั้งหมด
const connections = new Map<string, NotificationConnection>();

// เก็บ connections ตาม userId
const userConnections = new Map<string, Set<string>>();

// เก็บ connections ตาม siteId (optional)
const siteConnections = new Map<string, Set<string>>();

// สร้าง connection ID
function generateConnectionId(): string {
  return `notif_conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// เพิ่ม connection ใหม่
export function addConnection(ws: any, userId: string, userType: 'user' | 'customer', siteId?: string): string {
  const connectionId = generateConnectionId();

  connections.set(connectionId, {
    ws,
    userId,
    userType,
    siteId,
  });

  // เพิ่มใน userConnections
  if (!userConnections.has(userId)) {
    userConnections.set(userId, new Set());
  }
  userConnections.get(userId)?.add(connectionId);

  // เพิ่มใน siteConnections (ถ้ามี siteId)
  if (siteId) {
    if (!siteConnections.has(siteId)) {
      siteConnections.set(siteId, new Set());
    }
    siteConnections.get(siteId)?.add(connectionId);
  }

  console.log(
    `New notification connection added: ${connectionId} for user ${userId} (${userType})${
      siteId ? ` in site ${siteId}` : ' (global)'
    }`,
  );
  return connectionId;
}

// ลบ connection
export function removeConnection(connectionId: string): void {
  const connection = connections.get(connectionId);
  if (!connection) return;

  // ลบจาก user connections
  const userConns = userConnections.get(connection.userId);
  if (userConns) {
    userConns.delete(connectionId);
    if (userConns.size === 0) {
      userConnections.delete(connection.userId);
    }
  }

  // ลบจาก site connections
  const siteConns = siteConnections.get(connection.siteId);
  if (siteConns) {
    siteConns.delete(connectionId);
    if (siteConns.size === 0) {
      siteConnections.delete(connection.siteId);
    }
  }

  connections.delete(connectionId);
  console.log(`Notification connection removed: ${connectionId}`);
}

// ส่งการแจ้งเตือนไปยังผู้ใช้เฉพาะ
export function sendNotificationToUser(userId: string, notification: any): void {
  const userConns = userConnections.get(userId);
  if (!userConns) return;

  userConns.forEach(connectionId => {
    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === 1) {
      // WebSocket.OPEN
      try {
        connection.ws.send(JSON.stringify({
          type: 'notification',
          data: notification,
        }));
      }
      catch (error) {
        console.error(`Error sending notification to user ${userId}:`, error);
        removeConnection(connectionId);
      }
    }
  });
}

// ส่งการแจ้งเตือนไปยังทุกคนในไซต์
export function broadcastNotificationToSite(siteId: string, notification: any, excludeUserId?: string): void {
  connections.forEach((connection, connectionId) => {
    if (connection.siteId === siteId && connection.userId !== excludeUserId) {
      if (connection.ws.readyState === 1) {
        // WebSocket.OPEN
        try {
          connection.ws.send(JSON.stringify({
            type: 'notification',
            data: notification,
          }));
        }
        catch (error) {
          console.error(`Error broadcasting notification to site ${siteId}:`, error);
          removeConnection(connectionId);
        }
      }
    }
  });
}

// ส่งการแจ้งเตือนไปยังผู้ใช้หลายคน
export function sendNotificationToUsers(userIds: string[], notification: any): void {
  userIds.forEach(userId => {
    sendNotificationToUser(userId, notification);
  });
}

// ส่งการแจ้งเตือนจำนวนการแจ้งเตือนที่ยังไม่อ่าน
export function sendUnreadCountToUser(userId: string, count: number): void {
  const userConns = userConnections.get(userId);
  if (!userConns) return;

  userConns.forEach(connectionId => {
    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === 1) {
      // WebSocket.OPEN
      try {
        connection.ws.send(JSON.stringify({
          type: 'unread_count',
          data: { count },
        }));
      }
      catch (error) {
        console.error(`Error sending unread count to user ${userId}:`, error);
        removeConnection(connectionId);
      }
    }
  });
}

// ส่งการแจ้งเตือนการอัปเดตการตั้งค่า
export function sendSettingsUpdateToUser(userId: string, settings: any): void {
  const userConns = userConnections.get(userId);
  if (!userConns) return;

  userConns.forEach(connectionId => {
    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === 1) {
      // WebSocket.OPEN
      try {
        connection.ws.send(JSON.stringify({
          type: 'settings_update',
          data: settings,
        }));
      }
      catch (error) {
        console.error(`Error sending settings update to user ${userId}:`, error);
        removeConnection(connectionId);
      }
    }
  });
}

// ดึงข้อมูล connection
export function getConnection(connectionId: string): NotificationConnection | undefined {
  return connections.get(connectionId);
}

// ดึงจำนวน connections ของผู้ใช้
export function getUserConnectionCount(userId: string): number {
  const userConns = userConnections.get(userId);
  return userConns ? userConns.size : 0;
}

// ดึงจำนวน connections ในไซต์
export function getSiteConnectionCount(siteId: string): number {
  const siteConns = siteConnections.get(siteId);
  return siteConns ? siteConns.size : 0;
}

// ตรวจสอบว่าผู้ใช้ออนไลน์หรือไม่
export function isUserOnline(userId: string): boolean {
  return getUserConnectionCount(userId) > 0;
}

// ดึงรายการผู้ใช้ออนไลน์ในไซต์
export function getOnlineUsersInSite(siteId: string): Array<{ userId: string; userType: 'user' | 'customer'; }> {
  const onlineUsers = new Map<string, 'user' | 'customer'>();

  connections.forEach(connection => {
    if (connection.siteId === siteId) {
      onlineUsers.set(connection.userId, connection.userType);
    }
  });

  return Array.from(onlineUsers.entries()).map(([userId, userType]) => ({
    userId,
    userType,
  }));
}

// ทำความสะอาด connections ที่ปิดแล้ว
export function cleanupConnections(): void {
  const toRemove: string[] = [];

  connections.forEach((connection, connectionId) => {
    if (connection.ws.readyState !== 1) {
      // Not WebSocket.OPEN
      toRemove.push(connectionId);
    }
  });

  toRemove.forEach(connectionId => {
    removeConnection(connectionId);
  });

  // แสดง log เฉพาะเมื่อมี connection ที่ถูกลบ
  if (toRemove.length > 0) {
    console.log(`🧹 Cleaned up ${toRemove.length} dead notification WebSocket connections`);
  }
}

// ตัวแปรสำหรับเก็บ interval ID
let cleanupInterval: NodeJS.Timeout | null = null;

// เริ่ม cleanup process
export function startCleanupProcess(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
  }

  // เรียกใช้ cleanup ทุก 30 วินาที
  cleanupInterval = setInterval(cleanupConnections, 30000);
  console.log('🚀 Notification WebSocket cleanup process started (every 30 seconds)');
}

// หยุด cleanup process
export function stopCleanupProcess(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('⏹️ Notification WebSocket cleanup process stopped');
  }
}

// เริ่ม cleanup process อัตโนมัติ
startCleanupProcess();
