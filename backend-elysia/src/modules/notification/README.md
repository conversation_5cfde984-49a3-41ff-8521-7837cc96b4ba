# ระบบแจ้งเตือน (Notification System)

ระบบแจ้งเตือนที่ครอบคลุมและยืดหยุ่น รองรับการแจ้งเตือนหลากหลายประเภทสำหรับระบบ e-commerce

## ประเภทการแจ้งเตือนที่รองรับ

### 1. การแจ้งเตือนพื้นฐาน

- **order** - คำสั่งซื้อ (สถานะคำสั่งซื้อ, การชำระเงิน)
- **product** - สินค้า (สินค้าใหม่, สินค้าหมด, ราคาเปลี่ยน)
- **promotion** - โปรโมชั่น (ส่วนลด, แคมเปญ)
- **system** - ระบบ (อัปเดต, บำรุงรักษา)
- **chat** - แชท (ข้อความใหม่)
- **affiliate** - พันธมิตร (คอมมิชชั่น, สถิติ)

### 2. การแจ้งเตือนเฉพาะธุรกิจ

- **topup** - เติมเงิน (เติมเงินสำเร็จ, ยอดคงเหลือ)
- **membership** - สมาชิกใหม่ (ลูกค้าสมัครใหม่)
- **expiry** - วันหมดอายุ (แจ้งเตือนก่อนหมดอายุ)
- **inventory** - สต็อกสินค้า (สต็อกต่ำ, สินค้าหมด)
- **payment** - การชำระเงิน (ชำระสำเร็จ, ล้มเหลว)
- **security** - ความปลอดภัย (เข้าสู่ระบบ, เปลี่ยนรหัสผ่าน)
- **marketing** - การตลาด (ข่าวสาร, อีเวนต์)

## ช่องทางการแจ้งเตือน

- **In-App** - แจ้งเตือนในแอป/เว็บไซต์
- **Email** - อีเมล
- **Push** - Push notification
- **SMS** - ข้อความ SMS

## ระดับความสำคัญ

- **low** - ความสำคัญต่ำ
- **medium** - ความสำคัญปานกลาง (ค่าเริ่มต้น)
- **high** - ความสำคัญสูง
- **urgent** - เร่งด่วน

## API Endpoints

### การจัดการการแจ้งเตือนทั่วไป

```
GET    /notifications              - ดึงการแจ้งเตือนทั้งหมด
GET    /notifications/unread-count - ดึงจำนวนที่ยังไม่อ่าน
POST   /notifications/mark-read    - ทำเครื่องหมายว่าอ่านแล้ว
DELETE /notifications/:id          - ลบการแจ้งเตือน
GET    /notifications/stats        - ดึงสถิติการแจ้งเตือน
```

### การจัดการ Templates

```
GET    /notifications/templates    - ดึง templates ทั้งหมด
POST   /notifications/templates    - สร้าง template ใหม่
```

### การตั้งค่าการแจ้งเตือน

```
GET    /notifications/settings     - ดึงการตั้งค่า
PUT    /notifications/settings     - อัปเดตการตั้งค่า
```

### การแจ้งเตือนเฉพาะ

```
POST   /notifications/topup           - แจ้งเตือนเติมเงิน
POST   /notifications/new-member      - แจ้งเตือนสมาชิกใหม่
POST   /notifications/expiry          - แจ้งเตือนวันหมดอายุ
POST   /notifications/low-stock       - แจ้งเตือนสต็อกต่ำ
POST   /notifications/new-product     - แจ้งเตือนสินค้าใหม่
POST   /notifications/order-purchased - แจ้งเตือนคำสั่งซื้อใหม่
POST   /notifications/bulk            - ส่งการแจ้งเตือนจำนวนมาก
```

## ตัวอย่างการใช้งาน

### 1. แจ้งเตือนเติมเงิน

```javascript
// เมื่อลูกค้าเติมเงินสำเร็จ
const response = await fetch('/api/notifications/topup', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user123',
    amount: 1000,
    balance: 5000,
  }),
});
```

### 2. แจ้งเตือนสมาชิกใหม่

```javascript
// เมื่อมีลูกค้าสมัครใหม่
const response = await fetch('/api/notifications/new-member', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adminUsers: ['admin1', 'admin2'],
    customerName: 'นายสมชาย ใจดี',
    customerId: 'customer123',
  }),
});
```

### 3. แจ้งเตือนวันหมดอายุ

```javascript
// แจ้งเตือนก่อนหมดอายุ 7 วัน
const response = await fetch('/api/notifications/expiry', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user123',
    expiryDate: '2025-02-01T00:00:00.000Z',
    daysLeft: 7,
  }),
});
```

### 4. แจ้งเตือนสต็อกต่ำ

```javascript
// เมื่อสินค้าเหลือน้อย
const response = await fetch('/api/notifications/low-stock', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adminUsers: ['admin1', 'admin2'],
    productName: 'iPhone 15 Pro',
    productId: 'product123',
    stockLevel: 5,
    threshold: 10,
  }),
});
```

### 5. แจ้งเตือนสินค้าใหม่

```javascript
// เมื่อมีสินค้าใหม่
const response = await fetch('/api/notifications/new-product', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    customerIds: ['customer1', 'customer2'],
    productName: 'Samsung Galaxy S24',
    productId: 'product456',
    productImage: 'https://example.com/image.jpg',
  }),
});
```

### 6. แจ้งเตือนคำสั่งซื้อใหม่

```javascript
// เมื่อมีคำสั่งซื้อใหม่
const response = await fetch('/api/notifications/order-purchased', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adminUsers: ['admin1', 'admin2'],
    customerName: 'นางสาวสมหญิง ใจงาม',
    orderId: 'order789',
    amount: 15000,
  }),
});
```

## การตั้งค่าการแจ้งเตือน

ผู้ใช้สามารถตั้งค่าการแจ้งเตือนได้ตามต้องการ:

```javascript
// อัปเดตการตั้งค่า
const response = await fetch('/api/notifications/settings', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    preferences: {
      orderUpdates: true,
      productAlerts: true,
      promotions: false,
      topupAlerts: true,
      expiryWarnings: true,
      marketingMessages: false,
    },
    channels: {
      inApp: true,
      email: true,
      push: false,
      sms: false,
    },
    quietHours: {
      enabled: true,
      startTime: '22:00',
      endTime: '08:00',
      timezone: 'Asia/Bangkok',
    },
  }),
});
```

## การสร้าง Template

สร้าง template สำหรับการแจ้งเตือนแต่ละประเภท:

```javascript
const response = await fetch('/api/notifications/templates', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'เติมเงินสำเร็จ',
    type: 'topup',
    title: 'เติมเงินสำเร็จ',
    message: 'คุณได้เติมเงิน {{amount}} บาท สำเร็จ ยอดคงเหลือ {{balance}} บาท',
    variables: ['amount', 'balance'],
    channels: {
      inApp: true,
      email: true,
      push: false,
      sms: false,
    },
  }),
});
```

## การใช้งานใน Frontend

### React/Vue.js Example

```javascript
// Hook สำหรับดึงการแจ้งเตือน
const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const fetchNotifications = async () => {
    const response = await fetch('/api/notifications');
    const data = await response.json();
    setNotifications(data.data.notifications);
    setUnreadCount(data.data.unreadCount);
  };

  const markAsRead = async notificationIds => {
    await fetch('/api/notifications/mark-read', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ notificationIds }),
    });
    fetchNotifications();
  };

  return { notifications, unreadCount, fetchNotifications, markAsRead };
};
```

## การผสานกับระบบอื่น

### 1. ระบบ Order

```javascript
// ใน order service
import * as notificationService from '@/modules/notification/notification.service';

// เมื่อมีคำสั่งซื้อใหม่
await notificationService.notifyOrderPurchased(
  siteId,
  adminUsers,
  customerName,
  orderId,
  amount,
);
```

### 2. ระบบ Inventory

```javascript
// ใน inventory service
import * as notificationService from '@/modules/notification/notification.service';

// เมื่อสต็อกต่ำ
if (stockLevel <= threshold) {
  await notificationService.notifyLowStock(
    siteId,
    adminUsers,
    productName,
    productId,
    stockLevel,
    threshold,
  );
}
```

### 3. ระบบ User/Customer

```javascript
// ใน user service
import * as notificationService from '@/modules/notification/notification.service';

// เมื่อมีสมาชิกใหม่
await notificationService.notifyNewMember(
  siteId,
  adminUsers,
  customerName,
  customerId,
);
```

## การตั้งค่า Cron Jobs

สำหรับการแจ้งเตือนอัตโนมัติ เช่น วันหมดอายุ:

```javascript
// ตัวอย่าง cron job สำหรับตรวจสอบวันหมดอายุ
import cron from 'node-cron';

// ทุกวันเวลา 09:00
cron.schedule('0 9 * * *', async () => {
  // ตรวจสอบผู้ใช้ที่ใกล้หมดอายุ
  const expiringUsers = await User.find({
    expiryDate: {
      $gte: new Date(),
      $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 วันข้างหน้า
    },
  });

  for (const user of expiringUsers) {
    const daysLeft = Math.ceil(
      (user.expiryDate - new Date()) / (24 * 60 * 60 * 1000),
    );

    await notificationService.notifyExpiry(
      user.siteId,
      user._id,
      user.expiryDate,
      daysLeft,
    );
  }
});
```

## ข้อดีของระบบใหม่

1. **ครอบคลุม** - รองรับการแจ้งเตือนทุกประเภทที่ธุรกิจต้องการ
2. **ยืดหยุ่น** - สามารถเพิ่มประเภทการแจ้งเตือนใหม่ได้ง่าย
3. **ปรับแต่งได้** - ผู้ใช้สามารถตั้งค่าการแจ้งเตือนตามต้องการ
4. **หลายช่องทาง** - รองรับการส่งผ่านหลายช่องทาง
5. **Template System** - จัดการข้อความแจ้งเตือนแบบ template
6. **Real-time Ready** - พร้อมสำหรับการแจ้งเตือนแบบ real-time
7. **Scalable** - รองรับการส่งการแจ้งเตือนจำนวนมาก
