import { Site } from './site.model';
// ลบ import addCustomer เพื่อไม่ให้สับสนกับ customer module
import { registerDomainVercel } from '@/core/utils/domain';
import { HttpError } from '@/core/utils/error';
import { createPaginationResult, getPaginationParams, PaginationParams } from '@/core/utils/pagination';
import { MenuItem } from '@/modules/menu/menu.model';
import { Page } from '@/modules/menu/page.model';
import { Role } from '@/modules/role/role.model';
import { recordSitePackageHistory } from '@/modules/site/sitePackageHistory.service';

// แพ็คเกจและราคา
const PACKAGE_PRICES = {
  monthly: { moneyPoint: 99, days: 30 }, // ✅ ลดจาก 99 เป็น 5 สำหรับทดสอบ
  yearly: { moneyPoint: 999, days: 365 }, // ✅ ลดจาก 999 เป็น 50
  permanent: { moneyPoint: 9999, days: 36500 }, // ✅ ลดจาก 9999 เป็น 100 (100 ปี)
} as const;

type PackageType = keyof typeof PACKAGE_PRICES;

// ฟังก์ชันสำหรับสร้างข้อมูลเริ่มต้นของเว็บไซต์
async function createDefaultSiteContent(siteId: string, siteName: string) {
  // สร้างหน้าเว็บเริ่มต้น
  const defaultPages = [
    {
      siteId,
      title: 'หน้าแรก',
      slug: 'home',
      content: `<h1>ยินดีต้อนรับสู่ ${siteName}</h1>
<p>นี่คือหน้าแรกของเว็บไซต์ของคุณ คุณสามารถแก้ไขเนื้อหานี้ได้ตามต้องการ</p>
<h2>เกี่ยวกับเรา</h2>
<p>เราคือ ${siteName} ที่พร้อมให้บริการที่ดีที่สุดแก่คุณ</p>`,
      template: 'default',
      isActive: true,
      isPublic: true,
      showInMenu: true,
      order: 1,
      seoSettings: {
        title: `${siteName} - หน้าแรก`,
        description: `ยินดีต้อนรับสู่ ${siteName} เว็บไซต์ที่ให้บริการที่ดีที่สุด`,
        keywords: `${siteName}, หน้าแรก, บริการ`,
      },
    },
    {
      siteId,
      title: 'เกี่ยวกับเรา',
      slug: 'about',
      content: `<h1>เกี่ยวกับ ${siteName}</h1>
<p>เราคือทีมงานที่มีประสบการณ์และความเชี่ยวชาญในการให้บริการ</p>
<h2>วิสัยทัศน์</h2>
<p>เป็นผู้นำในการให้บริการที่มีคุณภาพและตอบสนองความต้องการของลูกค้า</p>
<h2>พันธกิจ</h2>
<p>มุ่งมั่นในการพัฒนาและปรับปรุงบริการอย่างต่อเนื่อง</p>`,
      template: 'about',
      isActive: true,
      isPublic: true,
      showInMenu: true,
      order: 2,
      seoSettings: {
        title: `เกี่ยวกับเรา - ${siteName}`,
        description: `ทำความรู้จักกับ ${siteName} และทีมงานของเรา`,
        keywords: `${siteName}, เกี่ยวกับเรา, ทีมงาน`,
      },
    },
    {
      siteId,
      title: 'ติดต่อเรา',
      slug: 'contact',
      content: `<h1>ติดต่อเรา</h1>
<p>หากคุณมีคำถามหรือต้องการข้อมูลเพิ่มเติม สามารถติดต่อเราได้ตามช่องทางด้านล่าง</p>
<h2>ข้อมูลการติดต่อ</h2>
<ul>
<li>อีเมล: info@${siteName.toLowerCase().replace(/\s+/g, '')}.com</li>
<li>โทรศัพท์: 02-xxx-xxxx</li>
<li>ที่อยู่: กรุงเทพมหานคร ประเทศไทย</li>
</ul>`,
      template: 'contact',
      isActive: true,
      isPublic: true,
      showInMenu: true,
      order: 3,
      seoSettings: {
        title: `ติดต่อเรา - ${siteName}`,
        description: `ติดต่อ ${siteName} สำหรับข้อมูลเพิ่มเติมและการสอบถาม`,
        keywords: `${siteName}, ติดต่อเรา, สอบถาม`,
      },
    },
    {
      siteId,
      title: 'ข้อผิดพลาด 404',
      slug: 'error-404',
      content: `<h1>ไม่พบหน้าที่คุณต้องการ</h1>
<p>ขออภัย หน้าที่คุณกำลังมองหาไม่มีอยู่หรือถูกย้ายไปแล้ว</p>
<p><a href="/">กลับสู่หน้าแรก</a></p>`,
      template: 'default',
      isActive: true,
      isPublic: true,
      showInMenu: false,
      order: 99,
      seoSettings: {
        title: 'ไม่พบหน้า - 404',
        description: 'หน้าที่คุณต้องการไม่พบ',
        robots: 'noindex, nofollow',
      },
    },
    {
      siteId,
      title: 'ปิดปรุงระบบ',
      slug: 'maintenance',
      content: `<h1>ขณะนี้เว็บไซต์อยู่ระหว่างการปิดปรุงระบบ</h1>
<p>เราจะกลับมาให้บริการในเร็วๆ นี้ ขออภัยในความไม่สะดวก</p>`,
      template: 'default',
      isActive: true,
      isPublic: false,
      showInMenu: false,
      order: 98,
      seoSettings: {
        title: 'ปิดปรุงระบบ',
        description: 'เว็บไซต์อยู่ระหว่างการปิดปรุงระบบ',
        robots: 'noindex, nofollow',
      },
    },
  ];

  const createdPages = await Page.insertMany(defaultPages);

  // หา ID ของหน้าเริ่มต้น
  const homePage = createdPages.find(page => page.slug === 'home');
  const errorPage = createdPages.find(page => page.slug === 'error-404');
  const maintenancePage = createdPages.find(page => page.slug === 'maintenance');

  // สร้างเมนูเริ่มต้น
  const defaultMenuItems = [
    {
      siteId,
      title: 'หน้าแรก',
      url: '/',
      type: 'page',
      pageId: homePage?._id,
      order: 1,
      isActive: true,
      showInHeader: true,
      showInFooter: true,
      showInMobile: true,
      visibility: 'public',
      icon: 'home',
    },
    {
      siteId,
      title: 'เกี่ยวกับเรา',
      url: '/about',
      type: 'page',
      pageId: createdPages.find(page => page.slug === 'about')?._id,
      order: 2,
      isActive: true,
      showInHeader: true,
      showInFooter: true,
      showInMobile: true,
      visibility: 'public',
      icon: 'info',
    },
    {
      siteId,
      title: 'ติดต่อเรา',
      url: '/contact',
      type: 'page',
      pageId: createdPages.find(page => page.slug === 'contact')?._id,
      order: 3,
      isActive: true,
      showInHeader: true,
      showInFooter: true,
      showInMobile: true,
      visibility: 'public',
      icon: 'phone',
    },
  ];

  await MenuItem.insertMany(defaultMenuItems);

  // อัปเดต site ให้มี reference ไปยังหน้าเริ่มต้น
  await Site.findByIdAndUpdate(siteId, {
    'pageSettings.defaultPageId': homePage?._id,
    'pageSettings.errorPageId': errorPage?._id,
    'pageSettings.maintenancePageId': maintenancePage?._id,
  });

  return {
    pages: createdPages,
    menuItems: defaultMenuItems,
    defaultPageId: homePage?._id,
    errorPageId: errorPage?._id,
    maintenancePageId: maintenancePage?._id,
  };
}

export async function createSite({
  packageType,
  name,
  typeDomain,
  fullDomain,
  user,
}: {
  packageType: PackageType;
  name: string;
  typeDomain: string;
  fullDomain: string;
  user: any;
  [key: string]: any;
}) {
  console.log('createSite: fullDomain', fullDomain);
  console.log('createSite: packageType', packageType);
  console.log('createSite: name', name);
  console.log('createSite: typeDomain', typeDomain);
  console.log('createSite: user', user);

  const packageInfo = PACKAGE_PRICES[packageType];
  if (!packageInfo) {
    console.error('[createSite] Invalid packageType', { packageType });
    throw new HttpError(400, 'แพ็คเกจไม่ถูกต้อง');
  }

  if (user.moneyPoint < packageInfo.moneyPoint) {
    console.error('[createSite] Not enough moneyPoint', {
      userMoneyPoint: user.moneyPoint,
      required: packageInfo.moneyPoint,
    });
    throw new HttpError(400, 'ยอดเงินไม่เพียงพอ');
  }

  const { resRegisterDomain, resCheck } = await registerDomainVercel(fullDomain);
  console.log('[createSite] resRegisterDomain', resRegisterDomain);
  console.log('[createSite] resCheck', resCheck);

  // ✅ ส่วนของการหักค่าเช่าเว็บไซต์จาก user;
  const expiredAt = packageType === 'permanent'
    ? new Date(Date.now() + 36500 * 24 * 60 * 60 * 1000)
    : new Date(Date.now() + packageInfo.days * 24 * 60 * 60 * 1000);
  user.moneyPoint -= packageInfo.moneyPoint;
  await user.save();

  let site: any;

  try {
    site = await Site.create({ name, fullDomain, typeDomain, expiredAt });

    const { addSiteRole } = await import('@/modules/role/role.service');
    await addSiteRole(site._id, user._id, 'owner');
    console.log('[createSite] Owner role created for user:', user._id);

    const history = await recordSitePackageHistory({
      userId: user._id,
      siteId: site._id,
      packageType,
      action: 'create',
      expiredAt,
      moneyPointUsed: packageInfo.moneyPoint,
    });
    console.log('[createSite] history', history);
  }
  catch (err: any) {
    console.error('[createSite] Error creating site', err);
    if (err.code === 11000) {
      throw new HttpError(400, `ไม่สามารถสร้างเว็บไซต์ได้ เนื่องจาก ${fullDomain} ถูกใช้แล้ว`);
    }
    throw err;
  }

  // สร้างข้อมูลเริ่มต้น (menu และ page)
  const defaultContent = await createDefaultSiteContent(site._id, name);

  return {
    site,
    defaultContent,
    resRegisterDomain,
    resCheck,
  };
}

export async function renewSite({ packageType, site, user }: any) {
  const packageInfo = PACKAGE_PRICES[packageType as PackageType];
  if (!packageInfo) {
    throw new HttpError(400, 'แพ็คเกจไม่ถูกต้อง');
  }
  if (user.moneyPoint < packageInfo.moneyPoint) {
    throw new HttpError(400, 'ยอดเงินไม่เพียงพอ');
  }
  const now = new Date();
  const base = site.expiredAt > now ? site.expiredAt : now;
  site.expiredAt = new Date(base.getTime() + packageInfo.days * 24 * 60 * 60 * 1000);
  await site.save();
  user.moneyPoint -= packageInfo.moneyPoint;
  await user.save();
  await recordSitePackageHistory({
    userId: user._id,
    siteId: site._id,
    packageType,
    action: 'renew',
    expiredAt: site.expiredAt,
    moneyPointUsed: packageInfo.moneyPoint,
  });
  return site;
}

export async function getMySites(user: any, paginationParams?: PaginationParams, search?: string, status?: string) {
  const { page, limit, skip } = getPaginationParams(paginationParams);
  // สร้าง match conditions สำหรับ search และ status
  const matchConditions: any[] = [{ userId: user._id }];
  console.log('matchConditions', matchConditions);
  console.log('search', search);
  console.log('status', status);

  // เพิ่ม search condition ถ้ามี
  if (search) {
    matchConditions.push({
      'siteInfo.name': { $regex: search, $options: 'i' },
    });
  }

  // เพิ่ม status condition ถ้ามี
  if (status) {
    if (status === 'active') {
      matchConditions.push({ 'siteInfo.isActive': true });
    }
    else if (status === 'inactive') {
      matchConditions.push({ 'siteInfo.isActive': false });
    }
  }

  const roles = await Role.aggregate([
    { $match: { userId: user._id } },
    {
      $lookup: {
        from: 'sites',
        localField: 'siteId',
        foreignField: '_id',
        as: 'siteInfo',
        pipeline: [
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: '_id',
              as: 'ownerInfo',
              pipeline: [
                {
                  $project: {
                    email: 1,
                    firstName: 1,
                    lastName: 1,
                    moneyPoint: 1,
                    goldPoint: 1,
                  },
                },
              ],
            },
          },
          { $addFields: { ownerInfo: { $arrayElemAt: ['$ownerInfo', 0] } } },
          {
            $project: {
              _id: 1,
              name: 1,
              typeDomain: 1,
              fullDomain: 1,
              seoSettings: 1,
              isActive: 1,
              expiredAt: 1,
              createdAt: 1,
              updatedAt: 1,
              ownerInfo: 1,
            },
          },
        ],
      },
    },
    { $addFields: { siteInfo: { $arrayElemAt: ['$siteInfo', 0] } } },
    { $match: { siteInfo: { $ne: null } } },
    // เพิ่ม search และ status filtering
    ...(search ? [{ $match: { 'siteInfo.name': { $regex: search, $options: 'i' } } }] : []),
    ...(status
      ? [
        {
          $match: {
            'siteInfo.isActive': status === 'active' ? true : false,
          },
        },
      ]
      : []),
    { $project: { _id: 1, role: 1, siteId: 1, userId: 1, siteInfo: 1 } },
    { $sort: { 'siteInfo.createdAt': -1 } },
    { $skip: skip },
    { $limit: limit },
  ]);
  const totalCount = await Role.aggregate([
    { $match: { userId: user._id } },
    {
      $lookup: {
        from: 'sites',
        localField: 'siteId',
        foreignField: '_id',
        as: 'siteInfo',
      },
    },
    { $match: { siteInfo: { $ne: [] } } },
    // เพิ่ม search และ status filtering สำหรับ total count
    ...(search ? [{ $match: { 'siteInfo.name': { $regex: search, $options: 'i' } } }] : []),
    ...(status
      ? [
        {
          $match: {
            'siteInfo.isActive': status === 'active' ? true : false,
          },
        },
      ]
      : []),
    { $count: 'total' },
  ]);
  const total = totalCount.length > 0 ? totalCount[0].total : 0;
  const safeSites = roles.map((role: any) => ({
    _id: role.siteInfo._id,
    name: role.siteInfo.name,
    typeDomain: role.siteInfo.typeDomain,
    fullDomain: role.siteInfo.fullDomain,
    seoSettings: role.siteInfo.seoSettings,
    isActive: role.siteInfo.isActive,
    expiredAt: role.siteInfo.expiredAt,
    createdAt: role.siteInfo.createdAt,
    updatedAt: role.siteInfo.updatedAt,
    userRole: role.role,
    owner: role.siteInfo.ownerInfo,
  }));
  const paginationResult = createPaginationResult(page, limit, total);
  return { sites: safeSites, pagination: paginationResult };
}

export function getPackageInfo() {
  return {
    packages: [
      // { id: 'daily', name: 'รายวัน', moneyPoint: PACKAGE_PRICES.daily.moneyPoint, days: PACKAGE_PRICES.daily.days, description: 'เช่าเว็บไซตรายวัน' },
      // { id: 'weekly', name: 'รายสัปดาห์', moneyPoint: PACKAGE_PRICES.weekly.moneyPoint, days: PACKAGE_PRICES.weekly.days, description: 'เช่าเว็บไซตรายสัปดาห์' },
      {
        id: 'monthly',
        name: 'รายเดือน',
        moneyPoint: PACKAGE_PRICES.monthly.moneyPoint,
        days: PACKAGE_PRICES.monthly.days,
        description: 'เช่าเว็บไซตรายเดือน',
      },
      {
        id: 'yearly',
        name: 'รายปี',
        moneyPoint: PACKAGE_PRICES.yearly.moneyPoint,
        days: PACKAGE_PRICES.yearly.days,
        description: 'เช่าเว็บไซตรายปี',
      },
      {
        id: 'permanent',
        name: 'ถาวร',
        moneyPoint: PACKAGE_PRICES.permanent.moneyPoint,
        days: PACKAGE_PRICES.permanent.days,
        description: 'เช่าเว็บไซตถาวร',
      },
    ],
  };
}

// ฟังก์ชันสำหรับดึงข้อมูล site พร้อม aggregate menu และ page
export async function getSiteWithContent(siteId: string) {
  const result = await Site.aggregate([
    { $match: { _id: siteId } },
    {
      $lookup: {
        from: 'menuitems',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [{ $match: { isActive: true } }, { $sort: { order: 1 } }],
        as: 'menuItems',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [{ $match: { isActive: true } }, { $sort: { order: 1 } }],
        as: 'pages',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'defaultPageId',
        foreignField: '_id',
        as: 'defaultPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'errorPageId',
        foreignField: '_id',
        as: 'errorPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'maintenancePageId',
        foreignField: '_id',
        as: 'maintenancePage',
      },
    },
    {
      $addFields: {
        defaultPage: { $arrayElemAt: ['$defaultPage', 0] },
        errorPage: { $arrayElemAt: ['$errorPage', 0] },
        maintenancePage: { $arrayElemAt: ['$maintenancePage', 0] },
      },
    },
  ]);

  return result[0] || null;
}

// ฟังก์ชันสำหรับดึงข้อมูล site พร้อม menu สำหรับแสดงผล
export async function getSiteWithMenu(siteId: string) {
  const result = await Site.aggregate([
    { $match: { _id: siteId } },
    {
      $lookup: {
        from: 'menuitems',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          {
            $match: {
              isActive: true,
              $or: [{ showInHeader: true }, { showInFooter: true }, { showInMobile: true }],
            },
          },
          { $sort: { order: 1 } },
        ],
        as: 'menuItems',
      },
    },
  ]);

  return result[0] || null;
}

// ฟังก์ชันสำหรับดึงข้อมูล site พร้อม pages สำหรับแสดงผล
export async function getSiteWithPages(siteId: string) {
  const result = await Site.aggregate([
    { $match: { _id: siteId } },
    {
      $lookup: {
        from: 'pages',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          {
            $match: {
              isActive: true,
              isPublic: true,
            },
          },
          { $sort: { order: 1 } },
        ],
        as: 'pages',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'defaultPageId',
        foreignField: '_id',
        as: 'defaultPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'errorPageId',
        foreignField: '_id',
        as: 'errorPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'maintenancePageId',
        foreignField: '_id',
        as: 'maintenancePage',
      },
    },
    {
      $addFields: {
        defaultPage: { $arrayElemAt: ['$defaultPage', 0] },
        errorPage: { $arrayElemAt: ['$errorPage', 0] },
        maintenancePage: { $arrayElemAt: ['$maintenancePage', 0] },
      },
    },
  ]);

  return result[0] || null;
}

// ฟังก์ชันสำหรับดึงข้อมูล site โดย domain
export async function getSiteByDomain(fullDomain: string) {
  const result = await Site.aggregate([
    {
      $match: {
        fullDomain,
        isActive: true,
        expiredAt: { $gt: new Date() },
      },
    },
    {
      $lookup: {
        from: 'menuitems',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [{ $match: { isActive: true } }, { $sort: { order: 1 } }],
        as: 'menuItems',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          {
            $match: {
              isActive: true,
              isPublic: true,
            },
          },
          { $sort: { order: 1 } },
        ],
        as: 'pages',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'defaultPageId',
        foreignField: '_id',
        as: 'defaultPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'errorPageId',
        foreignField: '_id',
        as: 'errorPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'maintenancePageId',
        foreignField: '_id',
        as: 'maintenancePage',
      },
    },
    {
      $addFields: {
        defaultPage: { $arrayElemAt: ['$defaultPage', 0] },
        errorPage: { $arrayElemAt: ['$errorPage', 0] },
        maintenancePage: { $arrayElemAt: ['$maintenancePage', 0] },
      },
    },
  ]);

  return result[0] || null;
}

// Theme settings functions
export async function updateThemeSettings(siteId: string, themeSettings: any): Promise<any> {
  try {
    const site = await Site.findByIdAndUpdate(
      siteId,
      {
        themeSettings,
        updatedAt: new Date(),
      },
      { new: true },
    );

    if (!site) {
      throw new HttpError(404, 'Site not found');
    }

    return site;
  }
  catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Error updating theme settings:', error);
    throw new HttpError(500, 'Failed to update theme settings');
  }
}

export async function getThemeSettings(siteId: string): Promise<any> {
  try {
    const site = await Site.findById(siteId).select('themeSettings');

    if (!site) {
      throw new HttpError(404, 'Site not found');
    }

    return site.themeSettings || {};
  }
  catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Error getting theme settings:', error);
    throw new HttpError(500, 'Failed to get theme settings');
  }
}

export async function resetThemeSettings(siteId: string): Promise<any> {
  try {
    const defaultThemeSettings = {
      siteId,
      layout: 'grid',
      headerStyle: 'centered',
      footerStyle: 'simple',
      showSearch: true,
      showLanguageSelector: true,
      showThemeToggle: true,
      mobileMenuStyle: 'slide',
      desktopMenuStyle: 'horizontal',
      primaryColor: '#3b82f6',
      secondaryColor: '#6b7280',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      accentColor: '#f59e0b',
      fontFamily: 'Inter',
      fontSize: '16px',
      lineHeight: '1.6',
      fontWeight: '400',
      containerPadding: '1rem',
      sectionSpacing: '2rem',
      elementSpacing: '1rem',
      borderRadius: '0.5rem',
      spacing: '1rem',
      customCSS: '',
      isActive: true,
    };

    const site = await Site.findByIdAndUpdate(
      siteId,
      {
        themeSettings: defaultThemeSettings,
        updatedAt: new Date(),
      },
      { new: true },
    );

    if (!site) {
      throw new HttpError(404, 'Site not found');
    }

    return site;
  }
  catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Error resetting theme settings:', error);
    throw new HttpError(500, 'Failed to reset theme settings');
  }
}

// ฟังก์ชัน aggregate ที่มีประสิทธิภาพสำหรับดึงข้อมูล site พร้อมข้อมูลที่เกี่ยวข้อง
export async function getSiteWithAggregatedContent(siteId: string) {
  const result = await Site.aggregate([
    { $match: { _id: siteId } },
    // Lookup menu items
    {
      $lookup: {
        from: 'menuitems',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          { $match: { isActive: true } },
          { $sort: { order: 1 } },
          {
            $project: {
              _id: 1,
              name: 1,
              url: 1,
              order: 1,
              showInHeader: 1,
              showInFooter: 1,
              showInMobile: 1,
              parentId: 1,
              children: 1,
            },
          },
        ],
        as: 'menuItems',
      },
    },
    // Lookup pages
    {
      $lookup: {
        from: 'pages',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          { $match: { isActive: true } },
          { $sort: { order: 1 } },
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
              order: 1,
              isPublic: 1,
              metaTitle: 1,
              metaDescription: 1,
            },
          },
        ],
        as: 'pages',
      },
    },
    // Lookup default page
    {
      $lookup: {
        from: 'pages',
        localField: 'defaultPageId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
              metaTitle: 1,
              metaDescription: 1,
            },
          },
        ],
        as: 'defaultPage',
      },
    },
    // Lookup error page
    {
      $lookup: {
        from: 'pages',
        localField: 'errorPageId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
            },
          },
        ],
        as: 'errorPage',
      },
    },
    // Lookup maintenance page
    {
      $lookup: {
        from: 'pages',
        localField: 'maintenancePageId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
            },
          },
        ],
        as: 'maintenancePage',
      },
    },
    // Add computed fields
    {
      $addFields: {
        defaultPage: { $arrayElemAt: ['$defaultPage', 0] },
        errorPage: { $arrayElemAt: ['$errorPage', 0] },
        maintenancePage: { $arrayElemAt: ['$maintenancePage', 0] },
        // Add computed fields for better performance
        hasMenuItems: { $gt: [{ $size: '$menuItems' }, 0] },
        hasPages: { $gt: [{ $size: '$pages' }, 0] },
        // Add status based on expiry
        status: {
          $cond: {
            if: { $lt: ['$expiredAt', new Date()] },
            then: 'expired',
            else: {
              $cond: {
                if: { $lt: ['$expiredAt', { $add: [new Date(), 7 * 24 * 60 * 60 * 1000] }] },
                then: 'expiring_soon',
                else: 'active',
              },
            },
          },
        },
      },
    },
    // Project only needed fields
    {
      $project: {
        _id: 1,
        name: 1,
        fullDomain: 1,
        typeDomain: 1,
        isActive: 1,
        expiredAt: 1,
        createdAt: 1,
        updatedAt: 1,
        themeSettings: 1,
        seoSettings: 1,
        navigationSettings: 1,
        pageSettings: 1,
        loadingSettings: 1,
        analyticsSettings: 1,
        securitySettings: 1,
        menuItems: 1,
        pages: 1,
        defaultPage: 1,
        errorPage: 1,
        maintenancePage: 1,
        hasMenuItems: 1,
        hasPages: 1,
        status: 1,
      },
    },
  ]);

  return result[0] || null;
}

// ฟังก์ชัน aggregate สำหรับดึงข้อมูล site พร้อม menu เฉพาะ
export async function getSiteWithAggregatedMenu(siteId: string) {
  const result = await Site.aggregate([
    { $match: { _id: siteId } },
    {
      $lookup: {
        from: 'menuitems',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          {
            $match: {
              isActive: true,
              $or: [{ showInHeader: true }, { showInFooter: true }, { showInMobile: true }],
            },
          },
          { $sort: { order: 1 } },
          {
            $project: {
              _id: 1,
              name: 1,
              url: 1,
              order: 1,
              showInHeader: 1,
              showInFooter: 1,
              showInMobile: 1,
              parentId: 1,
            },
          },
        ],
        as: 'menuItems',
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        fullDomain: 1,
        navigationSettings: 1,
        themeSettings: 1,
        menuItems: 1,
      },
    },
  ]);

  return result[0] || null;
}

// ฟังก์ชัน aggregate สำหรับดึงข้อมูล site พร้อม pages เฉพาะ
export async function getSiteWithAggregatedPages(siteId: string) {
  const result = await Site.aggregate([
    { $match: { _id: siteId } },
    {
      $lookup: {
        from: 'pages',
        localField: '_id',
        foreignField: 'siteId',
        pipeline: [
          {
            $match: {
              isActive: true,
              isPublic: true,
            },
          },
          { $sort: { order: 1 } },
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
              order: 1,
              metaTitle: 1,
              metaDescription: 1,
            },
          },
        ],
        as: 'pages',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'defaultPageId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
              metaTitle: 1,
              metaDescription: 1,
            },
          },
        ],
        as: 'defaultPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'errorPageId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
            },
          },
        ],
        as: 'errorPage',
      },
    },
    {
      $lookup: {
        from: 'pages',
        localField: 'maintenancePageId',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              slug: 1,
              content: 1,
            },
          },
        ],
        as: 'maintenancePage',
      },
    },
    {
      $addFields: {
        defaultPage: { $arrayElemAt: ['$defaultPage', 0] },
        errorPage: { $arrayElemAt: ['$errorPage', 0] },
        maintenancePage: { $arrayElemAt: ['$maintenancePage', 0] },
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        fullDomain: 1,
        pageSettings: 1,
        navigationSettings: 1,
        themeSettings: 1,
        seoSettings: 1,
        pages: 1,
        defaultPage: 1,
        errorPage: 1,
        maintenancePage: 1,
      },
    },
  ]);

  return result[0] || null;
}

// ฟังก์ชันสำหรับตรวจสอบสิทธิ์เข้าถึง site
export async function validateSiteAccess(siteId: string, userId: string): Promise<void> {
  try {
    // ตรวจสอบว่า user มีสิทธิ์เข้าถึง site นี้หรือไม่
    const role = await Role.findOne({ siteId, userId });

    if (!role) {
      throw new HttpError(403, 'คุณไม่มีสิทธิ์เข้าถึงเว็บไซต์นี้');
    }

    // ตรวจสอบว่า site ยังใช้งานได้หรือไม่
    const site = await Site.findById(siteId);
    if (!site) {
      throw new HttpError(404, 'ไม่พบเว็บไซต์ที่ระบุ');
    }

    if (!site.isActive) {
      throw new HttpError(403, 'เว็บไซต์นี้ถูกปิดใช้งาน');
    }

    if (site.expiredAt < new Date()) {
      throw new HttpError(403, 'เว็บไซต์นี้หมดอายุแล้ว');
    }
  }
  catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Error validating site access:', error);
    throw new HttpError(500, 'ไม่สามารถตรวจสอบสิทธิ์เข้าถึงได้');
  }
}
