import { Site } from '@/modules/site/site.model';
import { SitePackageHistory } from '@/modules/site/sitePackageHistory.model';
import { User } from '@/modules/user/user.model';

export async function recordSitePackageHistory({
  userId,
  siteId,
  packageType,
  action,
  expiredAt,
  moneyPointUsed,
  subscriptionId,
}: {
  userId: string;
  siteId: string;
  packageType: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'permanent';
  action: 'create' | 'renew';
  expiredAt: Date;
  moneyPointUsed: number;
  subscriptionId?: string;
}) {
  await SitePackageHistory.create({
    userId,
    siteId,
    packageType,
    action,
    expiredAt,
    moneyPointUsed,
    subscriptionId,
  });
}

export async function getSitePackageHistory({ userId, siteId }: { userId?: string; siteId?: string; }) {
  const query: any = {};
  if (userId) query.userId = userId;
  if (siteId) query.siteId = siteId;
  return SitePackageHistory.find(query).sort({ createdAt: -1 });
}

// ฟังก์ชันใหม่สำหรับดึงประวัติที่ครอบคลุม
export async function getUserHistory({
  userId,
  type,
  page = 1,
  limit = 20,
}: {
  userId: string;
  type?: 'package' | 'topup' | 'createsite' | 'all';
  page?: number;
  limit?: number;
}) {
  const skip = (page - 1) * limit;

  // ถ้า type เป็น 'all' หรือไม่ระบุ ให้ดึงทุกประเภท
  if (!type || type === 'all') {
    const [packageHistory, topupHistory, createHistory] = await Promise.all([
      // ประวัติแพ็คเกจ
      SitePackageHistory.find({ userId })
        .populate('siteId', 'name fullDomain')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),

      // ประวัติ topup (จาก user model)
      User.findById(userId).select('moneyPoint goldPoint'),

      // ประวัติการสร้างเว็บไซต์ (จาก site model)
      Site.find({ userId }).select('name fullDomain createdAt expiredAt'),
    ]);

    return {
      packageHistory: packageHistory.map(item => ({
        type: 'package',
        action: item.action,
        packageType: item.packageType,
        moneyPointUsed: item.moneyPointUsed,
        expiredAt: item.expiredAt,
        site: item.siteId,
        createdAt: (item as any).createdAt,
      })),
      topupHistory: topupHistory
        ? [
          {
            type: 'topup',
            action: 'current_balance',
            moneyPoint: topupHistory.moneyPoint,
            goldPoint: topupHistory.goldPoint,
            createdAt: new Date(),
          },
        ]
        : [],
      createHistory: createHistory.map(site => ({
        type: 'createsite',
        action: 'site_created',
        siteName: (site as any).name,
        fullDomain: (site as any).fullDomain,
        expiredAt: site.expiredAt,
        createdAt: (site as any).createdAt,
      })),
    };
  }

  // ถ้า type เป็น 'package'
  if (type === 'package') {
    const history = await SitePackageHistory.find({ userId })
      .populate('siteId', 'name fullDomain')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    return history.map(item => ({
      type: 'package',
      action: item.action,
      packageType: item.packageType,
      moneyPointUsed: item.moneyPointUsed,
      expiredAt: item.expiredAt,
      site: item.siteId,
      createdAt: (item as any).createdAt,
    }));
  }

  // ถ้า type เป็น 'topup'
  if (type === 'topup') {
    const user = await User.findById(userId).select('moneyPoint goldPoint');
    return user
      ? [
        {
          type: 'topup',
          action: 'current_balance',
          moneyPoint: user.moneyPoint,
          goldPoint: user.goldPoint,
          createdAt: new Date(),
        },
      ]
      : [];
  }

  // ถ้า type เป็น 'createsite'
  if (type === 'createsite') {
    const sites = await Site.find({ userId })
      .select('name fullDomain createdAt expiredAt')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    return sites.map(site => ({
      type: 'createsite',
      action: 'site_created',
      siteName: (site as any).name,
      fullDomain: (site as any).fullDomain,
      expiredAt: site.expiredAt,
      createdAt: (site as any).createdAt,
    }));
  }

  return [];
}
