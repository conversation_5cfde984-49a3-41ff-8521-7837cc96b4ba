import { getSiteByIdOrDomain, sitePlugin } from '@/core/middleware/checkSite';
import { userAuthPlugin } from '@/core/plugins/auth';
import { checkDomainAvailability, getFullDomain } from '@/core/utils/domain';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import { createSiteSchema } from './site.schema';
import {
  createSite,
  getMySites,
  getPackageInfo,
  getSiteWithAggregatedContent,
  getSiteWithAggregatedMenu,
  getSiteWithAggregatedPages,
  getThemeSettings,
  renewSite,
  resetThemeSettings,
  updateThemeSettings,
} from './site.service';
import { getSitePackageHistory } from './sitePackageHistory.service';

export const siteRoutes = new Elysia({ prefix: '/site' })
  // Public test endpoint for theme
  .get('/test-theme', () => {
    return {
      success: true,
      message: 'Theme routes are working',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: {
        test: 'Theme API is accessible',
      },
    };
  })
  // Mock theme endpoint for testing
  .get('/mock-site/theme', () => {
    const mockThemeSettings = {
      primaryColor: '#3b82f6',
      secondaryColor: '#6b7280',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      fontFamily: 'Inter',
      borderRadius: '0.5rem',
      spacing: '1rem',
      layout: 'grid',
      headerStyle: 'centered',
      footerStyle: 'simple',
      customCSS: '',
    };

    return {
      success: true,
      message: 'ดึงข้อมูลธีมสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: mockThemeSettings,
    };
  })
  .get('/packages', async () => {
    const packages = getPackageInfo();
    return {
      success: true,
      message: 'ดึงข้อมูลแพ็คเกจสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: packages,
    };
  })
  .get('/check-domain', async ({ query }: any) => {
    try {
      const { typeDomain, subDomain, mainDomain, customDomain } = query;

      const fullDomain = await getFullDomain(typeDomain, subDomain, mainDomain, customDomain);

      const { available } = await checkDomainAvailability(fullDomain);
      return {
        success: true,
        data: {
          available,
          fullDomain,
        },
        message: available ? 'โดเมนนี้ว่าง' : 'โดเมนนี้ถูกใช้แล้ว',
        statusMessage: available ? 'สำเร็จ!' : 'ล้มเหลว!',
        timestamp: new Date().toISOString(),
      };
    }
    catch (error: any) {
      console.error('[check-domain] Unexpected error:', error);
      // ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
      if (error instanceof HttpError) throw error;
      throw new HttpError(500, 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน');
    }
  })
  // Public route สำหรับดึงข้อมูล site โดย fullDomain หรือ siteId (ไม่ต้อง auth)
  .get('/get-site/:fullDomain/:siteId', async ({ params }: any) => {
    const { fullDomain, siteId } = params;
    const site: any = await getSiteByIdOrDomain(siteId, fullDomain);
    console.log('site', site);

    if (!site) {
      throw new HttpError(404, 'ไม่พบเว็บไซต์หรือเว็บไซต์หมดอายุ');
    }

    return {
      success: true,
      message: 'ดึงข้อมูลเว็บไซต์สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: {
        site: {
          _id: site._id,
          name: site.name,
          isActive: site.isActive,
          expiredAt: site.expiredAt,
          createdAt: site.createdAt,
          updatedAt: site.updatedAt,
          fullDomain: site.fullDomain,
          themeSettings: site.themeSettings,
          seoSettings: site.seoSettings,
          navigationSettings: site.navigationSettings,
          pageSettings: site.pageSettings,
          loadingSettings: site.loadingSettings,
        },
        menuItems: site.menuItems,
        pages: site.pages,
        defaultPage: site.defaultPage,
        errorPage: site.errorPage,
        maintenancePage: site.maintenancePage,
      },
    };
  })
  .use(userAuthPlugin)
  .post(
    '/create',
    async ({ body, user }: any) => {
      const { name, typeDomain, subDomain, mainDomain, customDomain, plan } = body;

      console.log(name, typeDomain, subDomain, mainDomain, customDomain, plan);

      // Validate domain fields based on typeDomain
      if (typeDomain === 'subdomain') {
        if (!subDomain || subDomain.trim() === '') {
          throw new HttpError(400, 'กรุณากรอกซับโดเมน');
        }
        if (!mainDomain || mainDomain.trim() === '') {
          throw new HttpError(400, 'กรุณากรอกโดเมนหลัก');
        }
        if (customDomain && customDomain.trim() !== '') {
          throw new HttpError(400, 'ไม่ควรมีโดเมนส่วนตัวเมื่อเลือกซับโดเมน');
        }
        // Set customDomain to undefined for subdomain
        body.customDomain = undefined;
      }
      else if (typeDomain === 'custom') {
        if (!customDomain || customDomain.trim() === '') {
          throw new HttpError(400, 'กรุณากรอกโดเมนส่วนตัว');
        }
        if ((subDomain && subDomain.trim() !== '') || (mainDomain && mainDomain.trim() !== '')) {
          throw new HttpError(400, 'ไม่ควรมีซับโดเมนหรือโดเมนหลักเมื่อเลือกโดเมนส่วนตัว');
        }
        // Set subDomain and mainDomain to undefined for custom domain
        body.subDomain = undefined;
        body.mainDomain = undefined;
      }

      const fullDomain = await getFullDomain(typeDomain, subDomain, mainDomain, customDomain);

      const { available } = await checkDomainAvailability(fullDomain);

      if (!available) {
        throw new HttpError(400, `ไม่สามารถสร้างเว็บไซต์ได้ เนื่องจาก ${fullDomain} ถูกใช้แล้ว`);
      }

      const { site, resRegisterDomain, resCheck } = await createSite({
        packageType: plan,
        name,
        typeDomain,
        fullDomain,
        user,
      });

      return {
        success: true,
        message: 'สร้างเว็บไซต์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: { site, resRegisterDomain, resCheck },
      };
    },
    {
      body: createSiteSchema,
      // beforeHandle: async (c: any) => {
      //   await getResultUser(c);
      // }
    },
  )
  .get('/my-sites', async ({ query, user }: any) => {
    console.log('query', query);
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 20;
    const search = query.search || undefined;
    const status = query.status || undefined;

    const { sites, pagination } = await getMySites(user, { page, limit }, search, status);
    console.log('sites', sites);

    return {
      success: true,
      message: 'ดึงข้อมูลเว็บไซต์สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: { sites, pagination },
    };
  })
  .use(sitePlugin)
  .get(
    '/:siteId/renew',
    async ({ body, user, site }: any) => {
      const { packageType } = body;
      const result = await renewSite({ packageType, site, user });
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      // beforeHandle: async (c: any) => {
      //   await getResultSite(c, { checkExpiry: true, checkActive: true });
      //   await getResultUser(c);
      // }
    },
  )
  .get(
    '/:siteId/history',
    async ({ params, user }: any) => {
      const { siteId } = params;
      const result = await getSitePackageHistory({ userId: user._id, siteId });
      return {
        success: true,
        message: 'ดึงประวัติการซื้อสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      // beforeHandle: async (c: any) => {
      //   await getResultSite(c, { checkExpiry: true, checkActive: true });
      //   await getResultUser(c);
      // }
    },
  )
  // ดึงข้อมูล site พร้อม menu และ page ทั้งหมด (ใช้ aggregate)
  .get('/:siteId/content', async ({ params }: any) => {
    const { siteId } = params;
    const site = await getSiteWithAggregatedContent(siteId);

    if (!site) {
      throw new HttpError(404, 'ไม่พบเว็บไซต์');
    }

    return {
      success: true,
      message: 'ดึงข้อมูลเว็บไซต์พร้อมเนื้อหาสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: site,
    };
  })
  // ดึงข้อมูล site พร้อม menu สำหรับแสดงผล (ใช้ aggregate)
  .get('/:siteId/menu', async ({ params }: any) => {
    const { siteId } = params;
    const site: any = await getSiteWithAggregatedMenu(siteId);

    if (!site) {
      throw new HttpError(404, 'ไม่พบเว็บไซต์');
    }

    return {
      success: true,
      message: 'ดึงข้อมูลเมนูเว็บไซต์สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: {
        site: {
          _id: site._id,
          fullDomain: site.fullDomain,
          navigationSettings: site.navigationSettings,
          themeSettings: site.themeSettings,
        },
        menuItems: site.menuItems,
      },
    };
  })
  // ดึงข้อมูล site พร้อม pages สำหรับแสดงผล (ใช้ aggregate)
  .get('/:siteId/pages', async ({ params }: any) => {
    const { siteId } = params;
    const site: any = await getSiteWithAggregatedPages(siteId);

    if (!site) {
      throw new HttpError(404, 'ไม่พบเว็บไซต์');
    }

    return {
      success: true,
      message: 'ดึงข้อมูลหน้าเว็บไซต์สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: {
        site: {
          _id: site._id,
          name: site.name,
          isActive: site.isActive,
          expiredAt: site.expiredAt,
          createdAt: site.createdAt,
          updatedAt: site.updatedAt,
          fullDomain: site.fullDomain,
          pageSettings: site.pageSettings,
          navigationSettings: site.navigationSettings,
          themeSettings: site.themeSettings,
          seoSettings: site.seoSettings,
        },
        pages: site.pages,
        defaultPage: site.defaultPage,
        errorPage: site.errorPage,
        maintenancePage: site.maintenancePage,
      },
    };
  })
  // Theme settings routes
  .get('/:siteId/theme', async ({ params }: any) => {
    try {
      const { siteId } = params;
      const themeSettings = await getThemeSettings(siteId);

      return {
        success: true,
        message: 'ดึงข้อมูลธีมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: themeSettings,
      };
    }
    catch (error) {
      console.error('Error getting theme settings:', error);
      return {
        success: false,
        message: error instanceof HttpError ? error.message : 'Failed to get theme settings',
        statusMessage: 'ล้มเหลว!',
        timestamp: new Date().toISOString(),
      };
    }
  })
  .put('/:siteId/theme', async ({ params, body }: any) => {
    try {
      const { siteId } = params;
      const site = await updateThemeSettings(siteId, body);

      return {
        success: true,
        message: 'อัปเดตธีมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: site.themeSettings,
      };
    }
    catch (error) {
      console.error('Error updating theme settings:', error);
      return {
        success: false,
        message: error instanceof HttpError ? error.message : 'Failed to update theme settings',
        statusMessage: 'ล้มเหลว!',
        timestamp: new Date().toISOString(),
      };
    }
  })
  .post('/:siteId/theme/reset', async ({ params }: any) => {
    try {
      const { siteId } = params;
      const site = await resetThemeSettings(siteId);

      return {
        success: true,
        message: 'รีเซ็ตธีมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: site.themeSettings,
      };
    }
    catch (error) {
      console.error('Error resetting theme settings:', error);
      return {
        success: false,
        message: error instanceof HttpError ? error.message : 'Failed to reset theme settings',
        statusMessage: 'ล้มเหลว!',
        timestamp: new Date().toISOString(),
      };
    }
  });
