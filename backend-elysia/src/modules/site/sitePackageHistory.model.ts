import { generateFileId } from '@/core/utils/idGenerator';
import { model, Schema } from 'mongoose';

export interface ISitePackageHistory extends Document {
  _id: string;
  userId: string;
  siteId: string;
  packageType: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'permanent';
  action: 'create' | 'renew';
  expiredAt: Date;
  moneyPointUsed: number;
}

const sitePackageHistorySchema = new Schema<ISitePackageHistory>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    userId: { type: String, required: true, ref: 'User' },
    siteId: { type: String, required: true, ref: 'Site' },
    packageType: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly', 'permanent'],
      required: true,
    },
    action: { type: String, enum: ['create', 'renew'], required: true },
    expiredAt: {
      type: Date,
      required: true,
      min: new Date(),
      max: new Date(new Date().setFullYear(new Date().getFullYear() + 10)),
    },
    moneyPointUsed: { type: Number, required: true, default: 0, min: 0, max: 9999 },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

export const SitePackageHistory = model<ISitePackageHistory>('SitePackageHistory', sitePackageHistorySchema);
