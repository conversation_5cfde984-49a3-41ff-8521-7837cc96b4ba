import mongoose, { type Document, Schema } from 'mongoose';
import { generateFileId } from '../../core/utils/idGenerator';

export type ProductType = 'physical' | 'digital' | 'service' | 'subscription';
export type SaleChannel = 'online' | 'offline' | 'both';

export interface IProductVariant {
  _id: string;
  name: string; // เช่น "สีแดง ไซส์ M"
  sku?: string;
  price?: number; // ราคาเฉพาะ variant (ถ้าไม่มีจะใช้ราคาหลัก)
  stock?: number;
  attributes: Record<string, string>; // { "color": "red", "size": "M" }
  images?: string[];
  isActive: boolean;
}

export interface IProductImage {
  url: string;
  alt?: string;
  isPrimary: boolean;
  variantId?: string; // ถ้าเป็นรูปของ variant เฉพาะ
}

export interface IDigitalAsset {
  name: string;
  url: string;
  fileSize?: number;
  fileType?: string;
  downloadLimit?: number; // จำนวนครั้งที่ดาวน์โหลดได้
  expiryDays?: number; // วันหมดอายุหลังซื้อ
}

export interface IShipping {
  weight?: number; // กรัม
  dimensions?: {
    length: number; // ซม.
    width: number;
    height: number;
  };
  shippingClass?: string;
}

export interface IProduct extends Document {
  _id: string;
  siteId: string;
  name: string;
  slug: string; // URL-friendly name
  type: ProductType;
  saleChannel: SaleChannel;

  // ราคาและสต็อก
  price: number;
  compareAtPrice?: number; // ราคาเปรียบเทียบ (ราคาขีดฆ่า)
  costPrice?: number; // ต้นทุน
  stock?: number;
  trackStock: boolean;
  allowBackorder: boolean;

  // ข้อมูลพื้นฐาน
  categoryId?: string;
  description?: string;
  shortDescription?: string;
  tags?: string[];

  // รูปภาพ
  images: IProductImage[];

  // Variants (สี, ขนาด, etc.)
  hasVariants: boolean;
  variants: IProductVariant[];
  variantAttributes: string[]; // ["color", "size"]

  // Digital products
  digitalAssets: IDigitalAsset[];

  // การจัดส่ง
  shipping: IShipping;

  // SEO
  seoTitle?: string;
  seoDescription?: string;

  // การแสดงผล
  featured: boolean;
  isActive: boolean;

  // Pre-order
  allowPreOrder: boolean;

  // ข้อมูลเพิ่มเติม
  customFields?: Record<string, any>;

  createdAt: Date;
  updatedAt: Date;
}

// Sub-schemas
const productImageSchema = new Schema(
  {
    url: { type: String, required: true },
    alt: { type: String },
    isPrimary: { type: Boolean, default: false },
    variantId: { type: String },
  },
  { _id: false },
);

const productVariantSchema = new Schema(
  {
    _id: { type: String, default: () => generateFileId(3) },
    name: { type: String, required: true },
    sku: { type: String },
    price: { type: Number },
    stock: { type: Number },
    attributes: { type: Schema.Types.Mixed, default: {} },
    images: [{ type: String }],
    isActive: { type: Boolean, default: true },
  },
  { _id: false },
);

const digitalAssetSchema = new Schema(
  {
    name: { type: String, required: true },
    url: { type: String, required: true },
    fileSize: { type: Number },
    fileType: { type: String },
    downloadLimit: { type: Number },
    expiryDays: { type: Number },
  },
  { _id: false },
);

const shippingSchema = new Schema(
  {
    weight: { type: Number },
    dimensions: {
      length: { type: Number },
      width: { type: Number },
      height: { type: Number },
    },
    shippingClass: { type: String },
  },
  { _id: false },
);

const productSchema = new Schema<IProduct>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    slug: { type: String, required: true },
    type: {
      type: String,
      enum: ['physical', 'digital', 'service', 'subscription'],
      required: true,
      default: 'physical',
    },
    saleChannel: {
      type: String,
      enum: ['online', 'offline', 'both'],
      required: true,
      default: 'online',
    },

    // ราคาและสต็อก
    price: { type: Number, required: true },
    compareAtPrice: { type: Number },
    costPrice: { type: Number },
    stock: { type: Number, default: 0 },
    trackStock: { type: Boolean, default: true },
    allowBackorder: { type: Boolean, default: false },

    // ข้อมูลพื้นฐาน
    categoryId: { type: String },
    description: { type: String },
    shortDescription: { type: String },
    tags: [{ type: String }],

    // รูปภาพ
    images: [productImageSchema],

    // Variants
    hasVariants: { type: Boolean, default: false },
    variants: [productVariantSchema],
    variantAttributes: [{ type: String }],

    // Digital products
    digitalAssets: [digitalAssetSchema],

    // การจัดส่ง
    shipping: shippingSchema,

    // SEO
    seoTitle: { type: String },
    seoDescription: { type: String },

    // การแสดงผล
    featured: { type: Boolean, default: false },
    isActive: { type: Boolean, default: true },

    // Pre-order
    allowPreOrder: { type: Boolean, default: false },

    // ข้อมูลเพิ่มเติม
    customFields: { type: Schema.Types.Mixed },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
// productSchema.index({ siteId: 1, slug: 1 }, { unique: true });
// productSchema.index({ siteId: 1, name: 1 });
// productSchema.index({ siteId: 1, categoryId: 1 });
// productSchema.index({ siteId: 1, type: 1 });
// productSchema.index({ siteId: 1, featured: 1 });
// productSchema.index({ siteId: 1, isActive: 1 });
// productSchema.index({ siteId: 1, tags: 1 });

// Methods
productSchema.methods.getPrimaryImage = function() {
  return this.images.find((img: IProductImage) => img.isPrimary) || this.images[0];
};

productSchema.methods.getVariantByAttributes = function(attributes: Record<string, string>) {
  return this.variants.find((variant: IProductVariant) => {
    return Object.keys(attributes).every((key: string) => variant.attributes[key] === attributes[key]);
  });
};

productSchema.methods.getTotalStock = function() {
  if (!this.hasVariants) {
    return this.stock || 0;
  }
  return this.variants.reduce((total: number, variant: IProductVariant) => total + (variant.stock || 0), 0);
};

productSchema.methods.getPrice = function(variantId?: string) {
  if (variantId && this.hasVariants) {
    const variant = this.variants.find((v: IProductVariant) => v._id === variantId);
    return variant?.price || this.price;
  }
  return this.price;
};

// Auto-generate slug from name
productSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\u0E00-\u0E7F]+/g, '-') // รองรับภาษาไทย
      .replace(/^-+|-+$/g, '');
  }
  next();
});

export const Product = mongoose.model<IProduct>('Product', productSchema);
