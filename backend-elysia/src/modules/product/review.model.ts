import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export interface IReview extends Document {
  _id: string;
  siteId: string;
  productId: string;
  customerId: string;
  customerName: string;
  customerEmail: string;

  // Rating & Review
  rating: number; // 1-5 stars
  title: string;
  comment: string;
  images?: string[];

  // Verification & Moderation
  isVerified: boolean; // ลูกค้าที่ซื้อจริง
  isApproved: boolean; // admin approve
  isHelpful: number; // จำนวนคนที่กด helpful

  // Metadata
  helpfulVotes: string[]; // user IDs ที่กด helpful
  reportCount: number; // จำนวนคนที่ report

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
}

const reviewSchema = new Schema<IReview>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    productId: { type: String, required: true, index: true },
    customerId: { type: String, required: true, index: true },
    customerName: { type: String, required: true },
    customerEmail: { type: String, required: true },

    // Rating & Review
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
      index: true,
    },
    title: {
      type: String,
      required: true,
      maxLength: 200,
    },
    comment: {
      type: String,
      required: true,
      maxLength: 2000,
    },
    images: [{ type: String }],

    // Verification & Moderation
    isVerified: { type: Boolean, default: false, index: true },
    isApproved: { type: Boolean, default: true, index: true },
    isHelpful: { type: Number, default: 0, index: true },

    // Metadata
    helpfulVotes: [{ type: String }],
    reportCount: { type: Number, default: 0 },

    // Timestamps
    approvedAt: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
reviewSchema.index({ siteId: 1, productId: 1 });
reviewSchema.index({ siteId: 1, customerId: 1 });
reviewSchema.index({ siteId: 1, rating: 1 });
reviewSchema.index({ siteId: 1, isVerified: 1 });
reviewSchema.index({ siteId: 1, isApproved: 1 });
reviewSchema.index({ siteId: 1, createdAt: -1 });

// Methods
reviewSchema.methods.markAsHelpful = function(userId: string) {
  if (!this.helpfulVotes.includes(userId)) {
    this.helpfulVotes.push(userId);
    this.isHelpful = this.helpfulVotes.length;
  }
  return this.save();
};

reviewSchema.methods.unmarkAsHelpful = function(userId: string) {
  const index = this.helpfulVotes.indexOf(userId);
  if (index > -1) {
    this.helpfulVotes.splice(index, 1);
    this.isHelpful = this.helpfulVotes.length;
  }
  return this.save();
};

reviewSchema.methods.report = function() {
  this.reportCount += 1;
  return this.save();
};

reviewSchema.methods.approve = function() {
  this.isApproved = true;
  this.approvedAt = new Date();
  return this.save();
};

reviewSchema.methods.reject = function() {
  this.isApproved = false;
  return this.save();
};

// Static methods
reviewSchema.statics.getAverageRating = function(productId: string) {
  return this.aggregate([
    { $match: { productId, isApproved: true } },
    { $group: { _id: null, avgRating: { $avg: '$rating' }, count: { $sum: 1 } } },
  ]);
};

reviewSchema.statics.getRatingDistribution = function(productId: string) {
  return this.aggregate([
    { $match: { productId, isApproved: true } },
    { $group: { _id: '$rating', count: { $sum: 1 } } },
    { $sort: { _id: -1 } },
  ]);
};

export const Review = mongoose.model<IReview>('Review', reviewSchema);
