import { HttpError } from '@/core/utils/error';
import { Order } from '../order/order.model';
import { Product } from '../product/product.model';
import { Review } from './review.model';

// Review Service
export async function createReview(reviewData: {
  siteId: string;
  productId: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
}) {
  const { siteId, productId, customerId, customerName, customerEmail, rating, title, comment, images } = reviewData;
  const product = await Product.findById(productId);
  if (!product) throw new HttpError(404, 'ไม่พบสินค้า');
  const existingReview = await Review.findOne({ siteId, productId, customerId });
  if (existingReview) throw new HttpError(400, 'คุณเคยรีวิวสินค้านี้แล้ว');
  const hasPurchased = await Order.findOne({
    siteId,
    customerId,
    'items.productId': productId,
    status: { $in: ['delivered', 'shipped'] },
  });
  const review = await Review.create({
    siteId,
    productId,
    customerId,
    customerName,
    customerEmail,
    rating,
    title,
    comment,
    images,
    isVerified: !!hasPurchased,
  });
  return review;
}

export async function getReviewsByProduct(productId: string, filter: any = {}) {
  const query = { productId, isApproved: true, ...filter };
  const reviews = await Review.find(query).sort({ isVerified: -1, isHelpful: -1, createdAt: -1 });
  return reviews;
}

export async function getReviewsByCustomer(customerId: string, siteId: string) {
  const reviews = await Review.find({ customerId, siteId }).sort({ createdAt: -1 });
  return reviews;
}

export async function getReviewById(reviewId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  return review;
}

export async function updateReview(
  reviewId: string,
  updateData: {
    rating?: number;
    title?: string;
    comment?: string;
    images?: string[];
  },
) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  Object.assign(review, updateData);
  await review.save();
  return review;
}

export async function deleteReview(reviewId: string, customerId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  if (review.customerId !== customerId) throw new HttpError(403, 'ไม่มีสิทธิ์ลบรีวิวนี้');
  await Review.findByIdAndDelete(reviewId);
  return true;
}

export async function markReviewAsHelpful(reviewId: string, userId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  await (review as any).markAsHelpful(userId);
  return true;
}

export async function unmarkReviewAsHelpful(reviewId: string, userId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  await (review as any).unmarkAsHelpful(userId);
  return true;
}

export async function reportReview(reviewId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  await (review as any).report();
  return true;
}

// Admin functions
export async function approveReview(reviewId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  await (review as any).approve();
  return true;
}

export async function rejectReview(reviewId: string) {
  const review = await Review.findById(reviewId);
  if (!review) throw new HttpError(404, 'ไม่พบรีวิว');
  await (review as any).reject();
  return true;
}

// Analytics functions
export async function getProductRatingStats(productId: string) {
  const [avgRating, ratingDistribution] = await Promise.all([
    (Review as any).getAverageRating(productId),
    (Review as any).getRatingDistribution(productId),
  ]);
  const stats = {
    averageRating: avgRating[0]?.avgRating || 0,
    totalReviews: avgRating[0]?.count || 0,
    ratingDistribution: ratingDistribution.reduce((acc: any, item: any) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
  };
  return stats;
}

export async function getReviewStats(siteId: string) {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const [totalReviews, todayReviews, monthlyReviews, pendingReviews, verifiedReviews] = await Promise.all([
    Review.countDocuments({ siteId }),
    Review.countDocuments({ siteId, createdAt: { $gte: startOfDay } }),
    Review.countDocuments({ siteId, createdAt: { $gte: startOfMonth } }),
    Review.countDocuments({ siteId, isApproved: false }),
    Review.countDocuments({ siteId, isVerified: true }),
  ]);
  const avgRating = await Review.aggregate([
    { $match: { siteId, isApproved: true } },
    { $group: { _id: null, avgRating: { $avg: '$rating' } } },
  ]);
  return {
    totalReviews,
    todayReviews,
    monthlyReviews,
    pendingReviews,
    verifiedReviews,
    averageRating: avgRating[0]?.avgRating || 0,
  };
}
