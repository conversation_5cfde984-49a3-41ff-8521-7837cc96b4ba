import mongoose, { Document, Schema } from 'mongoose';

// Interface สำหรับ Addon Rental
export interface IAddonRental extends Document {
  siteId: string;
  addonId: string;
  addonName: string;
  price: number;
  status: 'active' | 'cancelled' | 'expired';
  isActive: boolean;
  rentedBy: string;
  createdAt: Date;
  activatedAt?: Date;
  deactivatedAt?: Date;
  cancelledAt?: Date;
  expiresAt: Date;
  updatedAt: Date;
}

// Schema สำหรับ Addon Rental
const AddonRentalSchema = new Schema<IAddonRental>({
  siteId: { type: String, required: true, index: true },
  addonId: { type: String, required: true, index: true },
  addonName: { type: String, required: true },
  price: { type: Number, required: true },
  status: {
    type: String,
    enum: ['active', 'cancelled', 'expired'],
    default: 'active',
    index: true,
  },
  isActive: { type: Boolean, default: false },
  rentedBy: { type: String, required: true },
  activatedAt: { type: Date },
  deactivatedAt: { type: Date },
  cancelledAt: { type: Date },
  expiresAt: { type: Date, required: true, index: true },
}, {
  timestamps: true,
  collection: 'addon_rentals',
});

// Compound index
AddonRentalSchema.index({ siteId: 1, addonId: 1 });

// Interface สำหรับ Addon Log
export interface IAddonLog extends Document {
  siteId: string;
  addonId: string;
  action: 'rent' | 'activate' | 'deactivate' | 'cancel';
  userId: string;
  details?: any;
  createdAt: Date;
}

// Schema สำหรับ Addon Log
const AddonLogSchema = new Schema<IAddonLog>({
  siteId: { type: String, required: true, index: true },
  addonId: { type: String, required: true, index: true },
  action: {
    type: String,
    enum: ['rent', 'activate', 'deactivate', 'cancel'],
    required: true,
    index: true,
  },
  userId: { type: String, required: true },
  details: { type: Schema.Types.Mixed },
}, {
  timestamps: { createdAt: true, updatedAt: false },
  collection: 'addon_logs',
});

// Compound index
AddonLogSchema.index({ siteId: 1, addonId: 1 });
AddonLogSchema.index({ createdAt: -1 });

// Export models
export const AddonRental = mongoose.model<IAddonRental>('AddonRental', AddonRentalSchema);
export const AddonLog = mongoose.model<IAddonLog>('AddonLog', AddonLogSchema);

// Available addons configuration
export const AVAILABLE_ADDONS = [
  {
    id: 'news',
    name: 'ระบบข่าวสาร',
    description: 'จัดการข่าวสารและประชาสัมพันธ์',
    price: 299,
    features: ['สร้างข่าวสาร', 'จัดหมวดหมู่', 'SEO ข่าวสาร', 'แสดงผลหน้าเว็บ'],
    category: 'content',
  },
  {
    id: 'blog',
    name: 'ระบบบล็อก',
    description: 'เขียนบทความและแชร์เนื้อหา',
    price: 399,
    features: ['เขียนบทความ', 'ระบบแท็ก', 'ความคิดเห็น', 'แชร์โซเชียล'],
    category: 'content',
  },
  {
    id: 'novel',
    name: 'ระบบนิยาย',
    description: 'เผยแพร่นิยายและเรื่องสั้น',
    price: 599,
    features: ['เขียนนิยาย', 'แบ่งตอน', 'ระบบจองอ่าน', 'ระบบเรตติ้ง'],
    category: 'content',
  },
];
