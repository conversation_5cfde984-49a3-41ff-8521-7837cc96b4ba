import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import {
  addTrackingEventSchema,
  calculateShippingCostSchema,
  createShippingSchema,
  shippingCostResponseSchema,
  shippingFilterSchema,
  shippingListResponseSchema,
  shippingResponseSchema,
  shippingStatsResponseSchema,
  updateShippingInfoSchema,
  updateShippingStatusSchema,
} from './shipping.schema';
import {
  addTrackingEvent,
  calculateShippingCost,
  createShipping,
  getShippingByDateRange,
  getShippingByOrder,
  getShippingByStatus,
  getShippingByTracking,
  getShippingStats,
  updateShippingInfo,
  updateShippingStatus,
} from './shipping.service';
export const shippingRoutes = new Elysia({ prefix: '/shipping' })
  .use(userAuthPlugin)
  // สร้าง shipping record ใหม่
  .post(
    '/create',
    async ({ body, _user }: any) => {
      const { siteId, ...shippingData } = body;

      const result = await createShipping({
        siteId,
        ...shippingData,
      });

      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: createShippingSchema,
      response: shippingResponseSchema,
    },
  )
  // ดึง shipping ตามออเดอร์
  .get(
    '/order/:orderId',
    async ({ params, query }: any) => {
      const { orderId } = params;
      const { siteId } = query;

      const shipping = await getShippingByOrder(orderId, siteId);

      return {
        success: true,
        message: 'ดึงข้อมูล shipping สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: shipping,
      };
    },
    {
      response: shippingResponseSchema,
    },
  )
  // ดึง shipping ตาม tracking number
  .get(
    '/tracking/:trackingNumber',
    async ({ params, query }: any) => {
      const { trackingNumber } = params;
      const { siteId } = query;

      const shipping = await getShippingByTracking(trackingNumber, siteId);

      return {
        success: true,
        message: 'ดึงข้อมูล shipping สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: shipping,
      };
    },
    {
      response: shippingResponseSchema,
    },
  )
  // อัปเดตสถานะ shipping
  .put(
    '/:shippingId/status',
    async ({ params, body }: any) => {
      const { shippingId } = params;
      const { status } = body;

      const result = await updateShippingStatus(shippingId, status);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updateShippingStatusSchema,
      response: shippingResponseSchema,
    },
  )
  // เพิ่ม tracking event
  .post(
    '/:shippingId/tracking',
    async ({ params, body }: any) => {
      const { shippingId } = params;
      const { status, location, description, trackingCode } = body;

      const trackingEvent = {
        status,
        location,
        description,
        timestamp: new Date(),
        trackingCode,
      };

      const result = await addTrackingEvent(shippingId, trackingEvent);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: addTrackingEventSchema,
      response: shippingResponseSchema,
    },
  )
  // อัปเดตข้อมูล shipping
  .put(
    '/:shippingId',
    async ({ params, body }: any) => {
      const { shippingId } = params;

      const result = await updateShippingInfo(shippingId, body);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updateShippingInfoSchema,
      response: shippingResponseSchema,
    },
  )
  // ดึง shipping ตามสถานะ
  .get(
    '/status/:status',
    async ({ params, query }: any) => {
      const { status } = params;
      const { siteId } = query;

      const shipping = await getShippingByStatus(siteId, status);

      return {
        success: true,
        message: 'ดึง shipping ตามสถานะสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: shipping,
      };
    },
    {
      response: shippingResponseSchema,
    },
  )
  // คำนวณค่าจัดส่ง
  .post(
    '/calculate-cost',
    async ({ body }: any) => {
      const { weight, shippingMethod, insuranceCost, distance } = body;

      const costData = await calculateShippingCost({
        weight,
        shippingMethod,
        insuranceCost,
        distance,
      });

      return {
        success: true,
        message: 'คำนวณค่าจัดส่งสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: costData,
      };
    },
    {
      body: calculateShippingCostSchema,
      response: shippingCostResponseSchema,
    },
  )
  // ดึง shipping ตามช่วงวันที่
  .get(
    '/date-range',
    async ({ query }: any) => {
      const { siteId, startDate, endDate, page = 1, limit = 20 } = query;

      if (!startDate || !endDate) {
        throw new HttpError(400, 'กรุณาระบุ startDate และ endDate');
      }

      const shipments = await getShippingByDateRange(siteId, new Date(startDate), new Date(endDate));
      const total = shipments.length;
      const skip = (page - 1) * limit;
      const paginatedShipments = shipments.slice(skip, skip + limit);

      return {
        success: true,
        message: 'ดึง shipping ตามช่วงวันที่สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: paginatedShipments,
        total,
        page,
        limit,
      };
    },
    {
      query: shippingFilterSchema,
      response: shippingListResponseSchema,
    },
  )
  // สถิติ shipping
  .get(
    '/stats/:siteId',
    async ({ params }: any) => {
      const { siteId } = params;
      const stats = await getShippingStats(siteId);

      return {
        success: true,
        message: 'ดึงสถิติ shipping สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: stats,
      };
    },
    {
      response: shippingStatsResponseSchema,
    },
  );
