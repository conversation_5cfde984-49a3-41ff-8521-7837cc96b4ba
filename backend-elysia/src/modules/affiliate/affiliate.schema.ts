import { t } from 'elysia';

// Affiliate Schemas
export const AffiliateCreateSchema = t.Object({
  customerId: t.String({
    error: 'customerId ต้องเป็นข้อความที่ถูกต้อง',
  }),
  commission: t.Optional(
    t.Object({
      rate: t.Number({ minimum: 0, maximum: 100, error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลขระหว่าง 0 ถึง 100' }),
      minAmount: t.Optional(t.Number({ error: 'จำนวนขั้นต่ำต้องเป็นตัวเลข' })),
      maxAmount: t.Optional(t.Number({ error: 'จำนวนสูงสุดต้องเป็นตัวเลข' })),
    }),
  ),
  settings: t.Optional(
    t.Object({
      autoApprove: t.Optional(t.<PERSON>({ error: 'autoApprove ต้องเป็นค่า Boolean' })),
      requireApproval: t.<PERSON>tional(t.<PERSON>({ error: 'requireApproval ต้องเป็นค่า Boolean' })),
      minPayout: t.Optional(t.Number({ error: 'minPayout ต้องเป็นตัวเลข' })),
      payoutSchedule: t.Optional(
        t.Union([
          t.Literal('weekly', { error: 'ระยะเวลาจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('monthly', { error: 'ระยะเวลาจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('quarterly', { error: 'ระยะเวลาจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
        ]),
      ),
      paymentMethod: t.Optional(
        t.Union([
          t.Literal('bank', { error: 'วิธีการจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('paypal', { error: 'วิธีการจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('crypto', { error: 'วิธีการจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
        ]),
      ),
    }),
  ),
});

export const AffiliateUpdateSchema = t.Object({
  status: t.Optional(
    t.Union([
      t.Literal('active', { error: 'สถานะต้องเป็น active, inactive หรือ suspended' }),
      t.Literal('inactive', { error: 'สถานะต้องเป็น active, inactive หรือ suspended' }),
      t.Literal('suspended', { error: 'สถานะต้องเป็น active, inactive หรือ suspended' }),
    ]),
  ),
  commission: t.Optional(
    t.Object({
      rate: t.Number({ minimum: 0, maximum: 100, error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลขระหว่าง 0 ถึง 100' }),
      minAmount: t.Optional(t.Number({ error: 'จำนวนขั้นต่ำต้องเป็นตัวเลข' })),
      maxAmount: t.Optional(t.Number({ error: 'จำนวนสูงสุดต้องเป็นตัวเลข' })),
    }),
  ),
  settings: t.Optional(
    t.Object({
      autoApprove: t.Optional(t.Boolean({ error: 'autoApprove ต้องเป็นค่า Boolean' })),
      requireApproval: t.Optional(t.Boolean({ error: 'requireApproval ต้องเป็นค่า Boolean' })),
      minPayout: t.Optional(t.Number({ error: 'minPayout ต้องเป็นตัวเลข' })),
      payoutSchedule: t.Optional(
        t.Union([
          t.Literal('weekly', { error: 'ระยะเวลาจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('monthly', { error: 'ระยะเวลาจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('quarterly', { error: 'ระยะเวลาจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
        ]),
      ),
      paymentMethod: t.Optional(
        t.Union([
          t.Literal('bank', { error: 'วิธีการจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('paypal', { error: 'วิธีการจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
          t.Literal('crypto', { error: 'วิธีการจ่ายเงินต้องเป็นค่าที่กำหนดไว้' }),
        ]),
      ),
    }),
  ),
});

export const AffiliateQuerySchema = t.Object({
  page: t.Optional(t.String({ error: 'page ต้องเป็นข้อความที่ถูกต้อง' })),
  limit: t.Optional(t.String({ error: 'limit ต้องเป็นข้อความที่ถูกต้อง' })),
  status: t.Optional(
    t.Union([
      t.Literal('active', { error: 'สถานะต้องเป็น active, inactive หรือ suspended' }),
      t.Literal('inactive', { error: 'สถานะต้องเป็น active, inactive หรือ suspended' }),
      t.Literal('suspended', { error: 'สถานะต้องเป็น active, inactive หรือ suspended' }),
    ]),
  ),
});

export const AffiliateResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็นค่า Boolean' }),
  data: t.Object({
    _id: t.String({ error: '_id ต้องเป็นข้อความที่ถูกต้อง' }),
    siteId: t.String({ error: 'siteId ต้องเป็นข้อความที่ถูกต้อง' }),
    customerId: t.String({ error: 'customerId ต้องเป็นข้อความที่ถูกต้อง' }),
    affiliateCode: t.String({ error: 'affiliateCode ต้องเป็นข้อความที่ถูกต้อง' }),
    status: t.String({ error: 'status ต้องเป็นข้อความที่ถูกต้อง' }),
    commission: t.Object({
      rate: t.Number({ error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลข' }),
      minAmount: t.Number({ error: 'จำนวนขั้นต่ำต้องเป็นตัวเลข' }),
      maxAmount: t.Number({ error: 'จำนวนสูงสุดต้องเป็นตัวเลข' }),
    }),
    performance: t.Object({
      totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
      totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
      totalCommission: t.Number({ error: 'totalCommission ต้องเป็นตัวเลข' }),
      totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
      conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' }),
      clicks: t.Number({ error: 'clicks ต้องเป็นตัวเลข' }),
      impressions: t.Number({ error: 'impressions ต้องเป็นตัวเลข' }),
    }),
    products: t.Array(
      t.Object({
        productId: t.String({ error: 'productId ต้องเป็นข้อความที่ถูกต้อง' }),
        commissionRate: t.Number({ error: 'commissionRate ต้องเป็นตัวเลข' }),
        isActive: t.Boolean({ error: 'isActive ต้องเป็นค่า Boolean' }),
      }),
    ),
    tracking: t.Object({
      lastActivity: t.String({ error: 'lastActivity ต้องเป็นข้อความที่ถูกต้อง' }),
      totalClicks: t.Number({ error: 'totalClicks ต้องเป็นตัวเลข' }),
      uniqueClicks: t.Number({ error: 'uniqueClicks ต้องเป็นตัวเลข' }),
      totalImpressions: t.Number({ error: 'totalImpressions ต้องเป็นตัวเลข' }),
    }),
    settings: t.Object({
      autoApprove: t.Boolean({ error: 'autoApprove ต้องเป็นค่า Boolean' }),
      requireApproval: t.Boolean({ error: 'requireApproval ต้องเป็นค่า Boolean' }),
      minPayout: t.Number({ error: 'minPayout ต้องเป็นตัวเลข' }),
      payoutSchedule: t.String({ error: 'payoutSchedule ต้องเป็นข้อความที่กำหนดไว้' }),
      paymentMethod: t.String({ error: 'paymentMethod ต้องเป็นข้อความที่กำหนดไว้' }),
    }),
    createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความที่ถูกต้อง' }),
    updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความที่ถูกต้อง' }),
  }),
});

export const AffiliateListResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็นค่า Boolean' }),
  data: t.Object({
    affiliates: t.Array(
      t.Object({
        _id: t.String({ error: '_id ต้องเป็นข้อความที่ถูกต้อง' }),
        siteId: t.String({ error: 'siteId ต้องเป็นข้อความที่ถูกต้อง' }),
        customerId: t.String({ error: 'customerId ต้องเป็นข้อความที่ถูกต้อง' }),
        affiliateCode: t.String({ error: 'affiliateCode ต้องเป็นข้อความที่ถูกต้อง' }),
        status: t.String({ error: 'status ต้องเป็นข้อความที่ถูกต้อง' }),
        commission: t.Object({
          rate: t.Number({ error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลข' }),
          minAmount: t.Number({ error: 'จำนวนขั้นต่ำต้องเป็นตัวเลข' }),
          maxAmount: t.Number({ error: 'จำนวนสูงสุดต้องเป็นตัวเลข' }),
        }),
        performance: t.Object({
          totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
          totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
          totalCommission: t.Number({ error: 'totalCommission ต้องเป็นตัวเลข' }),
          totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
          conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' }),
          clicks: t.Number({ error: 'clicks ต้องเป็นตัวเลข' }),
          impressions: t.Number({ error: 'impressions ต้องเป็นตัวเลข' }),
        }),
        products: t.Array(
          t.Object({
            productId: t.String({ error: 'productId ต้องเป็นข้อความที่ถูกต้อง' }),
            commissionRate: t.Number({ error: 'commissionRate ต้องเป็นตัวเลข' }),
            isActive: t.Boolean({ error: 'isActive ต้องเป็นค่า Boolean' }),
          }),
        ),
        tracking: t.Object({
          lastActivity: t.String({ error: 'lastActivity ต้องเป็นข้อความที่ถูกต้อง' }),
          totalClicks: t.Number({ error: 'totalClicks ต้องเป็นตัวเลข' }),
          uniqueClicks: t.Number({ error: 'uniqueClicks ต้องเป็นตัวเลข' }),
          totalImpressions: t.Number({ error: 'totalImpressions ต้องเป็นตัวเลข' }),
        }),
        settings: t.Object({
          autoApprove: t.Boolean({ error: 'autoApprove ต้องเป็นค่า Boolean' }),
          requireApproval: t.Boolean({ error: 'requireApproval ต้องเป็นค่า Boolean' }),
          minPayout: t.Number({ error: 'minPayout ต้องเป็นตัวเลข' }),
          payoutSchedule: t.String({ error: 'payoutSchedule ต้องเป็นข้อความที่กำหนดไว้' }),
          paymentMethod: t.String({ error: 'paymentMethod ต้องเป็นข้อความที่กำหนดไว้' }),
        }),
        createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความที่ถูกต้อง' }),
        updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความที่ถูกต้อง' }),
      }),
    ),
    pagination: t.Object({
      page: t.Number({ error: 'page ต้องเป็นตัวเลข' }),
      limit: t.Number({ error: 'limit ต้องเป็นตัวเลข' }),
      total: t.Number({ error: 'total ต้องเป็นตัวเลข' }),
      pages: t.Number({ error: 'pages ต้องเป็นตัวเลข' }),
    }),
  }),
});

export const AffiliateClickSchema = t.Object({
  affiliateCode: t.String({ error: 'affiliateCode ต้องเป็นข้อความที่ถูกต้อง' }),
  productId: t.String({ error: 'productId ต้องเป็นข้อความที่ถูกต้อง' }),
  trackingData: t.Object({
    customerId: t.Optional(t.String({ error: 'customerId ต้องเป็นข้อความที่ถูกต้อง' })),
    ipAddress: t.String({ error: 'ipAddress ต้องเป็นข้อความที่ถูกต้อง' }),
    userAgent: t.Optional(t.String({ error: 'userAgent ต้องเป็นข้อความที่ถูกต้อง' })),
    referrer: t.Optional(t.String({ error: 'referrer ต้องเป็นข้อความที่ถูกต้อง' })),
  }),
});

export const AffiliateOrderSchema = t.Object({
  orderId: t.String({ error: 'orderId ต้องเป็นข้อความที่ถูกต้อง' }),
  affiliateCode: t.String({ error: 'affiliateCode ต้องเป็นข้อความที่ถูกต้อง' }),
  trackingData: t.Object({
    clickTime: t.String({ error: 'clickTime ต้องเป็นข้อความที่ถูกต้อง' }),
    orderTime: t.String({ error: 'orderTime ต้องเป็นข้อความที่ถูกต้อง' }),
    ipAddress: t.Optional(t.String({ error: 'ipAddress ต้องเป็นข้อความที่ถูกต้อง' })),
    userAgent: t.Optional(t.String({ error: 'userAgent ต้องเป็นข้อความที่ถูกต้อง' })),
    referrer: t.Optional(t.String({ error: 'referrer ต้องเป็นข้อความที่ถูกต้อง' })),
  }),
});

export const AffiliateProductSchema = t.Object({
  productId: t.String({ error: 'productId ต้องเป็นข้อความที่ถูกต้อง' }),
  commissionRate: t.Number({ minimum: 0, maximum: 100, error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลขระหว่าง 0 ถึง 100' }),
});

export const AffiliateStatsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็นค่า Boolean' }),
  data: t.Object({
    affiliate: t.Object({
      _id: t.String({ error: '_id ต้องเป็นข้อความที่ถูกต้อง' }),
      siteId: t.String({ error: 'siteId ต้องเป็นข้อความที่ถูกต้อง' }),
      customerId: t.String({ error: 'customerId ต้องเป็นข้อความที่ถูกต้อง' }),
      affiliateCode: t.String({ error: 'affiliateCode ต้องเป็นข้อความที่ถูกต้อง' }),
      status: t.String({ error: 'status ต้องเป็นข้อความที่ถูกต้อง' }),
      commission: t.Object({
        rate: t.Number({ error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลข' }),
        minAmount: t.Number({ error: 'จำนวนขั้นต่ำต้องเป็นตัวเลข' }),
        maxAmount: t.Number({ error: 'จำนวนสูงสุดต้องเป็นตัวเลข' }),
      }),
      performance: t.Object({
        totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
        totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
        totalCommission: t.Number({ error: 'totalCommission ต้องเป็นตัวเลข' }),
        totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
        conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' }),
        clicks: t.Number({ error: 'clicks ต้องเป็นตัวเลข' }),
        impressions: t.Number({ error: 'impressions ต้องเป็นตัวเลข' }),
      }),
      products: t.Array(
        t.Object({
          productId: t.String({ error: 'productId ต้องเป็นข้อความที่ถูกต้อง' }),
          commissionRate: t.Number({ error: 'commissionRate ต้องเป็นตัวเลข' }),
          isActive: t.Boolean({ error: 'isActive ต้องเป็นค่า Boolean' }),
        }),
      ),
      tracking: t.Object({
        lastActivity: t.String({ error: 'lastActivity ต้องเป็นข้อความที่ถูกต้อง' }),
        totalClicks: t.Number({ error: 'totalClicks ต้องเป็นตัวเลข' }),
        uniqueClicks: t.Number({ error: 'uniqueClicks ต้องเป็นตัวเลข' }),
        totalImpressions: t.Number({ error: 'totalImpressions ต้องเป็นตัวเลข' }),
      }),
      settings: t.Object({
        autoApprove: t.Boolean({ error: 'autoApprove ต้องเป็นค่า Boolean' }),
        requireApproval: t.Boolean({ error: 'requireApproval ต้องเป็นค่า Boolean' }),
        minPayout: t.Number({ error: 'minPayout ต้องเป็นตัวเลข' }),
        payoutSchedule: t.String({ error: 'payoutSchedule ต้องเป็นข้อความที่กำหนดไว้' }),
        paymentMethod: t.String({ error: 'paymentMethod ต้องเป็นข้อความที่กำหนดไว้' }),
      }),
      createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความที่ถูกต้อง' }),
      updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความที่ถูกต้อง' }),
    }),
    clickStats: t.Object({
      totalClicks: t.Number({ error: 'totalClicks ต้องเป็นตัวเลข' }),
      uniqueClicks: t.Number({ error: 'uniqueClicks ต้องเป็นตัวเลข' }),
      conversions: t.Number({ error: 'conversions ต้องเป็นตัวเลข' }),
    }),
    pendingCommissions: t.Number({ error: 'pendingCommissions ต้องเป็นตัวเลข' }),
  }),
});

export const TopPerformersResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็นค่า Boolean' }),
  data: t.Array(
    t.Object({
      _id: t.String({ error: '_id ต้องเป็นข้อความที่ถูกต้อง' }),
      siteId: t.String({ error: 'siteId ต้องเป็นข้อความที่ถูกต้อง' }),
      customerId: t.String({ error: 'customerId ต้องเป็นข้อความที่ถูกต้อง' }),
      affiliateCode: t.String({ error: 'affiliateCode ต้องเป็นข้อความที่ถูกต้อง' }),
      status: t.String({ error: 'status ต้องเป็นข้อความที่ถูกต้อง' }),
      commission: t.Object({
        rate: t.Number({ error: 'อัตราคอมมิชชั่นต้องเป็นตัวเลข' }),
        minAmount: t.Number({ error: 'จำนวนขั้นต่ำต้องเป็นตัวเลข' }),
        maxAmount: t.Number({ error: 'จำนวนสูงสุดต้องเป็นตัวเลข' }),
      }),
      performance: t.Object({
        totalSales: t.Number({ error: 'totalSales ต้องเป็นตัวเลข' }),
        totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
        totalCommission: t.Number({ error: 'totalCommission ต้องเป็นตัวเลข' }),
        totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
        conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' }),
        clicks: t.Number({ error: 'clicks ต้องเป็นตัวเลข' }),
        impressions: t.Number({ error: 'impressions ต้องเป็นตัวเลข' }),
      }),
      products: t.Array(
        t.Object({
          productId: t.String({ error: 'productId ต้องเป็นข้อความที่ถูกต้อง' }),
          commissionRate: t.Number({ error: 'commissionRate ต้องเป็นตัวเลข' }),
          isActive: t.Boolean({ error: 'isActive ต้องเป็นค่า Boolean' }),
        }),
      ),
      tracking: t.Object({
        lastActivity: t.String({ error: 'lastActivity ต้องเป็นข้อความที่ถูกต้อง' }),
        totalClicks: t.Number({ error: 'totalClicks ต้องเป็นตัวเลข' }),
        uniqueClicks: t.Number({ error: 'uniqueClicks ต้องเป็นตัวเลข' }),
        totalImpressions: t.Number({ error: 'totalImpressions ต้องเป็นตัวเลข' }),
      }),
      settings: t.Object({
        autoApprove: t.Boolean({ error: 'autoApprove ต้องเป็นค่า Boolean' }),
        requireApproval: t.Boolean({ error: 'requireApproval ต้องเป็นค่า Boolean' }),
        minPayout: t.Number({ error: 'minPayout ต้องเป็นตัวเลข' }),
        payoutSchedule: t.String({ error: 'payoutSchedule ต้องเป็นข้อความที่กำหนดไว้' }),
        paymentMethod: t.String({ error: 'paymentMethod ต้องเป็นข้อความที่กำหนดไว้' }),
      }),
      createdAt: t.String({ error: 'createdAt ต้องเป็นข้อความที่ถูกต้อง' }),
      updatedAt: t.String({ error: 'updatedAt ต้องเป็นข้อความที่ถูกต้อง' }),
    }),
  ),
});
