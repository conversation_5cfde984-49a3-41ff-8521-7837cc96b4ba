import { generateId } from '@/core/utils';
import mongoose, { type Document, Schema } from 'mongoose';

export interface ILoyaltyAccount extends Document {
  _id: string;
  siteId: string;
  userId: string;
  userType: 'user' | 'customer';
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  points: number;
  lifetimePoints: number;
  tierPoints: number; // Points for current tier
  nextTierPoints: number; // Points needed for next tier
  settings: {
    autoUpgrade: boolean;
    allowPointExpiry: boolean;
    pointExpiryDays: number;
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: Date;
    customerSince: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IPointsTransaction extends Document {
  _id: string;
  siteId: string;
  userId: string;
  type: 'earn' | 'spend' | 'expire' | 'adjust' | 'bonus';
  amount: number;
  balance: number; // Balance after transaction
  source: 'purchase' | 'referral' | 'birthday' | 'review' | 'social' | 'manual' | 'expiry';
  orderId?: string;
  productId?: string;
  description: string;
  expiresAt?: Date;
  isExpired: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ILoyaltyReward extends Document {
  _id: string;
  siteId: string;
  name: string;
  description: string;
  type: 'discount' | 'free_shipping' | 'free_product' | 'cashback' | 'voucher';
  pointsRequired: number;
  value: number; // Discount amount, shipping cost, etc.
  maxUses: number;
  currentUses: number;
  minOrderAmount?: number;
  applicableProducts?: string[];
  applicableCategories?: string[];
  startDate: Date;
  endDate?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ILoyaltyTier extends Document {
  _id: string;
  siteId: string;
  name: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  pointsRequired: number;
  benefits: {
    pointMultiplier: number;
    discountPercentage: number;
    freeShipping: boolean;
    prioritySupport: boolean;
    exclusiveAccess: boolean;
    birthdayBonus: number;
    referralBonus: number;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const LoyaltyAccountSchema = new Schema<ILoyaltyAccount>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    userType: {
      type: String,
      required: true,
      enum: ['user', 'customer'],
    },
    tier: {
      type: String,
      required: true,
      enum: ['bronze', 'silver', 'gold', 'platinum', 'diamond'],
      default: 'bronze',
    },
    points: { type: Number, default: 0 },
    lifetimePoints: { type: Number, default: 0 },
    tierPoints: { type: Number, default: 0 },
    nextTierPoints: { type: Number, default: 0 },
    settings: {
      autoUpgrade: { type: Boolean, default: true },
      allowPointExpiry: { type: Boolean, default: false },
      pointExpiryDays: { type: Number, default: 365 },
    },
    stats: {
      totalOrders: { type: Number, default: 0 },
      totalSpent: { type: Number, default: 0 },
      averageOrderValue: { type: Number, default: 0 },
      lastOrderDate: { type: Date },
      customerSince: { type: Date, default: Date.now },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const PointsTransactionSchema = new Schema<IPointsTransaction>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    type: {
      type: String,
      required: true,
      enum: ['earn', 'spend', 'expire', 'adjust', 'bonus'],
    },
    amount: { type: Number, required: true },
    balance: { type: Number, required: true },
    source: {
      type: String,
      required: true,
      enum: ['purchase', 'referral', 'birthday', 'review', 'social', 'manual', 'expiry'],
    },
    orderId: { type: String, index: true },
    productId: { type: String, index: true },
    description: { type: String, required: true },
    expiresAt: { type: Date },
    isExpired: { type: Boolean, default: false },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const LoyaltyRewardSchema = new Schema<ILoyaltyReward>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ['discount', 'free_shipping', 'free_product', 'cashback', 'voucher'],
    },
    pointsRequired: { type: Number, required: true },
    value: { type: Number, required: true },
    maxUses: { type: Number, default: -1 }, // -1 = unlimited
    currentUses: { type: Number, default: 0 },
    minOrderAmount: { type: Number },
    applicableProducts: [{ type: String }],
    applicableCategories: [{ type: String }],
    startDate: { type: Date, required: true },
    endDate: { type: Date },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const LoyaltyTierSchema = new Schema<ILoyaltyTier>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    tier: {
      type: String,
      required: true,
      enum: ['bronze', 'silver', 'gold', 'platinum', 'diamond'],
    },
    pointsRequired: { type: Number, required: true },
    benefits: {
      pointMultiplier: { type: Number, default: 1 },
      discountPercentage: { type: Number, default: 0 },
      freeShipping: { type: Boolean, default: false },
      prioritySupport: { type: Boolean, default: false },
      exclusiveAccess: { type: Boolean, default: false },
      birthdayBonus: { type: Number, default: 0 },
      referralBonus: { type: Number, default: 0 },
    },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
LoyaltyAccountSchema.index({ siteId: 1, userId: 1 });
LoyaltyAccountSchema.index({ siteId: 1, tier: 1 });
LoyaltyAccountSchema.index({ points: -1 });

PointsTransactionSchema.index({ siteId: 1, userId: 1 });
PointsTransactionSchema.index({ siteId: 1, type: 1 });
PointsTransactionSchema.index({ createdAt: -1 });
PointsTransactionSchema.index({ expiresAt: 1 });

LoyaltyRewardSchema.index({ siteId: 1, isActive: 1 });
LoyaltyRewardSchema.index({ siteId: 1, pointsRequired: 1 });
LoyaltyRewardSchema.index({ startDate: 1, endDate: 1 });

LoyaltyTierSchema.index({ siteId: 1, tier: 1 });
LoyaltyTierSchema.index({ siteId: 1, isActive: 1 });

// Static methods
LoyaltyAccountSchema.statics.findByUser = async function(siteId: string, userId: string) {
  return this.findOne({ siteId, userId });
};

LoyaltyAccountSchema.statics.getTopCustomers = async function(siteId: string, limit: number = 10) {
  return this.find({ siteId }).sort({ points: -1 }).limit(limit);
};

PointsTransactionSchema.statics.findByUser = async function(siteId: string, userId: string) {
  return this.find({ siteId, userId }).sort({ createdAt: -1 });
};

PointsTransactionSchema.statics.getExpiredPoints = async function(siteId: string) {
  return this.find({
    siteId,
    type: 'earn',
    expiresAt: { $lt: new Date() },
    isExpired: false,
  });
};

LoyaltyRewardSchema.statics.findAvailable = async function(siteId: string, points: number) {
  const now = new Date();
  return this.find({
    siteId,
    isActive: true,
    pointsRequired: { $lte: points },
    startDate: { $lte: now },
    $or: [{ endDate: { $exists: false } }, { endDate: { $gt: now } }],
  }).sort({ pointsRequired: 1 });
};

LoyaltyTierSchema.statics.findByTier = async function(siteId: string, tier: string) {
  return this.findOne({ siteId, tier, isActive: true });
};

export const LoyaltyAccount = mongoose.model<ILoyaltyAccount>('LoyaltyAccount', LoyaltyAccountSchema);
export const PointsTransaction = mongoose.model<IPointsTransaction>('PointsTransaction', PointsTransactionSchema);
export const LoyaltyReward = mongoose.model<ILoyaltyReward>('LoyaltyReward', LoyaltyRewardSchema);
export const LoyaltyTier = mongoose.model<ILoyaltyTier>('LoyaltyTier', LoyaltyTierSchema);
