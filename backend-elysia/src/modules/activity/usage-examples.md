# 📚 Activity Module Usage Examples

## 🎯 ตัวอย่างการใช้งาน Activity Module ใน Modules อื่นๆ

### 1. **User Module Integration**

```typescript
// src/modules/user/user.service.ts
import {
  logLoginActivity,
  logSignupActivity,
  logUserActivity,
} from '../activity';

export class UserService {
  async signup(userData: any, token: string) {
    // ... existing signup logic

    // สร้างกิจกรรมการสมัครสมาชิก
    await logSignupActivity(
      user._id,
      user.firstName + ' ' + user.lastName,
      user.email,
      user.role,
      token,
      {
        registrationMethod: 'email',
        source: 'website',
      },
    );

    return user;
  }

  async login(email: string, password: string, token: string) {
    // ... existing login logic

    // สร้างกิจกรรมการเข้าสู่ระบบ
    await logLoginActivity(
      user._id,
      user.firstName + ' ' + user.lastName,
      user.email,
      user.role,
      token,
      {
        loginMethod: 'email',
        ipAddress: request.ip,
      },
    );

    return user;
  }

  async updateProfile(userId: string, updateData: any, token: string) {
    const oldValues = {/* current user data */};
    const newValues = {/* updated user data */};

    // ... existing update logic

    // สร้างกิจกรรมการอัปเดตโปรไฟล์
    await logProfileUpdateActivity(
      userId,
      user.firstName + ' ' + user.lastName,
      user.email,
      user.role,
      token,
      oldValues,
      newValues,
    );

    return user;
  }
}
```

### 2. **Product Module Integration**

```typescript
// src/modules/product/product.service.ts
import {
  logProductActivity,
  logProductCreateActivity,
  logProductUpdateActivity,
} from '../activity';

export class ProductService {
  async createProduct(
    productData: any,
    userId: string,
    userName: string,
    siteId: string,
    token: string,
  ) {
    // ... existing create logic

    // สร้างกิจกรรมการสร้างสินค้า
    await logProductCreateActivity(
      product._id,
      product.name,
      siteId,
      userId,
      userName,
      token,
      {
        category: product.category,
        price: product.price,
        stock: product.stock,
      },
    );

    return product;
  }

  async updateProduct(
    productId: string,
    updateData: any,
    userId: string,
    userName: string,
    siteId: string,
    token: string,
  ) {
    const oldValues = {/* current product data */};
    const newValues = {/* updated product data */};

    // ... existing update logic

    // สร้างกิจกรรมการอัปเดตสินค้า
    await logProductUpdateActivity(
      productId,
      product.name,
      siteId,
      userId,
      userName,
      token,
      oldValues,
      newValues,
    );

    return product;
  }
}
```

### 3. **Order Module Integration**

```typescript
// src/modules/order/order.service.ts
import {
  logOrderActivity,
  logOrderCreateActivity,
  logOrderStatusChangeActivity,
} from '../activity';

export class OrderService {
  async createOrder(
    orderData: any,
    userId: string,
    userName: string,
    token: string,
  ) {
    // ... existing create logic

    // สร้างกิจกรรมการสร้างออเดอร์
    await logOrderCreateActivity(
      order._id,
      order.orderNumber,
      order.siteId,
      order.customerId,
      order.customerName,
      userId,
      userName,
      token,
      {
        totalAmount: order.totalAmount,
        items: order.items.length,
        paymentMethod: order.paymentMethod,
      },
    );

    return order;
  }

  async updateOrderStatus(
    orderId: string,
    newStatus: string,
    userId: string,
    userName: string,
    token: string,
  ) {
    const order = await this.getOrderById(orderId);
    const oldStatus = order.status;

    // ... existing update logic

    // สร้างกิจกรรมการเปลี่ยนสถานะออเดอร์
    await logOrderStatusChangeActivity(
      orderId,
      order.orderNumber,
      order.siteId,
      order.customerId,
      order.customerName,
      userId,
      userName,
      oldStatus,
      newStatus,
      token,
    );

    return order;
  }
}
```

### 4. **Site Module Integration**

```typescript
// src/modules/site/site.service.ts
import {
  logSiteActivity,
  logSiteCreateActivity,
  logSiteUpdateActivity,
} from '../activity';

export class SiteService {
  async createSite(
    siteData: any,
    userId: string,
    userName: string,
    token: string,
  ) {
    // ... existing create logic

    // สร้างกิจกรรมการสร้างไซต์
    await logSiteCreateActivity(
      site._id,
      site.name,
      userId,
      userName,
      token,
      {
        domain: site.domain,
        template: site.template,
        plan: site.plan,
      },
    );

    return site;
  }

  async updateSite(
    siteId: string,
    updateData: any,
    userId: string,
    userName: string,
    token: string,
  ) {
    const oldValues = {/* current site data */};
    const newValues = {/* updated site data */};

    // ... existing update logic

    // สร้างกิจกรรมการอัปเดตไซต์
    await logSiteUpdateActivity(
      siteId,
      site.name,
      userId,
      userName,
      token,
      oldValues,
      newValues,
    );

    return site;
  }
}
```

### 5. **Customer Module Integration**

```typescript
// src/modules/customer/customer.service.ts
import {
  logCustomerActivity,
  logCustomerRegisterActivity,
  logReviewAddActivity,
} from '../activity';

export class CustomerService {
  async registerCustomer(customerData: any, siteId: string, token: string) {
    // ... existing register logic

    // สร้างกิจกรรมการสมัครสมาชิกลูกค้า
    await logCustomerRegisterActivity(
      customer._id,
      customer.firstName + ' ' + customer.lastName,
      customer.email,
      siteId,
      token,
      {
        registrationMethod: 'email',
        source: 'website',
      },
    );

    return customer;
  }

  async addReview(
    reviewData: any,
    customerId: string,
    customerName: string,
    siteId: string,
    token: string,
  ) {
    // ... existing review logic

    // สร้างกิจกรรมการเพิ่มรีวิว
    await logReviewAddActivity(
      review.productId,
      review.productName,
      customerId,
      customerName,
      siteId,
      token,
      review.rating,
      {
        comment: review.comment,
        helpful: review.helpful,
      },
    );

    return review;
  }
}
```

### 6. **Analytics Module Integration**

```typescript
// src/modules/analytics/analytics.service.ts
import { logPageViewActivity, logSearchActivity } from '../activity';

export class AnalyticsService {
  async trackPageView(pageData: any, siteId: string, token: string) {
    // ... existing tracking logic

    // สร้างกิจกรรมการเข้าชมหน้าเว็บ
    await logPageViewActivity(
      pageData.url,
      pageData.title,
      siteId,
      token,
      {
        userId: pageData.userId,
        customerId: pageData.customerId,
        sessionId: pageData.sessionId,
        referrer: pageData.referrer,
      },
    );
  }

  async trackSearch(searchData: any, siteId: string, token: string) {
    // ... existing search logic

    // สร้างกิจกรรมการค้นหา
    await logSearchActivity(
      searchData.query,
      searchData.resultsCount,
      siteId,
      token,
      {
        userId: searchData.userId,
        customerId: searchData.customerId,
        filters: searchData.filters,
        sortBy: searchData.sortBy,
      },
    );
  }
}
```

### 7. **System Module Integration**

```typescript
// src/modules/system/system.service.ts
import { logSystemActivity } from '../activity';

export class SystemService {
  async performBackup(backupData: any, token: string) {
    // ... existing backup logic

    // สร้างกิจกรรมการ backup
    await logSystemActivity(
      'system_backup',
      'สร้าง Backup',
      `สร้าง backup สำเร็จ - ${backupData.size} MB`,
      token,
      {
        priority: 'high',
        metadata: {
          backupId: backupData.id,
          size: backupData.size,
          type: backupData.type,
        },
      },
    );
  }

  async performUpdate(updateData: any, token: string) {
    // ... existing update logic

    // สร้างกิจกรรมการอัปเดตระบบ
    await logSystemActivity(
      'system_update',
      'อัปเดตระบบ',
      `อัปเดตระบบเป็นเวอร์ชัน ${updateData.version}`,
      token,
      {
        priority: 'critical',
        metadata: {
          version: updateData.version,
          changes: updateData.changes,
        },
      },
    );
  }
}
```

### 8. **Middleware Integration**

```typescript
// src/core/middleware/activity.middleware.ts
import { logPageViewActivity, logSearchActivity } from '../../modules/activity';

export function activityMiddleware() {
  return async (request: any, response: any, next: any) => {
    const startTime = Date.now();

    // ... existing middleware logic

    // Track page views
    if (request.path && !request.path.startsWith('/api')) {
      await logPageViewActivity(
        request.url,
        request.path,
        request.siteId,
        request.token,
        {
          userId: request.user?._id,
          customerId: request.customer?._id,
          userAgent: request.headers['user-agent'],
          ipAddress: request.ip,
        },
      );
    }

    // Track API searches
    if (request.path.includes('/api/search')) {
      await logSearchActivity(
        request.query.q,
        response.data?.length || 0,
        request.siteId,
        request.token,
        {
          userId: request.user?._id,
          customerId: request.customer?._id,
          filters: request.query.filters,
        },
      );
    }

    next();
  };
}
```

### 9. **Frontend Integration**

```typescript
// dashboard-sveltekit/src/lib/services/activity.ts
import { activityService } from './activity';

export class ActivityClientService {
  async getUserActivities(userId: string, token: string) {
    const result = await activityService.getUserActivities(userId, 20, token);
    return result.data;
  }

  async getSiteActivities(siteId: string, token: string) {
    const result = await activityService.getSiteActivities(siteId, 20, token);
    return result.data;
  }

  async getActivityStats(filters: any, token: string) {
    const result = await activityService.getActivityStats(filters, token);
    return result.data;
  }
}
```

### 10. **Dashboard Integration**

```svelte
<!-- dashboard-sveltekit/src/routes/(protected)/dashboard/activity/+page.svelte -->
<script lang="ts">
  import { activityService } from '$lib/services/activity';
  import ActivityList from '$lib/components/activity/ActivityList.svelte';
  import { authStore } from '$lib/stores/auth.svelte';

  let activities = $state([]);
  let stats = $state({});
  let filters = $state({
    entityType: '',
    type: '',
    startDate: '',
    endDate: '',
    limit: 50
  });

  async function loadActivities() {
    const result = await activityService.getActivities(filters, authStore.token);
    if (result.success) {
      activities = result.data;
    }
  }

  async function loadStats() {
    const result = await activityService.getActivityStats(filters, authStore.token);
    if (result.success) {
      stats = result.data;
    }
  }

  $effect(() => {
    loadActivities();
    loadStats();
  });
</script>

<div class="space-y-6">
  <!-- Filters -->
  <div class="bg-base-100 p-4 rounded-lg">
    <h3 class="text-lg font-semibold mb-4">ตัวกรองกิจกรรม</h3>
    <!-- Filter controls -->
  </div>

  <!-- Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <div class="bg-base-100 p-4 rounded-lg text-center">
      <div class="text-2xl font-bold text-primary">{stats.total || 0}</div>
      <div class="text-sm text-base-content/70">กิจกรรมทั้งหมด</div>
    </div>
    <!-- More stats -->
  </div>

  <!-- Activity List -->
  <div class="bg-base-100 p-4 rounded-lg">
    <h3 class="text-lg font-semibold mb-4">รายการกิจกรรม</h3>
    <ActivityList 
      activities={activities}
      showPriority={true}
      showStatus={true}
      showEntity={true}
      maxItems={50}
      emptyMessage="ไม่มีข้อมูลกิจกรรม"
    />
  </div>
</div>
```

## 🎯 ประโยชน์ของการใช้งาน

1. **📊 Centralized Tracking** - ติดตามกิจกรรมทั้งหมดในที่เดียว
2. **🔍 Advanced Analytics** - วิเคราะห์พฤติกรรมผู้ใช้
3. **📈 Performance Monitoring** - ติดตามประสิทธิภาพระบบ
4. **🔒 Audit Trail** - ตรวจสอบการเปลี่ยนแปลง
5. **📱 User Experience** - ปรับปรุง UX จากการวิเคราะห์
6. **🛡️ Security** - ติดตามการเข้าถึงที่ไม่ปกติ
7. **📊 Business Intelligence** - ข้อมูลสำหรับการตัดสินใจ
8. **🔧 Debugging** - ช่วยในการแก้ไขปัญหา

## 🚀 Best Practices

1. **ใช้ Helper Functions** - ใช้ฟังก์ชันที่เตรียมไว้ให้
2. **Error Handling** - จัดการ error อย่างเหมาะสม
3. **Performance** - ไม่บล็อก main functionality
4. **Metadata** - เก็บข้อมูลเพิ่มเติมที่มีประโยชน์
5. **Privacy** - ไม่เก็บข้อมูลส่วนตัวที่ไม่จำเป็น
6. **Cleanup** - ลบข้อมูลเก่าอย่างสม่ำเสมอ
7. **Monitoring** - ติดตาม performance ของ activity logging
8. **Documentation** - เอกสารการใช้งานที่ชัดเจน

---

**สรุป**: Activity Module สามารถใช้งานได้ในทุกส่วนของระบบ เพื่อติดตามและวิเคราะห์กิจกรรมต่างๆ ได้อย่างมีประสิทธิภาพ
