import { generateFileId } from '@/core/utils';
import mongoose, { Document, Schema } from 'mongoose';

// Activity Types
export type ActivityType =
  // User Activities
  | 'user_signup'
  | 'user_login'
  | 'user_logout'
  | 'user_profile_update'
  | 'user_password_change'
  | 'user_avatar_update'
  | 'user_email_verify'
  | 'user_email_resend'
  | 'user_account_delete'
  | 'user_status_change'
  | 'user_role_change'
  | 'user_points_earned'
  | 'user_points_spent'
  | 'user_topup'
  | 'user_withdrawal'
  // Site Activities
  | 'site_create'
  | 'site_update'
  | 'site_delete'
  | 'site_status_change'
  | 'site_settings_update'
  | 'site_domain_change'
  | 'site_backup_create'
  | 'site_backup_restore'
  // Product Activities
  | 'product_create'
  | 'product_update'
  | 'product_delete'
  | 'product_status_change'
  | 'product_category_change'
  | 'product_price_change'
  | 'product_stock_update'
  | 'product_image_update'
  | 'product_review_add'
  | 'product_review_update'
  | 'product_review_delete'
  | 'product_variant_add'
  | 'product_variant_update'
  | 'product_variant_delete'
  // Order Activities
  | 'order_create'
  | 'order_update'
  | 'order_status_change'
  | 'order_cancel'
  | 'order_refund'
  | 'order_payment_received'
  | 'order_payment_failed'
  | 'order_shipping_update'
  | 'order_tracking_update'
  | 'order_delivered'
  | 'order_return_request'
  | 'order_return_approved'
  | 'order_return_rejected'
  // Customer Activities
  | 'customer_register'
  | 'customer_login'
  | 'customer_profile_update'
  | 'customer_address_add'
  | 'customer_address_update'
  | 'customer_address_delete'
  | 'customer_wishlist_add'
  | 'customer_wishlist_remove'
  | 'customer_review_add'
  | 'customer_review_update'
  | 'customer_review_delete'
  | 'customer_subscription_start'
  | 'customer_subscription_end'
  | 'customer_subscription_renew'
  // Inventory Activities
  | 'inventory_stock_in'
  | 'inventory_stock_out'
  | 'inventory_adjustment'
  | 'inventory_transfer'
  | 'inventory_count'
  | 'inventory_low_stock_alert'
  | 'inventory_out_of_stock_alert'
  | 'inventory_supplier_order'
  | 'inventory_supplier_receive'
  // Discount Activities
  | 'discount_create'
  | 'discount_update'
  | 'discount_delete'
  | 'discount_activate'
  | 'discount_deactivate'
  | 'discount_usage'
  | 'discount_expired'
  // Shipping Activities
  | 'shipping_method_add'
  | 'shipping_method_update'
  | 'shipping_method_delete'
  | 'shipping_rate_update'
  | 'shipping_zone_add'
  | 'shipping_zone_update'
  | 'shipping_zone_delete'
  | 'shipping_tracking_update'
  // Analytics Activities
  | 'analytics_page_view'
  | 'analytics_product_view'
  | 'analytics_search'
  | 'analytics_cart_add'
  | 'analytics_cart_remove'
  | 'analytics_checkout_start'
  | 'analytics_checkout_complete'
  | 'analytics_conversion'
  | 'analytics_bounce'
  | 'analytics_session_start'
  | 'analytics_session_end'
  // Notification Activities
  | 'notification_sent'
  | 'notification_delivered'
  | 'notification_read'
  | 'notification_failed'
  | 'notification_template_create'
  | 'notification_template_update'
  | 'notification_template_delete'
  // Chat Activities
  | 'chat_message_sent'
  | 'chat_message_received'
  | 'chat_session_start'
  | 'chat_session_end'
  | 'chat_transfer'
  | 'chat_rating'
  | 'chat_auto_reply'
  // Media Activities
  | 'media_upload'
  | 'media_delete'
  | 'media_update'
  | 'media_organize'
  | 'media_share'
  | 'media_download'
  // Content Activities
  | 'content_create'
  | 'content_update'
  | 'content_delete'
  | 'content_publish'
  | 'content_unpublish'
  | 'content_schedule'
  | 'content_feature'
  | 'content_archive'
  // Menu Activities
  | 'menu_create'
  | 'menu_update'
  | 'menu_delete'
  | 'menu_item_add'
  | 'menu_item_update'
  | 'menu_item_delete'
  | 'menu_item_reorder'
  // Brand Activities
  | 'brand_create'
  | 'brand_update'
  | 'brand_delete'
  | 'brand_logo_update'
  | 'brand_status_change'
  // Category Activities
  | 'category_create'
  | 'category_update'
  | 'category_delete'
  | 'category_image_update'
  | 'category_reorder'
  // Affiliate Activities
  | 'affiliate_register'
  | 'affiliate_approve'
  | 'affiliate_reject'
  | 'affiliate_commission_earned'
  | 'affiliate_payout'
  | 'affiliate_link_click'
  | 'affiliate_conversion'
  // Social Activities
  | 'social_campaign_create'
  | 'social_campaign_update'
  | 'social_campaign_delete'
  | 'social_post_create'
  | 'social_post_update'
  | 'social_post_delete'
  | 'social_engagement'
  // Ads Activities
  | 'ads_campaign_create'
  | 'ads_campaign_update'
  | 'ads_campaign_delete'
  | 'ads_impression'
  | 'ads_click'
  | 'ads_conversion'
  // Loyalty Activities
  | 'loyalty_points_earned'
  | 'loyalty_points_redeemed'
  | 'loyalty_tier_upgrade'
  | 'loyalty_tier_downgrade'
  | 'loyalty_reward_claim'
  | 'loyalty_reward_expire'
  // Search Activities
  | 'search_query'
  | 'search_filter'
  | 'search_sort'
  | 'search_no_results'
  | 'search_suggestion'
  // System Activities
  | 'system_backup'
  | 'system_restore'
  | 'system_update'
  | 'system_maintenance'
  | 'system_error'
  | 'system_warning'
  | 'system_info';

// Activity Priority
export type ActivityPriority = 'low' | 'medium' | 'high' | 'critical';

// Activity Status
export type ActivityStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';

// Activity Interface
export interface IActivity extends Document {
  _id: string;
  type: ActivityType;
  title: string;
  description: string;
  priority: ActivityPriority;
  status: ActivityStatus;

  // Entity Information
  entityType:
    | 'user'
    | 'site'
    | 'product'
    | 'order'
    | 'customer'
    | 'inventory'
    | 'discount'
    | 'shipping'
    | 'analytics'
    | 'notification'
    | 'chat'
    | 'media'
    | 'content'
    | 'menu'
    | 'brand'
    | 'category'
    | 'affiliate'
    | 'social'
    | 'ads'
    | 'loyalty'
    | 'search'
    | 'system';
  entityId: string;
  entityName?: string;

  // User Information
  userId?: string;
  userName?: string;
  userEmail?: string;
  userRole?: string;

  // Site Information
  siteId?: string;
  siteName?: string;

  // Customer Information (for customer activities)
  customerId?: string;
  customerName?: string;
  customerEmail?: string;

  // Additional Data
  metadata?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;

  // Indexes
  indexes?: {
    type: number;
    entityType: number;
    entityId: number;
    userId: number;
    siteId: number;
    customerId: number;
    createdAt: number;
    priority: number;
    status: number;
  };
}

// Activity Schema
const ActivitySchema = new Schema<IActivity>(
  {
    _id: { type: String, default: () => generateFileId(10) },
    type: {
      type: String,
      required: true,
      enum: [
        // User Activities
        'user_signup',
        'user_login',
        'user_logout',
        'user_profile_update',
        'user_password_change',
        'user_avatar_update',
        'user_email_verify',
        'user_email_resend',
        'user_account_delete',
        'user_status_change',
        'user_role_change',
        'user_points_earned',
        'user_points_spent',
        'user_topup',
        'user_withdrawal',

        // Site Activities
        'site_create',
        'site_update',
        'site_delete',
        'site_status_change',
        'site_settings_update',
        'site_domain_change',
        'site_backup_create',
        'site_backup_restore',

        // Product Activities
        'product_create',
        'product_update',
        'product_delete',
        'product_status_change',
        'product_category_change',
        'product_price_change',
        'product_stock_update',
        'product_image_update',
        'product_review_add',
        'product_review_update',
        'product_review_delete',
        'product_variant_add',
        'product_variant_update',
        'product_variant_delete',

        // Order Activities
        'order_create',
        'order_update',
        'order_status_change',
        'order_cancel',
        'order_refund',
        'order_payment_received',
        'order_payment_failed',
        'order_shipping_update',
        'order_tracking_update',
        'order_delivered',
        'order_return_request',
        'order_return_approved',
        'order_return_rejected',

        // Customer Activities
        'customer_register',
        'customer_login',
        'customer_profile_update',
        'customer_address_add',
        'customer_address_update',
        'customer_address_delete',
        'customer_wishlist_add',
        'customer_wishlist_remove',
        'customer_review_add',
        'customer_review_update',
        'customer_review_delete',
        'customer_subscription_start',
        'customer_subscription_end',
        'customer_subscription_renew',

        // Inventory Activities
        'inventory_stock_in',
        'inventory_stock_out',
        'inventory_adjustment',
        'inventory_transfer',
        'inventory_count',
        'inventory_low_stock_alert',
        'inventory_out_of_stock_alert',
        'inventory_supplier_order',
        'inventory_supplier_receive',

        // Discount Activities
        'discount_create',
        'discount_update',
        'discount_delete',
        'discount_activate',
        'discount_deactivate',
        'discount_usage',
        'discount_expired',

        // Shipping Activities
        'shipping_method_add',
        'shipping_method_update',
        'shipping_method_delete',
        'shipping_rate_update',
        'shipping_zone_add',
        'shipping_zone_update',
        'shipping_zone_delete',
        'shipping_tracking_update',

        // Analytics Activities
        'analytics_page_view',
        'analytics_product_view',
        'analytics_search',
        'analytics_cart_add',
        'analytics_cart_remove',
        'analytics_checkout_start',
        'analytics_checkout_complete',
        'analytics_conversion',
        'analytics_bounce',
        'analytics_session_start',
        'analytics_session_end',

        // Notification Activities
        'notification_sent',
        'notification_delivered',
        'notification_read',
        'notification_failed',
        'notification_template_create',
        'notification_template_update',
        'notification_template_delete',

        // Chat Activities
        'chat_message_sent',
        'chat_message_received',
        'chat_session_start',
        'chat_session_end',
        'chat_transfer',
        'chat_rating',
        'chat_auto_reply',

        // Media Activities
        'media_upload',
        'media_delete',
        'media_update',
        'media_organize',
        'media_share',
        'media_download',

        // Content Activities
        'content_create',
        'content_update',
        'content_delete',
        'content_publish',
        'content_unpublish',
        'content_schedule',
        'content_feature',
        'content_archive',

        // Menu Activities
        'menu_create',
        'menu_update',
        'menu_delete',
        'menu_item_add',
        'menu_item_update',
        'menu_item_delete',
        'menu_item_reorder',

        // Brand Activities
        'brand_create',
        'brand_update',
        'brand_delete',
        'brand_logo_update',
        'brand_status_change',

        // Category Activities
        'category_create',
        'category_update',
        'category_delete',
        'category_image_update',
        'category_reorder',

        // Affiliate Activities
        'affiliate_register',
        'affiliate_approve',
        'affiliate_reject',
        'affiliate_commission_earned',
        'affiliate_payout',
        'affiliate_link_click',
        'affiliate_conversion',

        // Social Activities
        'social_campaign_create',
        'social_campaign_update',
        'social_campaign_delete',
        'social_post_create',
        'social_post_update',
        'social_post_delete',
        'social_engagement',

        // Ads Activities
        'ads_campaign_create',
        'ads_campaign_update',
        'ads_campaign_delete',
        'ads_impression',
        'ads_click',
        'ads_conversion',

        // Loyalty Activities
        'loyalty_points_earned',
        'loyalty_points_redeemed',
        'loyalty_tier_upgrade',
        'loyalty_tier_downgrade',
        'loyalty_reward_claim',
        'loyalty_reward_expire',

        // Search Activities
        'search_query',
        'search_filter',
        'search_sort',
        'search_no_results',
        'search_suggestion',

        // System Activities
        'system_backup',
        'system_restore',
        'system_update',
        'system_maintenance',
        'system_error',
        'system_warning',
        'system_info',
      ],
    },
    title: { type: String, required: true },
    description: { type: String, required: true },
    priority: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium',
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
      default: 'completed',
    },

    // Entity Information
    entityType: {
      type: String,
      required: true,
      enum: [
        'user',
        'site',
        'product',
        'order',
        'customer',
        'inventory',
        'discount',
        'shipping',
        'analytics',
        'notification',
        'chat',
        'media',
        'content',
        'menu',
        'brand',
        'category',
        'affiliate',
        'social',
        'ads',
        'loyalty',
        'search',
        'system',
      ],
    },
    entityId: { type: String, required: true },
    entityName: { type: String },

    // User Information
    userId: { type: String, index: true },
    userName: { type: String },
    userEmail: { type: String },
    userRole: { type: String },

    // Site Information
    siteId: { type: String, index: true },
    siteName: { type: String },

    // Customer Information
    customerId: { type: String, index: true },
    customerName: { type: String },
    customerEmail: { type: String },

    // Additional Data
    metadata: { type: Schema.Types.Mixed },
    oldValues: { type: Schema.Types.Mixed },
    newValues: { type: Schema.Types.Mixed },

    // Timestamps
    processedAt: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      virtuals: true,
      transform: function(doc, ret) {
        ret.id = ret._id;
        // delete ret._id;
        return ret;
      },
    },
    toObject: {
      virtuals: true,
      transform: function(doc, ret) {
        ret.id = ret._id;
        // delete ret._id;
        return ret;
      },
    },
  },
);

// Indexes for better performance
ActivitySchema.index({ type: 1, createdAt: -1 });
ActivitySchema.index({ entityType: 1, entityId: 1, createdAt: -1 });
ActivitySchema.index({ userId: 1, createdAt: -1 });
ActivitySchema.index({ siteId: 1, createdAt: -1 });
ActivitySchema.index({ customerId: 1, createdAt: -1 });
ActivitySchema.index({ priority: 1, status: 1 });
ActivitySchema.index({ createdAt: -1 });
ActivitySchema.index({ status: 1, processedAt: 1 });

// Virtual for formatted date
ActivitySchema.virtual('formattedDate').get(function() {
  return this.createdAt?.toLocaleString('th-TH');
});

// Virtual for activity icon
ActivitySchema.virtual('icon').get(function() {
  const iconMap: Record<ActivityType, string> = {
    // User Activities
    user_signup: 'mdi:account-plus',
    user_login: 'mdi:login',
    user_logout: 'mdi:logout',
    user_profile_update: 'mdi:account-edit',
    user_password_change: 'mdi:lock-reset',
    user_avatar_update: 'mdi:account-circle',
    user_email_verify: 'mdi:email-check',
    user_email_resend: 'mdi:email-send',
    user_account_delete: 'mdi:account-remove',
    user_status_change: 'mdi:account-switch',
    user_role_change: 'mdi:account-cog',
    user_points_earned: 'mdi:plus-circle',
    user_points_spent: 'mdi:minus-circle',
    user_topup: 'mdi:credit-card-plus',
    user_withdrawal: 'mdi:credit-card-minus',

    // Site Activities
    site_create: 'mdi:web-plus',
    site_update: 'mdi:web-edit',
    site_delete: 'mdi:web-remove',
    site_status_change: 'mdi:web-switch',
    site_settings_update: 'mdi:cog',
    site_domain_change: 'mdi:domain',
    site_backup_create: 'mdi:backup-restore',
    site_backup_restore: 'mdi:restore',

    // Product Activities
    product_create: 'mdi:package-plus',
    product_update: 'mdi:package-edit',
    product_delete: 'mdi:package-remove',
    product_status_change: 'mdi:package-switch',
    product_category_change: 'mdi:folder-switch',
    product_price_change: 'mdi:currency-usd',
    product_stock_update: 'mdi:package-variant',
    product_image_update: 'mdi:image-edit',
    product_review_add: 'mdi:star-plus',
    product_review_update: 'mdi:star-edit',
    product_review_delete: 'mdi:star-remove',
    product_variant_add: 'mdi:package-variant-plus',
    product_variant_update: 'mdi:package-variant-edit',
    product_variant_delete: 'mdi:package-variant-remove',

    // Order Activities
    order_create: 'mdi:cart-plus',
    order_update: 'mdi:cart-edit',
    order_status_change: 'mdi:cart-switch',
    order_cancel: 'mdi:cart-remove',
    order_refund: 'mdi:currency-usd-off',
    order_payment_received: 'mdi:credit-card-check',
    order_payment_failed: 'mdi:credit-card-off',
    order_shipping_update: 'mdi:truck-delivery',
    order_tracking_update: 'mdi:map-marker-path',
    order_delivered: 'mdi:package-variant-closed',
    order_return_request: 'mdi:package-variant-remove',
    order_return_approved: 'mdi:check-circle',
    order_return_rejected: 'mdi:close-circle',

    // Customer Activities
    customer_register: 'mdi:account-plus',
    customer_login: 'mdi:login',
    customer_profile_update: 'mdi:account-edit',
    customer_address_add: 'mdi:map-marker-plus',
    customer_address_update: 'mdi:map-marker-edit',
    customer_address_delete: 'mdi:map-marker-remove',
    customer_wishlist_add: 'mdi:heart-plus',
    customer_wishlist_remove: 'mdi:heart-remove',
    customer_review_add: 'mdi:star-plus',
    customer_review_update: 'mdi:star-edit',
    customer_review_delete: 'mdi:star-remove',
    customer_subscription_start: 'mdi:calendar-plus',
    customer_subscription_end: 'mdi:calendar-remove',
    customer_subscription_renew: 'mdi:calendar-refresh',

    // Inventory Activities
    inventory_stock_in: 'mdi:package-variant-plus',
    inventory_stock_out: 'mdi:package-variant-minus',
    inventory_adjustment: 'mdi:package-variant-edit',
    inventory_transfer: 'mdi:package-variant-closed',
    inventory_count: 'mdi:counter',
    inventory_low_stock_alert: 'mdi:alert',
    inventory_out_of_stock_alert: 'mdi:alert-circle',
    inventory_supplier_order: 'mdi:truck',
    inventory_supplier_receive: 'mdi:package-variant-closed',

    // Discount Activities
    discount_create: 'mdi:tag-plus',
    discount_update: 'mdi:tag-edit',
    discount_delete: 'mdi:tag-remove',
    discount_activate: 'mdi:tag-check',
    discount_deactivate: 'mdi:tag-off',
    discount_usage: 'mdi:tag-multiple',
    discount_expired: 'mdi:tag-clock',

    // Shipping Activities
    shipping_method_add: 'mdi:truck-plus',
    shipping_method_update: 'mdi:truck-edit',
    shipping_method_delete: 'mdi:truck-remove',
    shipping_rate_update: 'mdi:currency-usd',
    shipping_zone_add: 'mdi:map-plus',
    shipping_zone_update: 'mdi:map-edit',
    shipping_zone_delete: 'mdi:map-remove',
    shipping_tracking_update: 'mdi:map-marker-path',

    // Analytics Activities
    analytics_page_view: 'mdi:eye',
    analytics_product_view: 'mdi:package-variant',
    analytics_search: 'mdi:magnify',
    analytics_cart_add: 'mdi:cart-plus',
    analytics_cart_remove: 'mdi:cart-minus',
    analytics_checkout_start: 'mdi:cart-arrow-right',
    analytics_checkout_complete: 'mdi:cart-check',
    analytics_conversion: 'mdi:chart-line',
    analytics_bounce: 'mdi:chart-line-variant',
    analytics_session_start: 'mdi:play',
    analytics_session_end: 'mdi:stop',

    // Notification Activities
    notification_sent: 'mdi:bell-ring',
    notification_delivered: 'mdi:bell-check',
    notification_read: 'mdi:bell-off',
    notification_failed: 'mdi:bell-alert',
    notification_template_create: 'mdi:file-plus',
    notification_template_update: 'mdi:file-edit',
    notification_template_delete: 'mdi:file-remove',

    // Chat Activities
    chat_message_sent: 'mdi:message-text',
    chat_message_received: 'mdi:message-text-in',
    chat_session_start: 'mdi:chat-plus',
    chat_session_end: 'mdi:chat-remove',
    chat_transfer: 'mdi:chat-switch',
    chat_rating: 'mdi:star',
    chat_auto_reply: 'mdi:robot',

    // Media Activities
    media_upload: 'mdi:cloud-upload',
    media_delete: 'mdi:cloud-remove',
    media_update: 'mdi:cloud-edit',
    media_organize: 'mdi:folder-edit',
    media_share: 'mdi:share',
    media_download: 'mdi:cloud-download',

    // Content Activities
    content_create: 'mdi:file-plus',
    content_update: 'mdi:file-edit',
    content_delete: 'mdi:file-remove',
    content_publish: 'mdi:publish',
    content_unpublish: 'mdi:unpublish',
    content_schedule: 'mdi:calendar-clock',
    content_feature: 'mdi:star',
    content_archive: 'mdi:archive',

    // Menu Activities
    menu_create: 'mdi:menu-plus',
    menu_update: 'mdi:menu-edit',
    menu_delete: 'mdi:menu-remove',
    menu_item_add: 'mdi:menu-down',
    menu_item_update: 'mdi:menu-edit',
    menu_item_delete: 'mdi:menu-remove',
    menu_item_reorder: 'mdi:menu-swap',

    // Brand Activities
    brand_create: 'mdi:tag-plus',
    brand_update: 'mdi:tag-edit',
    brand_delete: 'mdi:tag-remove',
    brand_logo_update: 'mdi:image-edit',
    brand_status_change: 'mdi:tag-switch',

    // Category Activities
    category_create: 'mdi:folder-plus',
    category_update: 'mdi:folder-edit',
    category_delete: 'mdi:folder-remove',
    category_image_update: 'mdi:image-edit',
    category_reorder: 'mdi:folder-swap',

    // Affiliate Activities
    affiliate_register: 'mdi:account-plus',
    affiliate_approve: 'mdi:check-circle',
    affiliate_reject: 'mdi:close-circle',
    affiliate_commission_earned: 'mdi:currency-usd',
    affiliate_payout: 'mdi:credit-card',
    affiliate_link_click: 'mdi:link',
    affiliate_conversion: 'mdi:chart-line',

    // Social Activities
    social_campaign_create: 'mdi:share-variant',
    social_campaign_update: 'mdi:share-variant-edit',
    social_campaign_delete: 'mdi:share-variant-remove',
    social_post_create: 'mdi:post',
    social_post_update: 'mdi:post-edit',
    social_post_delete: 'mdi:post-remove',
    social_engagement: 'mdi:thumb-up',

    // Ads Activities
    ads_campaign_create: 'mdi:bullhorn',
    ads_campaign_update: 'mdi:bullhorn-edit',
    ads_campaign_delete: 'mdi:bullhorn-remove',
    ads_impression: 'mdi:eye',
    ads_click: 'mdi:cursor-pointer',
    ads_conversion: 'mdi:chart-line',

    // Loyalty Activities
    loyalty_points_earned: 'mdi:plus-circle',
    loyalty_points_redeemed: 'mdi:minus-circle',
    loyalty_tier_upgrade: 'mdi:arrow-up',
    loyalty_tier_downgrade: 'mdi:arrow-down',
    loyalty_reward_claim: 'mdi:gift',
    loyalty_reward_expire: 'mdi:clock-alert',

    // Search Activities
    search_query: 'mdi:magnify',
    search_filter: 'mdi:filter',
    search_sort: 'mdi:sort',
    search_no_results: 'mdi:alert-circle',
    search_suggestion: 'mdi:lightbulb',

    // System Activities
    system_backup: 'mdi:backup-restore',
    system_restore: 'mdi:restore',
    system_update: 'mdi:update',
    system_maintenance: 'mdi:wrench',
    system_error: 'mdi:alert-circle',
    system_warning: 'mdi:alert',
    system_info: 'mdi:information',
  };

  return iconMap[this.type] || 'mdi:information';
});

// Virtual for activity color
ActivitySchema.virtual('color').get(function() {
  const colorMap: Record<ActivityType, string> = {
    // User Activities
    user_signup: 'text-blue-500',
    user_login: 'text-green-500',
    user_logout: 'text-gray-500',
    user_profile_update: 'text-purple-500',
    user_password_change: 'text-orange-500',
    user_avatar_update: 'text-pink-500',
    user_email_verify: 'text-green-500',
    user_email_resend: 'text-blue-500',
    user_account_delete: 'text-red-500',
    user_status_change: 'text-yellow-500',
    user_role_change: 'text-indigo-500',
    user_points_earned: 'text-green-500',
    user_points_spent: 'text-red-500',
    user_topup: 'text-green-500',
    user_withdrawal: 'text-orange-500',

    // Site Activities
    site_create: 'text-blue-500',
    site_update: 'text-purple-500',
    site_delete: 'text-red-500',
    site_status_change: 'text-yellow-500',
    site_settings_update: 'text-indigo-500',
    site_domain_change: 'text-blue-500',
    site_backup_create: 'text-green-500',
    site_backup_restore: 'text-orange-500',

    // Product Activities
    product_create: 'text-green-500',
    product_update: 'text-blue-500',
    product_delete: 'text-red-500',
    product_status_change: 'text-yellow-500',
    product_category_change: 'text-purple-500',
    product_price_change: 'text-orange-500',
    product_stock_update: 'text-indigo-500',
    product_image_update: 'text-pink-500',
    product_review_add: 'text-yellow-500',
    product_review_update: 'text-blue-500',
    product_review_delete: 'text-red-500',
    product_variant_add: 'text-green-500',
    product_variant_update: 'text-blue-500',
    product_variant_delete: 'text-red-500',

    // Order Activities
    order_create: 'text-green-500',
    order_update: 'text-blue-500',
    order_status_change: 'text-yellow-500',
    order_cancel: 'text-red-500',
    order_refund: 'text-orange-500',
    order_payment_received: 'text-green-500',
    order_payment_failed: 'text-red-500',
    order_shipping_update: 'text-blue-500',
    order_tracking_update: 'text-purple-500',
    order_delivered: 'text-green-500',
    order_return_request: 'text-orange-500',
    order_return_approved: 'text-green-500',
    order_return_rejected: 'text-red-500',

    // Customer Activities
    customer_register: 'text-blue-500',
    customer_login: 'text-green-500',
    customer_profile_update: 'text-purple-500',
    customer_address_add: 'text-green-500',
    customer_address_update: 'text-blue-500',
    customer_address_delete: 'text-red-500',
    customer_wishlist_add: 'text-pink-500',
    customer_wishlist_remove: 'text-gray-500',
    customer_review_add: 'text-yellow-500',
    customer_review_update: 'text-blue-500',
    customer_review_delete: 'text-red-500',
    customer_subscription_start: 'text-green-500',
    customer_subscription_end: 'text-red-500',
    customer_subscription_renew: 'text-blue-500',

    // Inventory Activities
    inventory_stock_in: 'text-green-500',
    inventory_stock_out: 'text-red-500',
    inventory_adjustment: 'text-yellow-500',
    inventory_transfer: 'text-blue-500',
    inventory_count: 'text-purple-500',
    inventory_low_stock_alert: 'text-orange-500',
    inventory_out_of_stock_alert: 'text-red-500',
    inventory_supplier_order: 'text-blue-500',
    inventory_supplier_receive: 'text-green-500',

    // Discount Activities
    discount_create: 'text-green-500',
    discount_update: 'text-blue-500',
    discount_delete: 'text-red-500',
    discount_activate: 'text-green-500',
    discount_deactivate: 'text-red-500',
    discount_usage: 'text-yellow-500',
    discount_expired: 'text-gray-500',

    // Shipping Activities
    shipping_method_add: 'text-green-500',
    shipping_method_update: 'text-blue-500',
    shipping_method_delete: 'text-red-500',
    shipping_rate_update: 'text-orange-500',
    shipping_zone_add: 'text-green-500',
    shipping_zone_update: 'text-blue-500',
    shipping_zone_delete: 'text-red-500',
    shipping_tracking_update: 'text-purple-500',

    // Analytics Activities
    analytics_page_view: 'text-blue-500',
    analytics_product_view: 'text-green-500',
    analytics_search: 'text-purple-500',
    analytics_cart_add: 'text-green-500',
    analytics_cart_remove: 'text-red-500',
    analytics_checkout_start: 'text-blue-500',
    analytics_checkout_complete: 'text-green-500',
    analytics_conversion: 'text-green-500',
    analytics_bounce: 'text-red-500',
    analytics_session_start: 'text-green-500',
    analytics_session_end: 'text-gray-500',

    // Notification Activities
    notification_sent: 'text-blue-500',
    notification_delivered: 'text-green-500',
    notification_read: 'text-gray-500',
    notification_failed: 'text-red-500',
    notification_template_create: 'text-green-500',
    notification_template_update: 'text-blue-500',
    notification_template_delete: 'text-red-500',

    // Chat Activities
    chat_message_sent: 'text-blue-500',
    chat_message_received: 'text-green-500',
    chat_session_start: 'text-green-500',
    chat_session_end: 'text-gray-500',
    chat_transfer: 'text-purple-500',
    chat_rating: 'text-yellow-500',
    chat_auto_reply: 'text-indigo-500',

    // Media Activities
    media_upload: 'text-green-500',
    media_delete: 'text-red-500',
    media_update: 'text-blue-500',
    media_organize: 'text-purple-500',
    media_share: 'text-pink-500',
    media_download: 'text-blue-500',

    // Content Activities
    content_create: 'text-green-500',
    content_update: 'text-blue-500',
    content_delete: 'text-red-500',
    content_publish: 'text-green-500',
    content_unpublish: 'text-red-500',
    content_schedule: 'text-purple-500',
    content_feature: 'text-yellow-500',
    content_archive: 'text-gray-500',

    // Menu Activities
    menu_create: 'text-green-500',
    menu_update: 'text-blue-500',
    menu_delete: 'text-red-500',
    menu_item_add: 'text-green-500',
    menu_item_update: 'text-blue-500',
    menu_item_delete: 'text-red-500',
    menu_item_reorder: 'text-purple-500',

    // Brand Activities
    brand_create: 'text-green-500',
    brand_update: 'text-blue-500',
    brand_delete: 'text-red-500',
    brand_logo_update: 'text-pink-500',
    brand_status_change: 'text-yellow-500',

    // Category Activities
    category_create: 'text-green-500',
    category_update: 'text-blue-500',
    category_delete: 'text-red-500',
    category_image_update: 'text-pink-500',
    category_reorder: 'text-purple-500',

    // Affiliate Activities
    affiliate_register: 'text-blue-500',
    affiliate_approve: 'text-green-500',
    affiliate_reject: 'text-red-500',
    affiliate_commission_earned: 'text-green-500',
    affiliate_payout: 'text-blue-500',
    affiliate_link_click: 'text-purple-500',
    affiliate_conversion: 'text-green-500',

    // Social Activities
    social_campaign_create: 'text-green-500',
    social_campaign_update: 'text-blue-500',
    social_campaign_delete: 'text-red-500',
    social_post_create: 'text-green-500',
    social_post_update: 'text-blue-500',
    social_post_delete: 'text-red-500',
    social_engagement: 'text-yellow-500',

    // Ads Activities
    ads_campaign_create: 'text-green-500',
    ads_campaign_update: 'text-blue-500',
    ads_campaign_delete: 'text-red-500',
    ads_impression: 'text-blue-500',
    ads_click: 'text-green-500',
    ads_conversion: 'text-green-500',

    // Loyalty Activities
    loyalty_points_earned: 'text-green-500',
    loyalty_points_redeemed: 'text-red-500',
    loyalty_tier_upgrade: 'text-green-500',
    loyalty_tier_downgrade: 'text-red-500',
    loyalty_reward_claim: 'text-yellow-500',
    loyalty_reward_expire: 'text-gray-500',

    // Search Activities
    search_query: 'text-blue-500',
    search_filter: 'text-purple-500',
    search_sort: 'text-indigo-500',
    search_no_results: 'text-red-500',
    search_suggestion: 'text-yellow-500',

    // System Activities
    system_backup: 'text-green-500',
    system_restore: 'text-orange-500',
    system_update: 'text-blue-500',
    system_maintenance: 'text-yellow-500',
    system_error: 'text-red-500',
    system_warning: 'text-orange-500',
    system_info: 'text-blue-500',
  };

  return colorMap[this.type] || 'text-gray-500';
});

export const Activity = mongoose.model<IActivity>('Activity', ActivitySchema);
