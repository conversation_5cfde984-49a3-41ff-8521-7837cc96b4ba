import { Elysia, t } from 'elysia';
import { userPlugin } from '../../core/middleware/checkUser';
import { HttpError } from '../../core/utils/error';
import { activityService } from './activity.service';

export const activityRoutes = new Elysia({ prefix: '/activity' })
  .use(userPlugin)
  // ✅ Get all activities with filters
  .get('/', async ({ query, user }: any) => {
    try {
      const filters = {
        type: query.type,
        entityType: query.entityType,
        entityId: query.entityId,
        userId: query.userId,
        siteId: query.siteId,
        customerId: query.customerId,
        priority: query.priority,
        status: query.status,
        startDate: query.startDate ? new Date(query.startDate) : undefined,
        endDate: query.endDate ? new Date(query.endDate) : undefined,
        limit: query.limit ? parseInt(query.limit) : 50,
        skip: query.skip ? parseInt(query.skip) : 0,
      };

      const activities = await activityService.getActivities(filters);

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activities,
      };
    }
    catch (err: any) {
      console.error('Error in get activities:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรม');
    }
  }, {
    query: t.Object({
      type: t.Optional(t.String()),
      entityType: t.Optional(t.String()),
      entityId: t.Optional(t.String()),
      userId: t.Optional(t.String()),
      siteId: t.Optional(t.String()),
      customerId: t.Optional(t.String()),
      priority: t.Optional(t.String()),
      status: t.Optional(t.String()),
      startDate: t.Optional(t.String()),
      endDate: t.Optional(t.String()),
      limit: t.Optional(t.String()),
      skip: t.Optional(t.String()),
    }),
  })
  // ✅ Get activity by ID
  .get('/:id', async ({ params }: any) => {
    try {
      const { id } = params;
      const activity = await activityService.getActivityById(id);

      if (!activity) {
        throw new HttpError(404, 'ไม่พบข้อมูลกิจกรรม');
      }

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in get activity by ID:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรม');
    }
  }, {
    params: t.Object({
      id: t.String(),
    }),
  })
  // ✅ Get recent activities
  .get('/recent', async ({ query }: any) => {
    try {
      const limit = query.limit ? parseInt(query.limit) : 10;
      const activities = await activityService.getRecentActivities(limit);

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมล่าสุดสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activities,
      };
    }
    catch (err: any) {
      console.error('Error in get recent activities:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรมล่าสุด');
    }
  }, {
    query: t.Object({
      limit: t.Optional(t.String()),
    }),
  })
  // ✅ Get activities by entity
  .get('/entity/:entityType/:entityId', async ({ params, query }: any) => {
    try {
      const { entityType, entityId } = params;
      const limit = query.limit ? parseInt(query.limit) : 20;

      const activities = await activityService.getActivitiesByEntity(entityType, entityId, limit);

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมตาม entity สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activities,
      };
    }
    catch (err: any) {
      console.error('Error in get activities by entity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรม');
    }
  }, {
    params: t.Object({
      entityType: t.String(),
      entityId: t.String(),
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
    }),
  })
  // ✅ Get user activities
  .get('/user/:userId', async ({ params, query }: any) => {
    try {
      const { userId } = params;
      const limit = query.limit ? parseInt(query.limit) : 20;

      const activities = await activityService.getUserActivities(userId, limit);

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมผู้ใช้สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activities,
      };
    }
    catch (err: any) {
      console.error('Error in get user activities:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรมผู้ใช้');
    }
  }, {
    params: t.Object({
      userId: t.String(),
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
    }),
  })
  // ✅ Get site activities
  .get('/site/:siteId', async ({ params, query }: any) => {
    try {
      const { siteId } = params;
      const limit = query.limit ? parseInt(query.limit) : 20;

      const activities = await activityService.getSiteActivities(siteId, limit);

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมไซต์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activities,
      };
    }
    catch (err: any) {
      console.error('Error in get site activities:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรมไซต์');
    }
  }, {
    params: t.Object({
      siteId: t.String(),
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
    }),
  })
  // ✅ Get customer activities
  .get('/customer/:customerId', async ({ params, query }: any) => {
    try {
      const { customerId } = params;
      const limit = query.limit ? parseInt(query.limit) : 20;

      const activities = await activityService.getCustomerActivities(customerId, limit);

      return {
        success: true,
        message: 'ดึงข้อมูลกิจกรรมลูกค้าสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activities,
      };
    }
    catch (err: any) {
      console.error('Error in get customer activities:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลกิจกรรมลูกค้า');
    }
  }, {
    params: t.Object({
      customerId: t.String(),
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
    }),
  })
  // ✅ Get activity statistics
  .get('/stats', async ({ query }: any) => {
    try {
      const filters = {
        type: query.type,
        entityType: query.entityType,
        entityId: query.entityId,
        userId: query.userId,
        siteId: query.siteId,
        customerId: query.customerId,
        priority: query.priority,
        status: query.status,
        startDate: query.startDate ? new Date(query.startDate) : undefined,
        endDate: query.endDate ? new Date(query.endDate) : undefined,
      };

      const stats = await activityService.getActivityStats(filters);

      return {
        success: true,
        message: 'ดึงข้อมูลสถิติกิจกรรมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: stats,
      };
    }
    catch (err: any) {
      console.error('Error in get activity stats:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลสถิติกิจกรรม');
    }
  }, {
    query: t.Object({
      type: t.Optional(t.String()),
      entityType: t.Optional(t.String()),
      entityId: t.Optional(t.String()),
      userId: t.Optional(t.String()),
      siteId: t.Optional(t.String()),
      customerId: t.Optional(t.String()),
      priority: t.Optional(t.String()),
      status: t.Optional(t.String()),
      startDate: t.Optional(t.String()),
      endDate: t.Optional(t.String()),
    }),
  })
  // ✅ Create activity
  .post('/', async ({ body, user }: any) => {
    try {
      const activity = await activityService.createActivity({
        ...body,
        userId: body.userId || user?._id,
        userName: body.userName || user?.firstName + ' ' + user?.lastName,
        userEmail: body.userEmail || user?.email,
        userRole: body.userRole || user?.role,
      });

      return {
        success: true,
        message: 'สร้างกิจกรรมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรม');
    }
  }, {
    body: t.Object({
      type: t.String(),
      title: t.String(),
      description: t.String(),
      priority: t.Optional(t.Union([
        t.Literal('low'),
        t.Literal('medium'),
        t.Literal('high'),
        t.Literal('critical'),
      ])),
      status: t.Optional(t.Union([
        t.Literal('pending'),
        t.Literal('processing'),
        t.Literal('completed'),
        t.Literal('failed'),
        t.Literal('cancelled'),
      ])),
      entityType: t.String(),
      entityId: t.String(),
      entityName: t.Optional(t.String()),
      userId: t.Optional(t.String()),
      userName: t.Optional(t.String()),
      userEmail: t.Optional(t.String()),
      userRole: t.Optional(t.String()),
      siteId: t.Optional(t.String()),
      siteName: t.Optional(t.String()),
      customerId: t.Optional(t.String()),
      customerName: t.Optional(t.String()),
      customerEmail: t.Optional(t.String()),
      metadata: t.Optional(t.Any()),
      oldValues: t.Optional(t.Any()),
      newValues: t.Optional(t.Any()),
    }),
  })
  // ✅ Create user activity
  .post('/user', async ({ body, user }: any) => {
    try {
      const activity = await activityService.createUserActivity(
        body.type,
        body.userId || user?._id,
        body.userName || user?.firstName + ' ' + user?.lastName,
        body.userEmail || user?.email,
        body.userRole || user?.role,
        body.title,
        body.description,
        body.metadata,
      );

      return {
        success: true,
        message: 'สร้างกิจกรรมผู้ใช้สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create user activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรมผู้ใช้');
    }
  }, {
    body: t.Object({
      type: t.String(),
      title: t.String(),
      description: t.String(),
      userId: t.Optional(t.String()),
      userName: t.Optional(t.String()),
      userEmail: t.Optional(t.String()),
      userRole: t.Optional(t.String()),
      metadata: t.Optional(t.Any()),
    }),
  })
  // ✅ Create site activity
  .post('/site', async ({ body, user }: any) => {
    try {
      const activity = await activityService.createSiteActivity(
        body.type,
        body.siteId,
        body.siteName,
        body.userId || user?._id,
        body.userName || user?.firstName + ' ' + user?.lastName,
        body.title,
        body.description,
        body.metadata,
      );

      return {
        success: true,
        message: 'สร้างกิจกรรมไซต์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create site activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรมไซต์');
    }
  }, {
    body: t.Object({
      type: t.String(),
      siteId: t.String(),
      siteName: t.String(),
      title: t.String(),
      description: t.String(),
      userId: t.Optional(t.String()),
      userName: t.Optional(t.String()),
      metadata: t.Optional(t.Any()),
    }),
  })
  // ✅ Create product activity
  .post('/product', async ({ body, user }: any) => {
    try {
      const activity = await activityService.createProductActivity(
        body.type,
        body.productId,
        body.productName,
        body.siteId,
        body.userId || user?._id,
        body.userName || user?.firstName + ' ' + user?.lastName,
        body.title,
        body.description,
        body.metadata,
      );

      return {
        success: true,
        message: 'สร้างกิจกรรมสินค้าสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create product activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรมสินค้า');
    }
  }, {
    body: t.Object({
      type: t.String(),
      productId: t.String(),
      productName: t.String(),
      siteId: t.String(),
      title: t.String(),
      description: t.String(),
      userId: t.Optional(t.String()),
      userName: t.Optional(t.String()),
      metadata: t.Optional(t.Any()),
    }),
  })
  // ✅ Create order activity
  .post('/order', async ({ body, user }: any) => {
    try {
      const activity = await activityService.createOrderActivity(
        body.type,
        body.orderId,
        body.orderNumber,
        body.siteId,
        body.customerId,
        body.customerName,
        body.userId || user?._id,
        body.userName || user?.firstName + ' ' + user?.lastName,
        body.title,
        body.description,
        body.metadata,
      );

      return {
        success: true,
        message: 'สร้างกิจกรรมออเดอร์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create order activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรมออเดอร์');
    }
  }, {
    body: t.Object({
      type: t.String(),
      orderId: t.String(),
      orderNumber: t.String(),
      siteId: t.String(),
      customerId: t.String(),
      customerName: t.String(),
      title: t.String(),
      description: t.String(),
      userId: t.Optional(t.String()),
      userName: t.Optional(t.String()),
      metadata: t.Optional(t.Any()),
    }),
  })
  // ✅ Create customer activity
  .post('/customer', async ({ body, user }: any) => {
    try {
      const activity = await activityService.createCustomerActivity(
        body.type,
        body.customerId,
        body.customerName,
        body.customerEmail,
        body.siteId,
        body.title,
        body.description,
        body.metadata,
      );

      return {
        success: true,
        message: 'สร้างกิจกรรมลูกค้าสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create customer activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรมลูกค้า');
    }
  }, {
    body: t.Object({
      type: t.String(),
      customerId: t.String(),
      customerName: t.String(),
      customerEmail: t.String(),
      siteId: t.String(),
      title: t.String(),
      description: t.String(),
      metadata: t.Optional(t.Any()),
    }),
  })
  // ✅ Create system activity
  .post('/system', async ({ body }: any) => {
    try {
      const activity = await activityService.createSystemActivity(
        body.type,
        body.title,
        body.description,
        body.priority,
        body.metadata,
      );

      return {
        success: true,
        message: 'สร้างกิจกรรมระบบสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in create system activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างกิจกรรมระบบ');
    }
  }, {
    body: t.Object({
      type: t.String(),
      title: t.String(),
      description: t.String(),
      priority: t.Optional(t.Union([
        t.Literal('low'),
        t.Literal('medium'),
        t.Literal('high'),
        t.Literal('critical'),
      ])),
      metadata: t.Optional(t.Any()),
    }),
  })
  // ✅ Update activity status
  .put('/:id/status', async ({ params, body }: any) => {
    try {
      const { id } = params;
      const { status } = body;

      const activity = await activityService.updateActivityStatus(id, status);

      if (!activity) {
        throw new HttpError(404, 'ไม่พบข้อมูลกิจกรรม');
      }

      return {
        success: true,
        message: 'อัปเดตสถานะกิจกรรมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: activity,
      };
    }
    catch (err: any) {
      console.error('Error in update activity status:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะกิจกรรม');
    }
  }, {
    params: t.Object({
      id: t.String(),
    }),
    body: t.Object({
      status: t.Union([
        t.Literal('pending'),
        t.Literal('processing'),
        t.Literal('completed'),
        t.Literal('failed'),
        t.Literal('cancelled'),
      ]),
    }),
  })
  // ✅ Delete activity
  .delete('/:id', async ({ params }: any) => {
    try {
      const { id } = params;
      const deleted = await activityService.deleteActivity(id);

      if (!deleted) {
        throw new HttpError(404, 'ไม่พบข้อมูลกิจกรรม');
      }

      return {
        success: true,
        message: 'ลบกิจกรรมสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    }
    catch (err: any) {
      console.error('Error in delete activity:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบกิจกรรม');
    }
  }, {
    params: t.Object({
      id: t.String(),
    }),
  })
  // ✅ Cleanup old activities
  .delete('/cleanup', async ({ query }: any) => {
    try {
      const daysOld = query.daysOld ? parseInt(query.daysOld) : 90;
      const deletedCount = await activityService.cleanupOldActivities(daysOld);

      return {
        success: true,
        message: `ลบกิจกรรมเก่า ${deletedCount} รายการสำเร็จ`,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: { deletedCount },
      };
    }
    catch (err: any) {
      console.error('Error in cleanup activities:', err);
      if (err instanceof HttpError) throw err;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบกิจกรรมเก่า');
    }
  }, {
    query: t.Object({
      daysOld: t.Optional(t.String()),
    }),
  });
