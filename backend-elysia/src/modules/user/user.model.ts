import { generateFileId } from '@/core/utils/idGenerator';
import { t } from 'elysia';
import { model, Schema } from 'mongoose';

export interface IUserBase {
  _id: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  password: string;
  role: 'user' | 'admin';
  avatar?: string;
  cover?: string;
  refreshToken?: string;
  resetPasswordToken?: string;
  isEmailVerified: boolean;
  profilePicture?: string;
  moneyPoint: number;
  goldPoint: number;
}

export interface IUser extends IUserBase {
  comparePassword(candidatePassword: string): Promise<boolean>;
  createdAt: Date;
  updatedAt: Date;
}

export const userSchema = t.Object({
  email: t.String({ format: 'email' }),
  password: t.String({ minLength: 6 }),
  role: t.Union([t.Literal('user'), t.Literal('admin')]),
});

const userSchemaMongoose = new Schema<IUser>(
  {
    _id: {
      type: String,
      required: true,
      trim: true,
      default: () => generateFileId(5),
    },
    firstName: {
      type: String,
      required: false,
    },
    lastName: {
      type: String,
      required: false,
    },
    phone: {
      type: String,
      required: false,
    },
    moneyPoint: {
      type: Number,
      default: 0,
    },
    goldPoint: {
      type: Number,
      default: 0,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
    },
    avatar: {
      type: String,
      default: 'avatardemo',
    },
    cover: {
      type: String,
      default: 'coverdemo',
    },
    refreshToken: {
      type: String,
      select: false,
    },
    resetPasswordToken: {
      type: String,
      select: false,
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    profilePicture: {
      type: String,
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user',
    },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
  },
);

// Hash password before saving
userSchemaMongoose.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const hash = await Bun.password.hash(this.password, {
      algorithm: 'argon2id',
      memoryCost: 65536, // 64MB
      timeCost: 3,
    });
    this.password = hash;
    next();
  }
  catch (error: unknown) {
    if (error instanceof Error) {
      next(error);
    }
    else {
      console.error('Unexpected error type during password hashing:', error);
      next(new Error(String(error)));
    }
  }
});

// Compare password method
userSchemaMongoose.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return Bun.password.verify(candidatePassword, this.password);
};

export const User = model<IUser>('User', userSchemaMongoose);
