import { HttpError } from '@/core/utils/error';
import { Product } from '../product/product.model';
import { PreOrder } from './preorder.model';

// PreOrder Service
export async function createPreOrder(preOrderData: {
  siteId: string;
  customerId: string;
  customerEmail: string;
  customerName: string;
  items: Array<{
    productId: string;
    variantId?: string;
    quantity: number;
  }>;
  expectedReleaseDate: Date;
  estimatedDeliveryDate?: Date;
  shippingAddress: any;
  billingAddress: any;
  paymentMethod: string;
  customerNotes?: string;
  adminNotes?: string;
  isNotifyOnRelease?: boolean;
}) {
  try {
    const {
      siteId,
      customerId,
      customerEmail,
      customerName,
      items,
      expectedReleaseDate,
      estimatedDeliveryDate,
      shippingAddress,
      billingAddress,
      paymentMethod,
      customerNotes,
      adminNotes,
      isNotifyOnRelease = true,
    } = preOrderData;

    // ตรวจสอบสินค้าและคำนวณราคา
    const preOrderItems = [];
    let subtotal = 0;

    for (const item of items) {
      const product = await Product.findById(item.productId);
      if (!product) {
        throw new HttpError(400, `ไม่พบสินค้า ${item.productId}`);
      }

      if (!product.isActive) {
        throw new HttpError(400, `สินค้า ${product.name} ไม่เปิดขาย`);
      }

      // ตรวจสอบว่าสินค้าเปิด pre-order หรือไม่
      if (!product.allowPreOrder) {
        throw new HttpError(400, `สินค้า ${product.name} ไม่รองรับการสั่งจอง`);
      }

      // คำนวณราคา
      let unitPrice = product.price;
      if (product.hasVariants && item.variantId) {
        const variant = product.variants.find(v => v._id === item.variantId);
        if (!variant) {
          throw new HttpError(400, `ไม่พบ variant ${item.variantId}`);
        }
        if (variant.price) {
          unitPrice = variant.price;
        }
      }

      const totalPrice = unitPrice * item.quantity;
      subtotal += totalPrice;

      preOrderItems.push({
        productId: item.productId,
        variantId: item.variantId,
        name: product.name,
        sku: product.hasVariants && item.variantId ? product.variants.find(v => v._id === item.variantId)?.sku
          : undefined,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        discount: 0,
        finalPrice: totalPrice,
      });
    }

    // สร้าง PreOrder
    const preOrder = await PreOrder.create({
      siteId,
      customerId,
      customerEmail,
      customerName,
      items: preOrderItems,
      subtotal,
      tax: 0, // คำนวณตาม tax rate
      shipping: 0, // คำนวณตาม shipping method
      discount: 0,
      total: subtotal,
      expectedReleaseDate,
      estimatedDeliveryDate,
      paymentMethod,
      shippingAddress,
      billingAddress,
      customerNotes,
      adminNotes,
      isNotifyOnRelease,
    });

    return {
      success: true,
      message: 'สร้างการสั่งจองสำเร็จ',
      data: preOrder,
    };
  }
  catch (err: any) {
    console.error('Error in createPreOrder:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างการสั่งจอง');
  }
}

export async function getPreOrderById(preOrderId: string) {
  try {
    const preOrder = await PreOrder.findById(preOrderId);
    if (!preOrder) {
      throw new HttpError(404, 'ไม่พบการสั่งจอง');
    }
    return preOrder;
  }
  catch (err: any) {
    console.error('Error in getPreOrderById:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูลการสั่งจอง');
  }
}

export async function getPreOrdersBySite(siteId: string, filter: any = {}) {
  try {
    const query = { siteId, ...filter };
    const preOrders = await PreOrder.find(query).sort({ createdAt: -1 });
    return preOrders;
  }
  catch (err: any) {
    console.error('Error in getPreOrdersBySite:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการสั่งจอง');
  }
}

export async function getPreOrdersByCustomer(customerId: string, siteId: string) {
  try {
    const preOrders = await PreOrder.find({ customerId, siteId }).sort({ createdAt: -1 });
    return preOrders;
  }
  catch (err: any) {
    console.error('Error in getPreOrdersByCustomer:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสั่งจองของลูกค้า');
  }
}

export async function updatePreOrderStatus(preOrderId: string, newStatus: PreOrderStatus) {
  try {
    const preOrder = await PreOrder.findById(preOrderId);
    if (!preOrder) {
      throw new HttpError(404, 'ไม่พบการสั่งจอง');
    }

    await (preOrder as any).updateStatus(newStatus);

    return {
      success: true,
      message: 'อัปเดตสถานะการสั่งจองสำเร็จ',
      data: preOrder,
    };
  }
  catch (err: any) {
    console.error('Error in updatePreOrderStatus:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะการสั่งจอง');
  }
}

export async function updatePaymentStatus(preOrderId: string, newStatus: PaymentStatus, paymentId?: string) {
  try {
    const preOrder = await PreOrder.findById(preOrderId);
    if (!preOrder) {
      throw new HttpError(404, 'ไม่พบการสั่งจอง');
    }

    if (paymentId) {
      preOrder.paymentId = paymentId;
    }

    await (preOrder as any).updatePaymentStatus(newStatus);

    return {
      success: true,
      message: 'อัปเดตสถานะการชำระเงินสำเร็จ',
      data: preOrder,
    };
  }
  catch (err: any) {
    console.error('Error in updatePaymentStatus:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะการชำระเงิน');
  }
}

export async function cancelPreOrder(preOrderId: string, reason?: string) {
  try {
    const preOrder = await PreOrder.findById(preOrderId);
    if (!preOrder) {
      throw new HttpError(404, 'ไม่พบการสั่งจอง');
    }

    if (preOrder.status === 'cancelled') {
      throw new HttpError(400, 'การสั่งจองนี้ถูกยกเลิกแล้ว');
    }

    if (preOrder.status === 'available') {
      throw new HttpError(400, 'ไม่สามารถยกเลิกการสั่งจองที่สินค้าพร้อมแล้ว');
    }

    preOrder.status = 'cancelled';
    preOrder.cancelledAt = new Date();
    if (reason) {
      preOrder.adminNotes = reason;
    }

    await preOrder.save();

    return {
      success: true,
      message: 'ยกเลิกการสั่งจองสำเร็จ',
      data: preOrder,
    };
  }
  catch (err: any) {
    console.error('Error in cancelPreOrder:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะยกเลิกการสั่งจอง');
  }
}

export async function updateReleaseDate(preOrderId: string, expectedReleaseDate: Date, estimatedDeliveryDate?: Date) {
  try {
    const preOrder = await PreOrder.findByIdAndUpdate(
      preOrderId,
      { expectedReleaseDate, estimatedDeliveryDate },
      { new: true },
    );

    if (!preOrder) {
      throw new HttpError(404, 'ไม่พบการสั่งจอง');
    }

    return {
      success: true,
      message: 'อัปเดตวันที่วางจำหน่ายสำเร็จ',
      data: preOrder,
    };
  }
  catch (err: any) {
    console.error('Error in updateReleaseDate:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตวันที่วางจำหน่าย');
  }
}

export async function markAsAvailable(preOrderId: string) {
  try {
    const preOrder = await PreOrder.findById(preOrderId);
    if (!preOrder) {
      throw new HttpError(404, 'ไม่พบการสั่งจอง');
    }

    preOrder.status = 'available';
    preOrder.availableAt = new Date();
    await preOrder.save();

    return {
      success: true,
      message: 'สินค้าพร้อมจัดส่งแล้ว',
      data: preOrder,
    };
  }
  catch (err: any) {
    console.error('Error in markAsAvailable:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะสินค้า');
  }
}

// Dashboard Analytics
export async function getPreOrderStats(siteId: string) {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const [totalPreOrders, todayPreOrders, monthlyPreOrders, pendingPreOrders, availablePreOrders] = await Promise.all([
      PreOrder.countDocuments({ siteId }),
      PreOrder.countDocuments({ siteId, createdAt: { $gte: startOfDay } }),
      PreOrder.countDocuments({ siteId, createdAt: { $gte: startOfMonth } }),
      PreOrder.countDocuments({ siteId, status: 'pending' }),
      PreOrder.countDocuments({ siteId, status: 'available' }),
    ]);

    const revenue = await PreOrder.aggregate([
      { $match: { siteId, paymentStatus: 'paid' } },
      { $group: { _id: null, total: { $sum: '$total' } } },
    ]);

    return {
      totalPreOrders,
      todayPreOrders,
      monthlyPreOrders,
      pendingPreOrders,
      availablePreOrders,
      totalRevenue: revenue[0]?.total || 0,
    };
  }
  catch (err: any) {
    console.error('Error in getPreOrderStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติการสั่งจอง');
  }
}

// Get pre-orders that are ready for release
export async function getReadyForRelease(siteId: string) {
  try {
    const today = new Date();
    const readyPreOrders = await PreOrder.find({
      siteId,
      status: { $in: ['pending', 'confirmed', 'processing'] },
      expectedReleaseDate: { $lte: today },
    }).sort({ expectedReleaseDate: 1 });

    return readyPreOrders;
  }
  catch (err: any) {
    console.error('Error in getReadyForRelease:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการพร้อมวางจำหน่าย');
  }
}
