import { isSiteOwner } from '@/core/middleware/checkUser';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { logger } from '@/core/utils/logger';
import { Elysia, t } from 'elysia';
import {
  cancelOrderSchema,
  createOrderSchema,
  orderResponseSchema,
  updateOrderStatusSchema,
  updatePaymentStatusSchema,
  updateShippingSchema,
} from './order.schema';
import {
  cancelOrder,
  createOrder,
  getOrderById,
  getOrdersBySite,
  getOrderStats,
  updateOrderShipping,
  updateOrderStatus,
  updatePaymentStatus,
} from './order.service';

export const orderRoutes = new Elysia({ prefix: '/order' })
  .use(userAuthPlugin)
  // Dashboard Order Routes (ใช้ siteId จาก URL)
  .group('/dashboard/:siteId', app =>
    app
      .use(userAuthPlugin)
      .get(
        '/list',
        async ({ params, query, user }: any) => {
          try {
            const { siteId } = params;
            const userId = user?.userId;

            if (!userId) {
              throw new Error('ไม่พบข้อมูลผู้ใช้');
            }

            // ตรวจสอบว่าเป็น site owner หรือไม่
            const isOwner = await isSiteOwner(siteId, userId);
            if (!isOwner) {
              throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
            }

            const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc' } = query;

            const orders = await getOrdersBySite(siteId, {
              page: parseInt(page),
              limit: parseInt(limit),
              status,
              sortBy,
              sortOrder,
            });

            return {
              success: true,
              message: 'ดึงรายการออเดอร์สำเร็จ',
              data: orders,
            };
          }
          catch (error: any) {
            logger.error('เกิดข้อผิดพลาดในการดึงรายการออเดอร์:', error);
            return {
              success: false,
              message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการออเดอร์',
            };
          }
        },
        {
          query: t.Object({
            page: t.Optional(t.String()),
            limit: t.Optional(t.String()),
            status: t.Optional(t.String()),
            sortBy: t.Optional(t.String()),
            sortOrder: t.Optional(t.Union([t.Literal('asc'), t.Literal('desc')])),
          }),
        },
      )
      .get(
        '/stats',
        async ({ params, query, user }: any) => {
          try {
            const { siteId } = params;
            const userId = user?.userId;

            if (!userId) {
              throw new Error('ไม่พบข้อมูลผู้ใช้');
            }

            // ตรวจสอบว่าเป็น site owner หรือไม่
            const isOwner = await isSiteOwner(siteId, userId);
            if (!isOwner) {
              throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
            }

            const dateRange = {
              start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              end: query.endDate ? new Date(query.endDate) : new Date(),
            };

            const stats = await getOrderStats(siteId, dateRange);

            return {
              success: true,
              message: 'ดึงสถิติออเดอร์สำเร็จ',
              statusMessage: 'สำเร็จ!',
              timestamp: new Date().toISOString(),
              data: stats,
            };
          }
          catch (error: any) {
            logger.error('เกิดข้อผิดพลาดในการดึงสถิติออเดอร์:', error);
            return {
              success: false,
              message: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติออเดอร์',
              statusMessage: 'เกิดข้อผิดพลาด',
              timestamp: new Date().toISOString(),
            };
          }
        },
        {
          query: t.Object({
            startDate: t.Optional(t.String()),
            endDate: t.Optional(t.String()),
          }),
        },
      ))
  // สร้างออเดอร์ใหม่
  .post(
    '/create',
    async ({ body, user }: any) => {
      const { siteId } = body;
      const orderData = {
        ...body,
        customerId: user._id,
        siteId,
      };

      const result = await createOrder(orderData);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: createOrderSchema,
      response: orderResponseSchema,
    },
  )
  // ดึงรายการออเดอร์ของไซต์
  .get(
    '/list',
    async ({ query, _user, site }: any) => {
      try {
        const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc' } = query;

        const orders = await getOrdersBySite(site._id, {
          page: parseInt(page),
          limit: parseInt(limit),
          status,
          sortBy,
          sortOrder,
        });

        return {
          success: true,
          message: 'ดึงรายการออเดอร์สำเร็จ',
          data: orders,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดึงรายการออเดอร์:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการออเดอร์',
        };
      }
    },
    {
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
        status: t.Optional(t.String()),
        sortBy: t.Optional(t.String()),
        sortOrder: t.Optional(t.Union([t.Literal('asc'), t.Literal('desc')])),
      }),
    },
  )
  // ดึงออเดอร์ของลูกค้า
  .get(
    '/my-orders',
    async ({ user, query }: any) => {
      const { siteId } = query;
      if (!siteId) {
        throw new HttpError(400, 'กรุณาระบุ siteId');
      }

      const orders = await getOrdersByCustomer(user._id, siteId);

      return {
        success: true,
        message: 'ดึงออเดอร์ของลูกค้าสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: orders,
      };
    },
    {
      response: orderResponseSchema,
    },
  )
  // ดึงข้อมูลออเดอร์เฉพาะ
  .get(
    '/:orderId',
    async ({ params }: any) => {
      const { orderId } = params;
      const order = await getOrderById(orderId);

      return {
        success: true,
        message: 'ดึงข้อมูลออเดอร์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: order,
      };
    },
    {
      response: orderResponseSchema,
    },
  )
  // อัปเดตสถานะออเดอร์
  .put(
    '/:orderId/status',
    async ({ params, body }: any) => {
      const { orderId } = params;
      const { status } = body;

      const result = await updateOrderStatus(orderId, status);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updateOrderStatusSchema,
      response: orderResponseSchema,
    },
  )
  // อัปเดตสถานะการชำระเงิน
  .put(
    '/:orderId/payment',
    async ({ params, body }: any) => {
      const { orderId } = params;
      const { paymentStatus, paymentId } = body;

      const result = await updatePaymentStatus(orderId, paymentStatus, paymentId);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updatePaymentStatusSchema,
      response: orderResponseSchema,
    },
  )
  // อัปเดตข้อมูลการจัดส่ง
  .put(
    '/:orderId/shipping',
    async ({ params, body }: any) => {
      const { orderId } = params;
      const shippingData = {
        ...body,
        estimatedDelivery: body.estimatedDelivery ? new Date(body.estimatedDelivery) : undefined,
      };

      const result = await updateOrderShipping(orderId, shippingData);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updateShippingSchema,
      response: orderResponseSchema,
    },
  )
  // ยกเลิกออเดอร์
  .put(
    '/:orderId/cancel',
    async ({ params, body }: any) => {
      const { orderId } = params;
      const { reason } = body;

      const result = await cancelOrder(orderId, reason);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: cancelOrderSchema,
      response: orderResponseSchema,
    },
  )
  // สถิติออเดอร์
  .get(
    '/stats',
    async ({ query, _user, site }: any) => {
      try {
        const dateRange = {
          start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: query.endDate ? new Date(query.endDate) : new Date(),
        };

        const stats = await getOrderStats(site._id, dateRange);

        return {
          success: true,
          message: 'ดึงสถิติออเดอร์สำเร็จ',
          data: stats,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดึงสถิติออเดอร์:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติออเดอร์',
        };
      }
    },
    {
      query: t.Object({
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
      }),
    },
  );
