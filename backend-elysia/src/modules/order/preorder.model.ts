import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export type PreOrderStatus = 'pending' | 'confirmed' | 'processing' | 'available' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

export interface IPreOrderItem {
  _id: string;
  productId: string;
  variantId?: string;
  name: string;
  sku?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount?: number;
  finalPrice: number;
}

export interface IAddress {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface IPreOrder extends Document {
  _id: string;
  siteId: string;
  preOrderNumber: string;
  customerId: string;
  customerEmail: string;
  customerName: string;

  // Pre-order details
  items: IPreOrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;

  // Status
  status: PreOrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: string;

  // Pre-order specific
  expectedReleaseDate: Date;
  estimatedDeliveryDate?: Date;
  isNotifyOnRelease: boolean;

  // Addresses
  shippingAddress: IAddress;
  billingAddress: IAddress;

  // Payment
  paymentId?: string;
  paidAt?: Date;

  // Notes
  customerNotes?: string;
  adminNotes?: string;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  confirmedAt?: Date;
  availableAt?: Date;
  cancelledAt?: Date;
}

const preOrderItemSchema = new Schema(
  {
    _id: { type: String, default: () => generateFileId(3) },
    productId: { type: String, required: true },
    variantId: { type: String },
    name: { type: String, required: true },
    sku: { type: String },
    quantity: { type: Number, required: true, min: 1 },
    unitPrice: { type: Number, required: true },
    totalPrice: { type: Number, required: true },
    discount: { type: Number, default: 0 },
    finalPrice: { type: Number, required: true },
  },
  { _id: false },
);

const addressSchema = new Schema(
  {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    address: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    postalCode: { type: String, required: true },
    country: { type: String, required: true },
  },
  { _id: false },
);

const preOrderSchema = new Schema<IPreOrder>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    preOrderNumber: { type: String, required: true, unique: true },
    customerId: { type: String, required: true, index: true },
    customerEmail: { type: String, required: true },
    customerName: { type: String, required: true },

    // Pre-order details
    items: [preOrderItemSchema],
    subtotal: { type: Number, required: true },
    tax: { type: Number, default: 0 },
    shipping: { type: Number, default: 0 },
    discount: { type: Number, default: 0 },
    total: { type: Number, required: true },

    // Status
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'processing', 'available', 'cancelled', 'refunded'],
      default: 'pending',
      index: true,
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded'],
      default: 'pending',
      index: true,
    },
    paymentMethod: { type: String, required: true },

    // Pre-order specific
    expectedReleaseDate: { type: Date, required: true, index: true },
    estimatedDeliveryDate: { type: Date },
    isNotifyOnRelease: { type: Boolean, default: true },

    // Addresses
    shippingAddress: { type: addressSchema, required: true },
    billingAddress: { type: addressSchema, required: true },

    // Payment
    paymentId: { type: String },
    paidAt: { type: Date },

    // Notes
    customerNotes: { type: String },
    adminNotes: { type: String },

    // Timestamps
    confirmedAt: { type: Date },
    availableAt: { type: Date },
    cancelledAt: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
preOrderSchema.index({ siteId: 1, preOrderNumber: 1 }, { unique: true });
preOrderSchema.index({ siteId: 1, customerId: 1 });
preOrderSchema.index({ siteId: 1, status: 1 });
preOrderSchema.index({ siteId: 1, paymentStatus: 1 });
preOrderSchema.index({ siteId: 1, expectedReleaseDate: 1 });
preOrderSchema.index({ siteId: 1, createdAt: -1 });

// Methods
preOrderSchema.methods.updateStatus = function(newStatus: PreOrderStatus) {
  this.status = newStatus;

  switch (newStatus) {
    case 'confirmed':
      this.confirmedAt = new Date();
      break;
    case 'available':
      this.availableAt = new Date();
      break;
    case 'cancelled':
      this.cancelledAt = new Date();
      break;
  }

  return this.save();
};

preOrderSchema.methods.updatePaymentStatus = function(newStatus: PaymentStatus) {
  this.paymentStatus = newStatus;

  if (newStatus === 'paid') {
    this.paidAt = new Date();
  }

  return this.save();
};

// Generate pre-order number
preOrderSchema.pre('save', async function(next) {
  if (this.isNew && !this.preOrderNumber) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // Count pre-orders for today
    const todayPreOrders = await mongoose.model('PreOrder').countDocuments({
      siteId: this.siteId,
      createdAt: {
        $gte: new Date(year, date.getMonth(), date.getDate()),
        $lt: new Date(year, date.getMonth(), date.getDate() + 1),
      },
    });

    const preOrderNumber = `PO${year}${month}${day}-${String(todayPreOrders + 1).padStart(4, '0')}`;
    this.preOrderNumber = preOrderNumber;
  }
  next();
});

export const PreOrder = mongoose.model<IPreOrder>('PreOrder', preOrderSchema);
