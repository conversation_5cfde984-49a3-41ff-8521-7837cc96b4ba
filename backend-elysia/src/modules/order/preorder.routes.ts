import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import {
  cancelPreOrderSchema,
  createPreOrderSchema,
  preOrderFilterSchema,
  preOrderListResponseSchema,
  preOrderResponseSchema,
  preOrderStatsResponseSchema,
  updatePaymentStatusSchema,
  updatePreOrderStatusSchema,
  updateReleaseDateSchema,
} from './preorder.schema';
import {
  cancelPreOrder,
  createPreOrder,
  getPreOrderById,
  getPreOrdersByCustomer,
  getPreOrdersBySite,
  getPreOrderStats,
  getReadyForRelease,
  markAsAvailable,
  updatePaymentStatus,
  updatePreOrderStatus,
  updateReleaseDate,
} from './preorder.service';
export const preOrderRoutes = new Elysia({ prefix: '/preorder' })
  .use(userAuthPlugin)
  // สร้างการสั่งจองใหม่
  .post(
    '/create',
    async ({ body, user }: any) => {
      const {
        siteId,
        items,
        expectedReleaseDate,
        estimatedDeliveryDate,
        shippingAddress,
        billingAddress,
        paymentMethod,
        customerNotes,
        adminNotes,
        isNotifyOnRelease,
      } = body;
      const preOrderData = {
        siteId,
        customerId: user._id,
        customerEmail: user.email,
        customerName: user.name || user.email,
        items,
        expectedReleaseDate: new Date(expectedReleaseDate),
        estimatedDeliveryDate: estimatedDeliveryDate ? new Date(estimatedDeliveryDate) : undefined,
        shippingAddress,
        billingAddress,
        paymentMethod,
        customerNotes,
        adminNotes,
        isNotifyOnRelease,
      };

      const result = await createPreOrder(preOrderData);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: createPreOrderSchema,
      response: preOrderResponseSchema,
    },
  )
  // ดึงรายการสั่งจองของไซต์
  .get(
    '/site/:siteId',
    async ({ params, query }: any) => {
      const { siteId } = params;
      const filter: any = {};

      if (query.status) filter.status = query.status;
      if (query.paymentStatus) filter.paymentStatus = query.paymentStatus;
      if (query.startDate) filter.createdAt = { $gte: new Date(query.startDate) };
      if (query.endDate) filter.createdAt = { ...filter.createdAt, $lte: new Date(query.endDate) };

      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 20;
      const skip = (page - 1) * limit;

      const [preOrders, total] = await Promise.all([
        getPreOrdersBySite(siteId, filter).skip(skip).limit(limit),
        getPreOrdersBySite(siteId, filter).countDocuments(),
      ]);

      return {
        success: true,
        message: 'ดึงรายการสั่งจองสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: preOrders,
        total,
        page,
        limit,
      };
    },
    {
      query: preOrderFilterSchema,
      response: preOrderListResponseSchema,
    },
  )
  // ดึงสั่งจองของลูกค้า
  .get(
    '/my-preorders',
    async ({ user, query }: any) => {
      const { siteId } = query;
      if (!siteId) {
        throw new HttpError(400, 'กรุณาระบุ siteId');
      }

      const preOrders = await getPreOrdersByCustomer(user._id, siteId);

      return {
        success: true,
        message: 'ดึงสั่งจองของลูกค้าสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: preOrders,
      };
    },
    {
      response: preOrderResponseSchema,
    },
  )
  // ดึงข้อมูลสั่งจองเฉพาะ
  .get(
    '/:preOrderId',
    async ({ params }: any) => {
      const { preOrderId } = params;
      const preOrder = await getPreOrderById(preOrderId);

      return {
        success: true,
        message: 'ดึงข้อมูลสั่งจองสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: preOrder,
      };
    },
    {
      response: preOrderResponseSchema,
    },
  )
  // อัปเดตสถานะสั่งจอง
  .put(
    '/:preOrderId/status',
    async ({ params, body }: any) => {
      const { preOrderId } = params;
      const { status } = body;

      const result = await updatePreOrderStatus(preOrderId, status);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updatePreOrderStatusSchema,
      response: preOrderResponseSchema,
    },
  )
  // อัปเดตสถานะการชำระเงิน
  .put(
    '/:preOrderId/payment',
    async ({ params, body }: any) => {
      const { preOrderId } = params;
      const { paymentStatus, paymentId } = body;

      const result = await updatePaymentStatus(preOrderId, paymentStatus, paymentId);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updatePaymentStatusSchema,
      response: preOrderResponseSchema,
    },
  )
  // อัปเดตวันที่วางจำหน่าย
  .put(
    '/:preOrderId/release-date',
    async ({ params, body }: any) => {
      const { preOrderId } = params;
      const { expectedReleaseDate, estimatedDeliveryDate } = body;

      const result = await updateReleaseDate(
        preOrderId,
        new Date(expectedReleaseDate),
        estimatedDeliveryDate ? new Date(estimatedDeliveryDate) : undefined,
      );
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updateReleaseDateSchema,
      response: preOrderResponseSchema,
    },
  )
  // ยกเลิกการสั่งจอง
  .put(
    '/:preOrderId/cancel',
    async ({ params, body }: any) => {
      const { preOrderId } = params;
      const { reason } = body;

      const result = await cancelPreOrder(preOrderId, reason);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: cancelPreOrderSchema,
      response: preOrderResponseSchema,
    },
  )
  // ทำเครื่องหมายว่าสินค้าพร้อมแล้ว
  .put(
    '/:preOrderId/available',
    async ({ params }: any) => {
      const { preOrderId } = params;

      const result = await markAsAvailable(preOrderId);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: preOrderResponseSchema,
    },
  )
  // รายการพร้อมวางจำหน่าย
  .get(
    '/ready-for-release/:siteId',
    async ({ params }: any) => {
      const { siteId } = params;
      const readyPreOrders = await getReadyForRelease(siteId);

      return {
        success: true,
        message: 'ดึงรายการพร้อมวางจำหน่ายสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: readyPreOrders,
      };
    },
    {
      response: preOrderResponseSchema,
    },
  )
  // สถิติสั่งจอง
  .get(
    '/stats/:siteId',
    async ({ params }: any) => {
      const { siteId } = params;
      const stats = await getPreOrderStats(siteId);

      return {
        success: true,
        message: 'ดึงสถิติสั่งจองสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: stats,
      };
    },
    {
      response: preOrderStatsResponseSchema,
    },
  );
