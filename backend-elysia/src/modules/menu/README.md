# ระบบเมนูและจัดการหน้าเว็บ (Menu & Page Management System)

ระบบจัดการเมนูและหน้าเว็บสำหรับเว็บไซต์ รองรับการสร้างเมนูแบบ hierarchical และจัดการหน้าเว็บต่างๆ

## คุณสมบัติ

### ระบบเมนู (Menu System)

- ✅ สร้าง/แก้ไข/ลบรายการเมนู
- ✅ รองรับ Submenu แบบหลายระดับ
- ✅ จัดเรียงลำดับเมนู (Drag & Drop)
- ✅ กำหนดตำแหน่งแสดงผล (Header, Footer, Mobile)
- ✅ ควบคุมสิทธิ์การมองเห็น (Public, Customer, Admin)
- ✅ รองรับลิงก์ภายในและภายนอก
- ✅ เชื่อมโยงกับหน้าเว็บ, หมวดหมู่, สินค้า

### ระบบหน้าเว็บ (Page System)

- ✅ สร้าง/แก้ไข/ลบหน้าเว็บ
- ✅ ระบบ Template (Default, Landing, About, Contact, Blog, Custom)
- ✅ SEO Settings (Meta Title, Description, Keywords, OG Tags)
- ✅ Custom CSS/JS สำหรับแต่ละหน้า
- ✅ ระบบ Slug อัตโนมัติ
- ✅ นับจำนวนการเข้าชม
- ✅ สร้าง Sitemap อัตโนมัติ

## API Endpoints

### เมนู (Menu Items)

#### สร้างรายการเมนู

```
POST /v1/menu/items
```

**Body:**

```json
{
  "title": "หน้าแรก",
  "url": "/",
  "type": "internal",
  "order": 1,
  "isActive": true,
  "visibility": "public",
  "showInHeader": true,
  "showInFooter": false,
  "showInMobile": true,
  "icon": "home",
  "description": "หน้าแรกของเว็บไซต์"
}
```

#### ดึงรายการเมนู

```
GET /v1/menu/items?type=internal&isActive=true&showInHeader=true
```

#### ดึงโครงสร้างเมนู

```
GET /v1/menu/tree/header
GET /v1/menu/tree/footer
GET /v1/menu/tree/mobile
```

#### อัพเดทรายการเมนู

```
PATCH /v1/menu/items/:id
```

#### ลบรายการเมนู

```
DELETE /v1/menu/items/:id
```

#### จัดเรียงลำดับเมนู

```
PATCH /v1/menu/items/reorder
```

**Body:**

```json
{
  "items": [
    { "id": "menu1", "order": 1, "parentId": null },
    { "id": "menu2", "order": 2, "parentId": "menu1" }
  ]
}
```

### หน้าเว็บ (Pages)

#### สร้างหน้าเว็บ

```
POST /v1/menu/pages
```

**Body:**

```json
{
  "title": "เกี่ยวกับเรา",
  "slug": "about-us",
  "content": "<h1>เกี่ยวกับเรา</h1><p>เนื้อหาเกี่ยวกับบริษัท</p>",
  "template": "about",
  "metaTitle": "เกี่ยวกับเรา - บริษัทของเรา",
  "metaDescription": "ข้อมูลเกี่ยวกับบริษัทและทีมงาน",
  "isActive": true,
  "isPublic": true,
  "visibility": "public",
  "showInMenu": true,
  "showInSitemap": true,
  "seoSettings": {
    "title": "เกี่ยวกับเรา",
    "description": "ข้อมูลเกี่ยวกับบริษัท",
    "keywords": "เกี่ยวกับเรา, บริษัท, ทีมงาน",
    "ogImage": "/images/about-og.jpg"
  }
}
```

#### ดึงรายการหน้าเว็บ

```
GET /v1/menu/pages?isActive=true&template=about&search=เกี่ยวกับ
```

#### ดึงหน้าเว็บตาม ID

```
GET /v1/menu/pages/:id
```

#### ดึงหน้าเว็บตาม Slug (สำหรับแสดงผล)

```
GET /v1/menu/pages/slug/:slug
```

#### อัพเดทหน้าเว็บ

```
PATCH /v1/menu/pages/:id
```

#### ลบหน้าเว็บ

```
DELETE /v1/menu/pages/:id
```

#### สร้าง Sitemap

```
GET /v1/menu/sitemap
```

## ประเภทเมนู (Menu Types)

- **internal**: ลิงก์ภายในเว็บไซต์
- **external**: ลิงก์ไปเว็บไซต์อื่น
- **page**: เชื่อมโยงกับหน้าเว็บ
- **category**: เชื่อมโยงกับหมวดหมู่สินค้า
- **product**: เชื่อมโยงกับสินค้า

## Template หน้าเว็บ

- **default**: หน้าเว็บทั่วไป
- **landing**: หน้า Landing Page
- **about**: หน้าเกี่ยวกับเรา
- **contact**: หน้าติดต่อ
- **blog**: หน้าบล็อก
- **custom**: หน้าแบบกำหนดเอง

## ระดับการมองเห็น (Visibility Levels)

- **public**: ทุกคนเห็นได้
- **customer**: เฉพาะสมาชิกเท่านั้น
- **admin**: เฉพาะผู้ดูแลระบบ

## ตัวอย่างการใช้งาน

### 1. สร้างเมนูหลักและ Submenu

```bash
# สร้างเมนูหลัก "สินค้า"
curl -X POST http://localhost:5000/v1/menu/items \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "สินค้า",
    "url": "/products",
    "type": "internal",
    "order": 1,
    "showInHeader": true
  }'

# สร้าง Submenu "เสื้อผ้า"
curl -X POST http://localhost:5000/v1/menu/items \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "เสื้อผ้า",
    "url": "/products/clothing",
    "type": "category",
    "parentId": "parent_menu_id",
    "order": 1,
    "showInHeader": true
  }'
```

### 2. สร้างหน้าเว็บ "เกี่ยวกับเรา"

```bash
curl -X POST http://localhost:5000/v1/menu/pages \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "เกี่ยวกับเรา",
    "slug": "about-us",
    "content": "<h1>เกี่ยวกับเรา</h1><p>เราเป็นบริษัทที่มีประสบการณ์มากกว่า 10 ปี</p>",
    "template": "about",
    "metaTitle": "เกี่ยวกับเรา - บริษัทของเรา",
    "metaDescription": "ข้อมูลเกี่ยวกับบริษัทและประวัติความเป็นมา",
    "showInMenu": true,
    "showInSitemap": true
  }'
```

### 3. ดึงโครงสร้างเมนูสำหรับ Header

```bash
curl -X GET http://localhost:5000/v1/menu/tree/header \
  -H "Authorization: Bearer <token>"
```

### 4. ดึงหน้าเว็บสำหรับแสดงผล

```bash
curl -X GET http://localhost:5000/v1/menu/pages/slug/about-us \
  -H "Authorization: Bearer <token>"
```

## Response Format

### สำเร็จ

```json
{
  "success": true,
  "message": "ดำเนินการสำเร็จ",
  "data": { ... }
}
```

### ผิดพลาด

```json
{
  "success": false,
  "message": "ข้อความข้อผิดพลาด"
}
```

## การทดสอบ

รันการทดสอบด้วยคำสั่ง:

```bash
bun test src/modules/menu/menu.service.test.ts
```

## Database Schema

### MenuItem Collection

```typescript
{
  _id: string,
  siteId: string,
  title: string,
  url: string,
  type: 'internal' | 'external' | 'page' | 'category' | 'product',
  pageId?: string,
  categoryId?: string,
  productId?: string,
  order: number,
  isActive: boolean,
  parentId?: string,
  icon?: string,
  description?: string,
  target: '_self' | '_blank',
  cssClass?: string,
  permissions?: string[],
  visibility: 'public' | 'customer' | 'admin',
  showInHeader: boolean,
  showInFooter: boolean,
  showInMobile: boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### Page Collection

```typescript
{
  _id: string,
  siteId: string,
  title: string,
  slug: string,
  content: string,
  excerpt?: string,
  metaTitle?: string,
  metaDescription?: string,
  metaKeywords?: string,
  isActive: boolean,
  isPublic: boolean,
  template: 'default' | 'landing' | 'about' | 'contact' | 'blog' | 'custom',
  customCSS?: string,
  customJS?: string,
  featuredImage?: string,
  author?: string,
  publishedAt?: Date,
  seoSettings?: {
    title?: string,
    description?: string,
    keywords?: string,
    ogImage?: string,
    ogTitle?: string,
    ogDescription?: string,
    canonicalUrl?: string,
    robots?: string,
    structuredData?: string
  },
  visibility: 'public' | 'customer' | 'admin',
  permissions?: string[],
  parentId?: string,
  order: number,
  showInMenu: boolean,
  showInSitemap: boolean,
  allowComments: boolean,
  viewCount: number,
  createdAt: Date,
  updatedAt: Date
}
```

## หมายเหตุ

- เฉพาะ Admin เท่านั้นที่สามารถจัดการเมนูและหน้าเว็บได้
- ระบบจะสร้าง Slug อัตโนมัติจากชื่อหน้าหากไม่ระบุ
- การลบเมนูหรือหน้าเว็บจะตรวจสอบ dependencies ก่อน
- ระบบรองรับ SEO และ Open Graph Tags
- มีการนับจำนวนการเข้าชมหน้าเว็บอัตโนมัติ
