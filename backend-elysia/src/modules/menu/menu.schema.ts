import { t } from 'elysia';

// Menu Item Schemas
export const createMenuItemSchema = t.Object({
  siteId: t.String({ minLength: 1, error: 'siteId ต้องไม่ว่าง' }),
  title: t.String({ minLength: 1, maxLength: 100, error: 'title ต้องไม่ว่างและต้องมีความยาวตั้งแต่ 1-100 ตัวอักษร' }),
  url: t.String({ minLength: 1, error: 'url ต้องไม่ว่าง' }),
  type: t.Union(
    [t.Literal('internal'), t.Literal('external'), t.Literal('page'), t.Literal('category'), t.Literal('product')],
    { error: 'type ต้องเป็น internal, external, page, category หรือ product' },
  ),
  pageId: t.Optional(t.String({ error: 'pageId ต้องเป็น string' })),
  categoryId: t.Optional(t.String({ error: 'categoryId ต้องเป็น string' })),
  productId: t.Optional(t.String({ error: 'productId ต้องเป็น string' })),
  order: t.Optional(t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0' })),
  isActive: t.Optional(t.<PERSON>({ error: 'isActive ต้องเป็น boolean' })),
  parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
  icon: t.Optional(t.String({ error: 'icon ต้องเป็น string' })),
  description: t.Optional(t.String({ error: 'description ต้องเป็น string' })),
  target: t.Optional(
    t.Union([t.Literal('_self'), t.Literal('_blank')], { error: 'target ต้องเป็น _self หรือ _blank' }),
  ),
  cssClass: t.Optional(t.String({ error: 'cssClass ต้องเป็น string' })),
  permissions: t.Optional(t.Array(t.String({ error: 'permissions ต้องเป็น array ของ string' })), {
    error: 'permissions ต้องเป็น array ของ string',
  }),
  visibility: t.Optional(
    t.Union([t.Literal('public'), t.Literal('customer'), t.Literal('admin')], {
      error: 'visibility ต้องเป็น public, customer หรือ admin',
    }),
  ),
  showInHeader: t.Optional(t.Boolean({ error: 'showInHeader ต้องเป็น boolean' })),
  showInFooter: t.Optional(t.Boolean({ error: 'showInFooter ต้องเป็น boolean' })),
  showInMobile: t.Optional(t.Boolean({ error: 'showInMobile ต้องเป็น boolean' })),
});

export const updateMenuItemSchema = t.Object({
  title: t.Optional(
    t.String({ minLength: 1, maxLength: 100, error: 'title ต้องไม่ว่างและต้องมีความยาวตั้งแต่ 1-100 ตัวอักษร' }),
  ),
  url: t.Optional(t.String({ minLength: 1, error: 'url ต้องไม่ว่าง' })),
  type: t.Optional(
    t.Union(
      [t.Literal('internal'), t.Literal('external'), t.Literal('page'), t.Literal('category'), t.Literal('product')],
      { error: 'type ต้องเป็น internal, external, page, category หรือ product' },
    ),
  ),
  pageId: t.Optional(t.String({ error: 'pageId ต้องเป็น string' })),
  categoryId: t.Optional(t.String({ error: 'categoryId ต้องเป็น string' })),
  productId: t.Optional(t.String({ error: 'productId ต้องเป็น string' })),
  order: t.Optional(t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0' })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น boolean' })),
  parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
  icon: t.Optional(t.String({ error: 'icon ต้องเป็น string' })),
  description: t.Optional(t.String({ error: 'description ต้องเป็น string' })),
  target: t.Optional(
    t.Union([t.Literal('_self'), t.Literal('_blank')], { error: 'target ต้องเป็น _self หรือ _blank' }),
  ),
  cssClass: t.Optional(t.String({ error: 'cssClass ต้องเป็น string' })),
  permissions: t.Optional(t.Array(t.String({ error: 'permissions ต้องเป็น array ของ string' })), {
    error: 'permissions ต้องเป็น array ของ string',
  }),
  visibility: t.Optional(
    t.Union([t.Literal('public'), t.Literal('customer'), t.Literal('admin')], {
      error: 'visibility ต้องเป็น public, customer หรือ admin',
    }),
  ),
  showInHeader: t.Optional(t.Boolean({ error: 'showInHeader ต้องเป็น boolean' })),
  showInFooter: t.Optional(t.Boolean({ error: 'showInFooter ต้องเป็น boolean' })),
  showInMobile: t.Optional(t.Boolean({ error: 'showInMobile ต้องเป็น boolean' })),
});

export const menuQuerySchema = t.Object({
  siteId: t.Optional(t.String({ error: 'siteId ต้องเป็น string' })),
  type: t.Optional(
    t.Union(
      [t.Literal('internal'), t.Literal('external'), t.Literal('page'), t.Literal('category'), t.Literal('product')],
      { error: 'type ต้องเป็น internal, external, page, category หรือ product' },
    ),
  ),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น boolean' })),
  parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
  visibility: t.Optional(
    t.Union([t.Literal('public'), t.Literal('customer'), t.Literal('admin')], {
      error: 'visibility ต้องเป็น public, customer หรือ admin',
    }),
  ),
  showInHeader: t.Optional(t.Boolean({ error: 'showInHeader ต้องเป็น boolean' })),
  showInFooter: t.Optional(t.Boolean({ error: 'showInFooter ต้องเป็น boolean' })),
  showInMobile: t.Optional(t.Boolean({ error: 'showInMobile ต้องเป็น boolean' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1' })),
  limit: t.Optional(
    t.Number({
      minimum: 1,
      maximum: 100,
      error: 'limit ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1 และน้อยกว่าหรือเท่ากับ 100',
    }),
  ),
});

// Page Schemas
export const createPageSchema = t.Object({
  siteId: t.String({ minLength: 1, error: 'siteId ต้องไม่ว่าง' }),
  title: t.String({ minLength: 1, maxLength: 200, error: 'title ต้องไม่ว่างและต้องมีความยาวตั้งแต่ 1-200 ตัวอักษร' }),
  slug: t.Optional(
    t.String({ minLength: 1, maxLength: 200, error: 'slug ต้องเป็น string ที่มีความยาวตั้งแต่ 1-200 ตัวอักษร' }),
  ),
  content: t.String({ error: 'content ต้องเป็น string' }),
  excerpt: t.Optional(t.String({ error: 'excerpt ต้องเป็น string' })),
  metaTitle: t.Optional(
    t.String({ maxLength: 60, error: 'metaTitle ต้องเป็น string ที่มีความยาวไม่เกิน 60 ตัวอักษร' }),
  ),
  metaDescription: t.Optional(
    t.String({ maxLength: 160, error: 'metaDescription ต้องเป็น string ที่มีความยาวไม่เกิน 160 ตัวอักษร' }),
  ),
  metaKeywords: t.Optional(t.String({ error: 'metaKeywords ต้องเป็น string' })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น boolean' })),
  isPublic: t.Optional(t.Boolean({ error: 'isPublic ต้องเป็น boolean' })),
  template: t.Optional(
    t.Union(
      [
        t.Literal('default'),
        t.Literal('landing'),
        t.Literal('about'),
        t.Literal('contact'),
        t.Literal('blog'),
        t.Literal('custom'),
      ],
      { error: 'template ต้องเป็น default, landing, about, contact, blog หรือ custom' },
    ),
  ),
  customCSS: t.Optional(t.String({ error: 'customCSS ต้องเป็น string' })),
  customJS: t.Optional(t.String({ error: 'customJS ต้องเป็น string' })),
  featuredImage: t.Optional(t.String({ error: 'featuredImage ต้องเป็น string' })),
  author: t.Optional(t.String({ error: 'author ต้องเป็น string' })),
  publishedAt: t.Optional(t.Date({ error: 'publishedAt ต้องเป็น Date' })),
  seoSettings: t.Optional(
    t.Object({
      title: t.Optional(t.String({ error: 'seoSettings.title ต้องเป็น string' })),
      description: t.Optional(t.String({ error: 'seoSettings.description ต้องเป็น string' })),
      keywords: t.Optional(t.String({ error: 'seoSettings.keywords ต้องเป็น string' })),
      ogImage: t.Optional(t.String({ error: 'seoSettings.ogImage ต้องเป็น string' })),
      ogTitle: t.Optional(t.String({ error: 'seoSettings.ogTitle ต้องเป็น string' })),
      ogDescription: t.Optional(t.String({ error: 'seoSettings.ogDescription ต้องเป็น string' })),
      canonicalUrl: t.Optional(t.String({ error: 'seoSettings.canonicalUrl ต้องเป็น string' })),
      robots: t.Optional(t.String({ error: 'seoSettings.robots ต้องเป็น string' })),
      structuredData: t.Optional(t.String({ error: 'seoSettings.structuredData ต้องเป็น string' })),
    }),
  ),
  visibility: t.Optional(
    t.Union([t.Literal('public'), t.Literal('customer'), t.Literal('admin')], {
      error: 'visibility ต้องเป็น public, customer หรือ admin',
    }),
  ),
  permissions: t.Optional(t.Array(t.String({ error: 'permissions ต้องเป็น array ของ string' })), {
    error: 'permissions ต้องเป็น array ของ string',
  }),
  parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
  order: t.Optional(t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0' })),
  showInMenu: t.Optional(t.Boolean({ error: 'showInMenu ต้องเป็น boolean' })),
  showInSitemap: t.Optional(t.Boolean({ error: 'showInSitemap ต้องเป็น boolean' })),
  allowComments: t.Optional(t.Boolean({ error: 'allowComments ต้องเป็น boolean' })),
});

export const updatePageSchema = t.Object({
  title: t.Optional(
    t.String({ minLength: 1, maxLength: 200, error: 'title ต้องไม่ว่างและต้องมีความยาวตั้งแต่ 1-200 ตัวอักษร' }),
  ),
  slug: t.Optional(
    t.String({ minLength: 1, maxLength: 200, error: 'slug ต้องเป็น string ที่มีความยาวตั้งแต่ 1-200 ตัวอักษร' }),
  ),
  content: t.Optional(t.String({ error: 'content ต้องเป็น string' })),
  excerpt: t.Optional(t.String({ error: 'excerpt ต้องเป็น string' })),
  metaTitle: t.Optional(
    t.String({ maxLength: 60, error: 'metaTitle ต้องเป็น string ที่มีความยาวไม่เกิน 60 ตัวอักษร' }),
  ),
  metaDescription: t.Optional(
    t.String({ maxLength: 160, error: 'metaDescription ต้องเป็น string ที่มีความยาวไม่เกิน 160 ตัวอักษร' }),
  ),
  metaKeywords: t.Optional(t.String({ error: 'metaKeywords ต้องเป็น string' })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น boolean' })),
  isPublic: t.Optional(t.Boolean({ error: 'isPublic ต้องเป็น boolean' })),
  template: t.Optional(
    t.Union(
      [
        t.Literal('default'),
        t.Literal('landing'),
        t.Literal('about'),
        t.Literal('contact'),
        t.Literal('blog'),
        t.Literal('custom'),
      ],
      { error: 'template ต้องเป็น default, landing, about, contact, blog หรือ custom' },
    ),
  ),
  customCSS: t.Optional(t.String({ error: 'customCSS ต้องเป็น string' })),
  customJS: t.Optional(t.String({ error: 'customJS ต้องเป็น string' })),
  featuredImage: t.Optional(t.String({ error: 'featuredImage ต้องเป็น string' })),
  author: t.Optional(t.String({ error: 'author ต้องเป็น string' })),
  publishedAt: t.Optional(t.Date({ error: 'publishedAt ต้องเป็น Date' })),
  seoSettings: t.Optional(
    t.Object({
      title: t.Optional(t.String({ error: 'seoSettings.title ต้องเป็น string' })),
      description: t.Optional(t.String({ error: 'seoSettings.description ต้องเป็น string' })),
      keywords: t.Optional(t.String({ error: 'seoSettings.keywords ต้องเป็น string' })),
      ogImage: t.Optional(t.String({ error: 'seoSettings.ogImage ต้องเป็น string' })),
      ogTitle: t.Optional(t.String({ error: 'seoSettings.ogTitle ต้องเป็น string' })),
      ogDescription: t.Optional(t.String({ error: 'seoSettings.ogDescription ต้องเป็น string' })),
      canonicalUrl: t.Optional(t.String({ error: 'seoSettings.canonicalUrl ต้องเป็น string' })),
      robots: t.Optional(t.String({ error: 'seoSettings.robots ต้องเป็น string' })),
      structuredData: t.Optional(t.String({ error: 'seoSettings.structuredData ต้องเป็น string' })),
    }),
  ),
  visibility: t.Optional(
    t.Union([t.Literal('public'), t.Literal('customer'), t.Literal('admin')], {
      error: 'visibility ต้องเป็น public, customer หรือ admin',
    }),
  ),
  permissions: t.Optional(t.Array(t.String({ error: 'permissions ต้องเป็น array ของ string' })), {
    error: 'permissions ต้องเป็น array ของ string',
  }),
  parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
  order: t.Optional(t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0' })),
  showInMenu: t.Optional(t.Boolean({ error: 'showInMenu ต้องเป็น boolean' })),
  showInSitemap: t.Optional(t.Boolean({ error: 'showInSitemap ต้องเป็น boolean' })),
  allowComments: t.Optional(t.Boolean({ error: 'allowComments ต้องเป็น boolean' })),
});

export const pageQuerySchema = t.Object({
  siteId: t.Optional(t.String({ error: 'siteId ต้องเป็น string' })),
  isActive: t.Optional(t.Boolean({ error: 'isActive ต้องเป็น boolean' })),
  isPublic: t.Optional(t.Boolean({ error: 'isPublic ต้องเป็น boolean' })),
  template: t.Optional(
    t.Union(
      [
        t.Literal('default'),
        t.Literal('landing'),
        t.Literal('about'),
        t.Literal('contact'),
        t.Literal('blog'),
        t.Literal('custom'),
      ],
      { error: 'template ต้องเป็น default, landing, about, contact, blog หรือ custom' },
    ),
  ),
  visibility: t.Optional(
    t.Union([t.Literal('public'), t.Literal('customer'), t.Literal('admin')], {
      error: 'visibility ต้องเป็น public, customer หรือ admin',
    }),
  ),
  parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
  showInMenu: t.Optional(t.Boolean({ error: 'showInMenu ต้องเป็น boolean' })),
  showInSitemap: t.Optional(t.Boolean({ error: 'showInSitemap ต้องเป็น boolean' })),
  search: t.Optional(t.String({ error: 'search ต้องเป็น string' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1' })),
  limit: t.Optional(
    t.Number({
      minimum: 1,
      maximum: 100,
      error: 'limit ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1 และน้อยกว่าหรือเท่ากับ 100',
    }),
  ),
});

export const reorderItemsSchema = t.Object({
  items: t.Array(
    t.Object({
      id: t.String({ error: 'id ต้องเป็น string' }),
      order: t.Number({ minimum: 0, error: 'order ต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0' }),
      parentId: t.Optional(t.String({ error: 'parentId ต้องเป็น string' })),
    }),
    { error: 'items ต้องเป็น array ของ object ที่มี id, order และ parentId' },
  ),
});
