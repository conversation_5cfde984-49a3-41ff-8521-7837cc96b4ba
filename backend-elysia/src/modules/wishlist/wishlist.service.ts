import { HttpError } from '@/core/utils/error';
import { Product } from '@/modules/product/product.model';
import { Wishlist, WishlistShare } from './wishlist.model';

// Wishlist Service
export async function createWishlist(siteId: string, userId: string, wishlistData: any) {
  try {
    const wishlist = await Wishlist.create({
      siteId,
      userId,
      userType: 'user', // หรือดึงจาก user data
      ...wishlistData,
    });

    return wishlist;
  }
  catch (err: any) {
    console.error('Error in createWishlist:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง wishlist');
  }
}

export async function getUserWishlists(siteId: string, userId: string) {
  try {
    const wishlists = await (Wishlist as any).findByUser(siteId, userId);
    return wishlists;
  }
  catch (err: any) {
    console.error('Error in getUserWishlists:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง wishlist');
  }
}

export async function getWishlistById(siteId: string, wishlistId: string, userId: string) {
  try {
    const wishlist = await Wishlist.findOne({
      siteId,
      _id: wishlistId,
      userId,
    });

    if (!wishlist) {
      throw new HttpError(404, 'ไม่พบ wishlist');
    }

    return wishlist;
  }
  catch (err: any) {
    console.error('Error in getWishlistById:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง wishlist');
  }
}

export async function addItemToWishlist(siteId: string, wishlistId: string, itemData: any) {
  try {
    // ตรวจสอบว่าสินค้ามีอยู่จริง
    const product = await Product.findOne({ siteId, _id: itemData.productId });
    if (!product) {
      throw new HttpError(404, 'ไม่พบสินค้า');
    }

    const wishlist = await (Wishlist as any).addItem(siteId, wishlistId, itemData.productId, itemData);

    if (!wishlist) {
      throw new HttpError(404, 'ไม่พบ wishlist');
    }

    return wishlist;
  }
  catch (err: any) {
    console.error('Error in addItemToWishlist:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะเพิ่มสินค้า');
  }
}

export async function removeItemFromWishlist(siteId: string, wishlistId: string, productId: string) {
  try {
    const wishlist = await (Wishlist as any).removeItem(siteId, wishlistId, productId);

    if (!wishlist) {
      throw new HttpError(404, 'ไม่พบ wishlist');
    }

    return wishlist;
  }
  catch (err: any) {
    console.error('Error in removeItemFromWishlist:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบสินค้า');
  }
}

export async function updateWishlist(siteId: string, wishlistId: string, userId: string, updates: any) {
  try {
    const wishlist = await Wishlist.findOneAndUpdate(
      { siteId, _id: wishlistId, userId },
      { $set: updates },
      { new: true },
    );

    if (!wishlist) {
      throw new HttpError(404, 'ไม่พบ wishlist');
    }

    return wishlist;
  }
  catch (err: any) {
    console.error('Error in updateWishlist:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต wishlist');
  }
}

export async function deleteWishlist(siteId: string, wishlistId: string, userId: string) {
  try {
    const wishlist = await Wishlist.findOneAndDelete({
      siteId,
      _id: wishlistId,
      userId,
    });

    if (!wishlist) {
      throw new HttpError(404, 'ไม่พบ wishlist');
    }

    return { message: 'ลบ wishlist สำเร็จ' };
  }
  catch (err: any) {
    console.error('Error in deleteWishlist:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ wishlist');
  }
}

export async function shareWishlist(siteId: string, wishlistId: string, shareData: any) {
  try {
    const share = await WishlistShare.create({
      siteId,
      wishlistId,
      sharedBy: shareData.sharedBy,
      sharedWith: shareData.sharedWith,
      permissions: shareData.permissions || 'view',
      expiresAt: shareData.expiresAt,
    });

    return share;
  }
  catch (err: any) {
    console.error('Error in shareWishlist:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะแชร์ wishlist');
  }
}

export async function getSharedWishlists(siteId: string, userId: string) {
  try {
    const shares = await (WishlistShare as any).findSharedWith(siteId, userId);
    return shares;
  }
  catch (err: any) {
    console.error('Error in getSharedWishlists:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง wishlist ที่แชร์');
  }
}

export async function getWishlistRecommendations(siteId: string, userId: string) {
  try {
    // ดึง wishlist ของผู้ใช้
    const userWishlists = await (Wishlist as any).findByUser(siteId, userId);

    // ดึงสินค้าที่อยู่ใน wishlist
    const wishlistProductIds = userWishlists.flatMap((wishlist: any) =>
      wishlist.items.map((item: any) => item.productId)
    );

    // หาสินค้าที่คล้ายกัน
    const recommendations = await Product.find({
      siteId,
      _id: { $nin: wishlistProductIds },
    })
      .limit(10)
      .sort({ createdAt: -1 });

    return recommendations;
  }
  catch (err: any) {
    console.error('Error in getWishlistRecommendations:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงคำแนะนำ');
  }
}

export async function checkPriceAlerts(siteId: string, userId: string) {
  try {
    const wishlists = await (Wishlist as any).findByUser(siteId, userId);
    const alerts = [];

    for (const wishlist of wishlists) {
      for (const item of wishlist.items) {
        const product = await Product.findOne({ siteId, _id: item.productId });

        if (product && item.priceWhenAdded && product.price < item.priceWhenAdded) {
          alerts.push({
            wishlistId: wishlist._id,
            productId: item.productId,
            productName: product.name,
            oldPrice: item.priceWhenAdded,
            newPrice: product.price,
            discount: item.priceWhenAdded - product.price,
          });
        }
      }
    }

    return alerts;
  }
  catch (err: any) {
    console.error('Error in checkPriceAlerts:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะตรวจสอบการแจ้งเตือนราคา');
  }
}

export async function getWishlistStats(siteId: string, userId: string) {
  try {
    const wishlists = await (Wishlist as any).findByUser(siteId, userId);

    const stats = {
      totalWishlists: wishlists.length,
      totalItems: 0,
      totalValue: 0,
      averageItemsPerWishlist: 0,
    };

    for (const wishlist of wishlists) {
      stats.totalItems += wishlist.items.length;
      stats.totalValue += wishlist.stats.totalValue || 0;
    }

    if (wishlists.length > 0) {
      stats.averageItemsPerWishlist = stats.totalItems / wishlists.length;
    }

    return stats;
  }
  catch (err: any) {
    console.error('Error in getWishlistStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ wishlist');
  }
}
