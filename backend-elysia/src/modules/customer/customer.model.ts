import { generateFileId } from '@/core/utils/idGenerator';
import { model, Schema } from 'mongoose';
export interface ICustomerBase {
  firstName: string;
  lastName: string;
  siteId: string;
  email: string;
  password: string;
  role: 'customer';
  phone?: string;
  avatar: string;
  cover: string;
  refreshToken?: string;
  resetPasswordToken?: string;
  isEmailVerified: boolean;
  profilePicture?: string;
  moneyPoint: number;
  goldPoint: number;
}

export interface ICustomer extends ICustomerBase {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const customerSchemaMongoose = new Schema<ICustomer>(
  {
    _id: {
      type: String,
      required: true,
      trim: true,
      default: () => generateFileId(5),
    },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    siteId: { type: String, required: true, ref: 'Site' },
    email: { type: String, required: true },
    password: { type: String, required: true, minlength: 6 },
    phone: { type: String, required: false },
    avatar: { type: String, default: 'cutomeravatar' },
    cover: { type: String, default: 'customercover' },
    refreshToken: { type: String, select: false },
    resetPasswordToken: { type: String, select: false },
    isEmailVerified: { type: Boolean, default: false },
    profilePicture: { type: String },
    role: { type: String, default: 'customer', enum: ['customer'] },
    moneyPoint: { type: Number, default: 0 },
    goldPoint: { type: Number, default: 0 },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

customerSchemaMongoose.index({ siteId: 1, email: 1 }, { unique: true });

customerSchemaMongoose.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  try {
    const hash = await Bun.password.hash(this.password, {
      algorithm: 'argon2id',
      memoryCost: 65536,
      timeCost: 3,
    });
    this.password = hash;
    next();
  }
  catch (error: unknown) {
    if (error instanceof Error) {
      next(error);
    }
    else {
      console.error('Unexpected error type during password hashing:', error);
      next(new Error(String(error)));
    }
  }
});

customerSchemaMongoose.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return Bun.password.verify(candidatePassword, this.password);
};

export const Customer = model<ICustomer>('Customer', customerSchemaMongoose);
