import { createResetPasswordEmail, createVerificationEmail, sendEmail } from '@/core/config/email';
import { config } from '@/core/config/environment';
import { cloudinaryUpload } from '@/core/utils/cloudinary-upload';
import { HttpError } from '@/core/utils/error';
import { generateFileId } from '@/core/utils/idGenerator';
import * as jose from 'jose';
import { Customer } from './customer.model';

const defaultDeps = {
  Customer: Customer,
  sendEmail,
  createVerificationEmail,
  createResetPasswordEmail,
};

export type SignupCustomerInput = { email: string; password: string; siteId: string; };
export type SigninCustomerInput = { email: string; password: string; siteId: string; };

export async function signupCustomer({ email, password, siteId }: SignupCustomerInput, deps: any = defaultDeps) {
  const exist = await deps.Customer.findOne({ siteId, email });
  if (exist) throw new HttpError(409, 'อีเมลนี้ถูกใช้งานในเว็บนี้แล้ว');
  const customer = await deps.Customer.create({
    _id: generateFileId(5),
    siteId,
    email,
    password,
    isEmailVerified: false,
  });

  // สร้าง access token (อายุสั้น)
  const token = await new jose.SignJWT({ customerId: customer._id, email, siteId })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('24h')
    .sign(new TextEncoder().encode(config.jwtSecret));

  // สร้าง refresh token (อายุยาว)
  const refreshToken = await new jose.SignJWT({ customerId: customer._id, email, siteId })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime(config.refreshTokenExpiresIn)
    .sign(new TextEncoder().encode(config.refreshTokenSecret));

  // บันทึก refresh token ลง database
  customer.refreshToken = refreshToken;
  await customer.save();

  // สร้าง customer object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeCustomer = {
    _id: customer._id,
    email: customer.email,
    firstName: customer.firstName,
    lastName: customer.lastName,
    phone: customer.phone,
    avatar: customer.avatar,
    cover: customer.cover,
    isEmailVerified: customer.isEmailVerified,
    siteId: customer.siteId,
    moneyPoint: customer.moneyPoint,
    goldPoint: customer.goldPoint,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt,
  };

  const link = `${config.frontendUrl}/verify-email?token=${token}`;
  await deps.sendEmail(email, 'ยืนยันอีเมล', deps.createVerificationEmail(link));
  return { customer: safeCustomer, token, refreshToken };
}

export async function signinCustomer({ email, password, siteId }: SigninCustomerInput, deps = defaultDeps) {
  const customer = await deps.Customer.findOne({ siteId, email });
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');
  const valid = await customer.comparePassword(password);
  if (!valid) throw new HttpError(401, 'รหัสผ่านไม่ถูกต้อง');
  if (!customer.isEmailVerified) throw new HttpError(401, 'กรุณายืนยันอีเมลก่อนเข้าสู่ระบบ');

  // สร้าง access token (อายุสั้น)
  const token = await new jose.SignJWT({ customerId: customer._id, email, siteId })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime(config.jwtExpiresIn)
    .sign(new TextEncoder().encode(config.jwtSecret));

  // สร้าง refresh token (อายุยาว)
  const refreshToken = await new jose.SignJWT({ customerId: customer._id, email, siteId })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime(config.refreshTokenExpiresIn)
    .sign(new TextEncoder().encode(config.refreshTokenSecret));

  // บันทึก refresh token ลง database
  customer.refreshToken = refreshToken;
  await customer.save();

  // สร้าง customer object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeCustomer = {
    _id: customer._id,
    email: customer.email,
    firstName: customer.firstName,
    lastName: customer.lastName,
    phone: customer.phone,
    avatar: customer.avatar,
    cover: customer.cover,
    isEmailVerified: customer.isEmailVerified,
    siteId: customer.siteId,
    moneyPoint: customer.moneyPoint,
    goldPoint: customer.goldPoint,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt,
  };

  return { customer: safeCustomer, token, refreshToken };
}

export async function signoutCustomer(customerId: string, deps: any = defaultDeps) {
  // ลบ refresh token ออกจาก database
  await deps.Customer.findByIdAndUpdate(customerId, { $unset: { refreshToken: 1 } });

  // ส่งผลลัพธ์สำเร็จกลับไปเสมอ
  return { success: true, message: 'ออกจากระบบสำเร็จ', statusMessage: 'สำเร็จ!', timestamp: new Date().toISOString() };
}

export async function verifyEmailCustomer(token: string, deps: any = defaultDeps) {
  const { payload } = await jose.jwtVerify(token, new TextEncoder().encode(config.jwtSecret));
  const customer = await deps.Customer.findById(payload.customerId);
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');
  customer.isEmailVerified = true;
  await customer.save();
  return {
    success: true,
    message: 'ยืนยันอีเมลสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: { customer },
  };
}

export async function forgotPasswordCustomer(email: string, deps: any = defaultDeps) {
  const customer = await deps.Customer.findOne({ email });
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');
  const token = await new jose.SignJWT({ customerId: customer._id, email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('1h')
    .sign(new TextEncoder().encode(config.jwtSecret));
  customer.resetPasswordToken = token;
  await customer.save();
  const link = `${config.frontendUrl}/reset-password?token=${token}`;
  await deps.sendEmail(email, 'รีเซ็ตรหัสผ่าน', deps.createResetPasswordEmail(link));
  return {
    success: true,
    message: 'ส่งรหัสผ่านไปยังอีเมลสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: { customer },
  };
}

export async function resetPasswordCustomer(token: string, newPassword: string, deps: any = defaultDeps) {
  const { payload } = await jose.jwtVerify(token, new TextEncoder().encode(config.jwtSecret));
  const customer = await deps.Customer.findById(payload.customerId);
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');
  if (customer.resetPasswordToken !== token) throw new HttpError(400, 'โทเค็นไม่ถูกต้อง');
  customer.password = newPassword;
  customer.resetPasswordToken = undefined;
  await customer.save();
  return {
    success: true,
    message: 'รีเซ็ตรหัสผ่านสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: { customer },
  };
}

export async function refreshTokenCustomer(refreshToken: string, deps: any = defaultDeps) {
  try {
    // ตรวจสอบ refresh token
    const { payload } = await jose.jwtVerify(refreshToken, new TextEncoder().encode(config.refreshTokenSecret));

    const customer = await deps.Customer.findById(payload.customerId).select('+refreshToken');
    if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');

    // ตรวจสอบว่า refresh token ตรงกับที่เก็บใน database
    if (customer.refreshToken !== refreshToken) {
      throw new HttpError(401, 'Refresh token ไม่ถูกต้อง');
    }

    // สร้าง access token ใหม่
    const newToken = await new jose.SignJWT({
      customerId: customer._id,
      email: customer.email,
      siteId: customer.siteId,
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setExpirationTime(config.jwtExpiresIn)
      .sign(new TextEncoder().encode(config.jwtSecret));

    // สร้าง refresh token ใหม่
    const newRefreshToken = await new jose.SignJWT({
      customerId: customer._id,
      email: customer.email,
      siteId: customer.siteId,
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setExpirationTime(config.refreshTokenExpiresIn)
      .sign(new TextEncoder().encode(config.refreshTokenSecret));

    // บันทึก refresh token ใหม่ลง database
    customer.refreshToken = newRefreshToken;
    await customer.save();

    // สร้าง customer object ที่ปลอดภัย (ไม่รวม password, refreshToken)
    const safeCustomer = {
      _id: customer._id,
      email: customer.email,
      firstName: customer.firstName,
      lastName: customer.lastName,
      phone: customer.phone,
      avatar: customer.avatar,
      cover: customer.cover,
      isEmailVerified: customer.isEmailVerified,
      siteId: customer.siteId,
      moneyPoint: customer.moneyPoint,
      goldPoint: customer.goldPoint,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };

    return {
      success: true,
      message: 'รีเฟรชโทเค็นสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: {
        customer: safeCustomer,
        token: newToken,
        refreshToken: newRefreshToken,
      },
    };
  }
  catch (error) {
    if (error instanceof HttpError) throw error;
    throw new HttpError(401, 'Refresh token ไม่ถูกต้องหรือหมดอายุ');
  }
}

export async function getCustomerProfile(customerId: string, deps: any = defaultDeps) {
  if (!customerId) throw new HttpError(404, 'ไม่พบ customerId');

  const customer = await deps.Customer.findById(customerId).select('-password -refreshToken -resetPasswordToken');
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');

  // สร้าง customer object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeCustomer = {
    _id: customer._id,
    email: customer.email,
    firstName: customer.firstName,
    lastName: customer.lastName,
    phone: customer.phone,
    avatar: customer.avatar,
    cover: customer.cover,
    isEmailVerified: customer.isEmailVerified,
    siteId: customer.siteId,
    moneyPoint: customer.moneyPoint,
    goldPoint: customer.goldPoint,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt,
  };

  return { customer: safeCustomer };
}

// ฟังก์ชันสำหรับอัพโหลดรูปโปรไฟล์สมาชิก
export async function uploadCustomerProfileImage(customerId: string, file: string | Buffer, deps: any = defaultDeps) {
  if (!customerId) throw new HttpError(400, 'ไม่พบ customerId');
  if (!file) throw new HttpError(400, 'ไม่พบไฟล์รูปภาพ');

  const customer = await deps.Customer.findById(customerId);
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');

  try {
    // ลบรูปเก่าถ้ามี
    if (customer.avatar) {
      const publicId = customer.avatar.split('/').pop()?.split('.')[0];
      if (publicId && publicId.startsWith('profile_')) {
        await cloudinaryUpload.deleteFile(`profiles/${publicId}`);
      }
    }

    // อัพโหลดรูปใหม่
    const result = await cloudinaryUpload.uploadProfileImage(file, customerId);

    // อัปเดต avatar ใน database
    customer.avatar = result.secureUrl;
    await customer.save();

    const safeCustomer = {
      _id: customer._id,
      email: customer.email,
      firstName: customer.firstName,
      lastName: customer.lastName,
      phone: customer.phone,
      avatar: customer.avatar,
      cover: customer.cover,
      isEmailVerified: customer.isEmailVerified,
      siteId: customer.siteId,
      moneyPoint: customer.moneyPoint,
      goldPoint: customer.goldPoint,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };

    return {
      success: true,
      message: 'อัพโหลดรูปโปรไฟล์สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: { customer: safeCustomer, imageUrl: result.secureUrl },
    };
  }
  catch (error: any) {
    console.error('Error uploading customer profile image:', error);
    throw new HttpError(500, `ไม่สามารถอัพโหลดรูปโปรไฟล์ได้: ${error.message}`);
  }
}

// ฟังก์ชันสำหรับอัพโหลดรูปปกสมาชิก
export async function uploadCustomerCoverImage(customerId: string, file: string | Buffer, deps: any = defaultDeps) {
  if (!customerId) throw new HttpError(400, 'ไม่พบ customerId');
  if (!file) throw new HttpError(400, 'ไม่พบไฟล์รูปภาพ');

  const customer = await deps.Customer.findById(customerId);
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');

  try {
    // ลบรูปเก่าถ้ามี
    if (customer.cover) {
      const publicId = customer.cover.split('/').pop()?.split('.')[0];
      if (publicId && publicId.startsWith('cover_')) {
        await cloudinaryUpload.deleteFile(`covers/${publicId}`);
      }
    }

    // อัพโหลดรูปใหม่
    const result = await cloudinaryUpload.uploadCoverImage(file, customerId);

    // อัปเดต cover ใน database
    customer.cover = result.secureUrl;
    await customer.save();

    const safeCustomer = {
      _id: customer._id,
      email: customer.email,
      firstName: customer.firstName,
      lastName: customer.lastName,
      phone: customer.phone,
      avatar: customer.avatar,
      cover: customer.cover,
      isEmailVerified: customer.isEmailVerified,
      siteId: customer.siteId,
      moneyPoint: customer.moneyPoint,
      goldPoint: customer.goldPoint,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };

    return {
      success: true,
      message: 'อัพโหลดรูปปกสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: { customer: safeCustomer, imageUrl: result.secureUrl },
    };
  }
  catch (error: any) {
    console.error('Error uploading customer cover image:', error);
    throw new HttpError(500, `ไม่สามารถอัพโหลดรูปปกได้: ${error.message}`);
  }
}

// ฟังก์ชันสำหรับสร้าง signed URL สำหรับ upload จาก client สำหรับสมาชิก
export async function generateCustomerUploadSignature(
  customerId: string,
  type: 'profile' | 'cover',
  deps: any = defaultDeps,
) {
  if (!customerId) throw new HttpError(400, 'ไม่พบ customerId');

  const customer = await deps.Customer.findById(customerId);
  if (!customer) throw new HttpError(404, 'ไม่พบสมาชิก');

  try {
    const folder = type === 'profile' ? 'profiles' : 'covers';
    const publicId = type === 'profile' ? `profile_${customerId}` : `cover_${customerId}`;

    const transformation = type === 'profile'
      ? { width: 400, height: 400, crop: 'fill', gravity: 'face', quality: 'auto:good', format: 'webp' }
      : { width: 1200, height: 400, crop: 'fill', quality: 'auto:good', format: 'webp' };

    const signedData = cloudinaryUpload.generateSignedUploadUrl({
      folder,
      publicId,
      resourceType: 'image',
      transformation,
    });

    return {
      success: true,
      message: 'สร้าง signed URL สำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: signedData,
    };
  }
  catch (error: any) {
    console.error('Error generating customer signed URL:', error);
    throw new HttpError(500, `ไม่สามารถสร้าง signed URL ได้: ${error.message}`);
  }
}

// Customer Statistics
export async function getCustomerStats(siteId: string, dateRange?: { start: Date; end: Date; }) {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // ใช้ dateRange ถ้ามี หรือใช้ค่าเริ่มต้น
    const _range = dateRange || {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: new Date(),
    };

    const [totalCustomers, newToday, newThisMonth, activeCustomers] = await Promise.all([
      Customer.countDocuments({ siteId }),
      Customer.countDocuments({ siteId, createdAt: { $gte: startOfDay } }),
      Customer.countDocuments({ siteId, createdAt: { $gte: startOfMonth } }),
      Customer.countDocuments({ siteId, status: 'active' }),
    ]);

    // คำนวณเปอร์เซ็นต์การเปลี่ยนแปลง (mock data สำหรับตอนนี้)
    const newChange = '+5%';

    return {
      total: totalCustomers,
      new: newToday,
      month: newThisMonth,
      active: activeCustomers,
      newChange,
      totalCustomers,
      newCustomers: newToday,
      activeCustomers,
    };
  }
  catch (err: any) {
    console.error('Error in getCustomerStats:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติลูกค้า');
  }
}
