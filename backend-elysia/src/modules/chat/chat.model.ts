import mongoose, { type Document, Schema } from 'mongoose';

// Interface สำหรับ Message
export interface IMessage extends Document {
  _id: string;
  chatRoomId: string;
  senderId: string;
  senderType: 'user' | 'customer';
  message: string;
  messageType: 'text' | 'image' | 'file';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isRead: boolean;
  readBy: Array<{
    userId: string;
    userType: 'user' | 'customer';
    readAt: Date;
  }>;
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Interface สำหรับ ChatRoom
export interface IChatRoom extends Document {
  _id: string;
  siteId: string;
  roomType: 'user_customer' | 'customer_customer';
  participants: Array<{
    userId: string;
    userType: 'user' | 'customer';
    joinedAt: Date;
    isActive: boolean;
  }>;
  lastMessage?: {
    message: string;
    senderId: string;
    senderType: 'user' | 'customer';
    sentAt: Date;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Schema สำหรับ Message
const MessageSchema = new Schema<IMessage>(
  {
    _id: { type: String, required: true },
    chatRoomId: { type: String, required: true, ref: 'ChatRoom' },
    senderId: { type: String, required: true },
    senderType: { type: String, enum: ['user', 'customer'], required: true },
    message: { type: String, required: true },
    messageType: { type: String, enum: ['text', 'image', 'file'], default: 'text' },
    fileUrl: { type: String },
    fileName: { type: String },
    fileSize: { type: Number },
    isRead: { type: Boolean, default: false },
    readBy: [
      {
        userId: { type: String, required: true },
        userType: { type: String, enum: ['user', 'customer'], required: true },
        readAt: { type: Date, default: Date.now },
      },
    ],
    isDeleted: { type: Boolean, default: false },
    deletedAt: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Schema สำหรับ ChatRoom
const ChatRoomSchema = new Schema<IChatRoom>(
  {
    _id: { type: String, required: true },
    siteId: { type: String, required: true, ref: 'Site' },
    roomType: { type: String, enum: ['user_customer', 'customer_customer'], required: true },
    participants: [
      {
        userId: { type: String, required: true },
        userType: { type: String, enum: ['user', 'customer'], required: true },
        joinedAt: { type: Date, default: Date.now },
        isActive: { type: Boolean, default: true },
      },
    ],
    lastMessage: {
      message: { type: String },
      senderId: { type: String },
      senderType: { type: String, enum: ['user', 'customer'] },
      sentAt: { type: Date },
    },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes สำหรับ performance
MessageSchema.index({ chatRoomId: 1, createdAt: -1 });
MessageSchema.index({ senderId: 1, senderType: 1 });
MessageSchema.index({ isDeleted: 1 });

ChatRoomSchema.index({ siteId: 1 });
ChatRoomSchema.index({ 'participants.userId': 1, 'participants.userType': 1 });
ChatRoomSchema.index({ roomType: 1 });
ChatRoomSchema.index({ isActive: 1 });

export const Message = mongoose.model<IMessage>('Message', MessageSchema);
export const ChatRoom = mongoose.model<IChatRoom>('ChatRoom', ChatRoomSchema);
