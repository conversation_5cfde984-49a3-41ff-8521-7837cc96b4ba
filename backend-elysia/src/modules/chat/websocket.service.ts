// Interface สำหรับ WebSocket connection
interface ChatConnection {
  ws: any; // WebSocket instance from Elysia
  userId: string;
  userType: 'user' | 'customer';
  siteId: string;
  chatRoomId?: string;
}

// เก็บ connections ทั้งหมด
const connections = new Map<string, ChatConnection>();

// เก็บ connections ตาม chatRoomId
const roomConnections = new Map<string, Set<string>>();

// เก็บ connections ตาม userId
const userConnections = new Map<string, Set<string>>();

// สร้าง connection ID
function generateConnectionId(): string {
  return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// เพิ่ม connection ใหม่
export function addConnection(ws: any, userId: string, userType: 'user' | 'customer', siteId: string): string {
  const connectionId = generateConnectionId();

  connections.set(connectionId, {
    ws,
    userId,
    userType,
    siteId,
  });

  // เพิ่มใน userConnections
  if (!userConnections.has(userId)) {
    userConnections.set(userId, new Set());
  }
  userConnections.get(userId)?.add(connectionId);

  console.log(`New connection added: ${connectionId} for user ${userId} (${userType})`);
  return connectionId;
}

// ลบ connection
export function removeConnection(connectionId: string): void {
  const connection = connections.get(connectionId);
  if (!connection) return;

  // ลบจาก room connections
  if (connection.chatRoomId) {
    const roomConns = roomConnections.get(connection.chatRoomId);
    if (roomConns) {
      roomConns.delete(connectionId);
      if (roomConns.size === 0) {
        roomConnections.delete(connection.chatRoomId);
      }
    }
  }

  // ลบจาก user connections
  const userConns = userConnections.get(connection.userId);
  if (userConns) {
    userConns.delete(connectionId);
    if (userConns.size === 0) {
      userConnections.delete(connection.userId);
    }
  }

  connections.delete(connectionId);
  console.log(`Connection removed: ${connectionId}`);
}

// เข้าร่วมห้องแชท
export function joinChatRoom(connectionId: string, chatRoomId: string): void {
  const connection = connections.get(connectionId);
  if (!connection) return;

  // ออกจากห้องเก่า (ถ้ามี)
  if (connection.chatRoomId) {
    leaveChatRoom(connectionId);
  }

  // เข้าห้องใหม่
  connection.chatRoomId = chatRoomId;

  if (!roomConnections.has(chatRoomId)) {
    roomConnections.set(chatRoomId, new Set());
  }
  roomConnections.get(chatRoomId)?.add(connectionId);

  console.log(`Connection ${connectionId} joined room ${chatRoomId}`);
}

// ออกจากห้องแชท
export function leaveChatRoom(connectionId: string): void {
  const connection = connections.get(connectionId);
  if (!connection || !connection.chatRoomId) return;

  const roomConns = roomConnections.get(connection.chatRoomId);
  if (roomConns) {
    roomConns.delete(connectionId);
    if (roomConns.size === 0) {
      roomConnections.delete(connection.chatRoomId);
    }
  }

  console.log(`Connection ${connectionId} left room ${connection.chatRoomId}`);
  connection.chatRoomId = undefined;
}

// ส่งข้อความไปยังห้องแชท
export function broadcastToRoom(chatRoomId: string, message: any, excludeConnectionId?: string): void {
  const roomConns = roomConnections.get(chatRoomId);
  if (!roomConns) return;

  roomConns.forEach(connectionId => {
    if (connectionId === excludeConnectionId) return;

    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === 1) {
      // WebSocket.OPEN
      try {
        connection.ws.send(JSON.stringify(message));
      }
      catch (error) {
        console.error(`Error sending message to connection ${connectionId}:`, error);
        removeConnection(connectionId);
      }
    }
  });
}

// ส่งข้อความไปยังผู้ใช้เฉพาะ
export function sendToUser(userId: string, message: any): void {
  const userConns = userConnections.get(userId);
  if (!userConns) return;

  userConns.forEach(connectionId => {
    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === 1) {
      // WebSocket.OPEN
      try {
        connection.ws.send(JSON.stringify(message));
      }
      catch (error) {
        console.error(`Error sending message to user ${userId}:`, error);
        removeConnection(connectionId);
      }
    }
  });
}

// ส่งข้อความไปยังทุกคนในไซต์
export function broadcastToSite(siteId: string, message: any, excludeUserId?: string): void {
  connections.forEach((connection, connectionId) => {
    if (connection.siteId === siteId && connection.userId !== excludeUserId) {
      if (connection.ws.readyState === 1) {
        // WebSocket.OPEN
        try {
          connection.ws.send(JSON.stringify(message));
        }
        catch (error) {
          console.error(`Error broadcasting to site ${siteId}:`, error);
          removeConnection(connectionId);
        }
      }
    }
  });
}

// ดึงข้อมูล connection
export function getConnection(connectionId: string): ChatConnection | undefined {
  return connections.get(connectionId);
}

// ดึงจำนวน connections ในห้อง
export function getRoomConnectionCount(chatRoomId: string): number {
  const roomConns = roomConnections.get(chatRoomId);
  return roomConns ? roomConns.size : 0;
}

// ดึงจำนวน connections ของผู้ใช้
export function getUserConnectionCount(userId: string): number {
  const userConns = userConnections.get(userId);
  return userConns ? userConns.size : 0;
}

// ตรวจสอบว่าผู้ใช้ออนไลน์หรือไม่
export function isUserOnline(userId: string): boolean {
  return getUserConnectionCount(userId) > 0;
}

// ดึงรายการผู้ใช้ออนไลน์ในไซต์
export function getOnlineUsersInSite(siteId: string): Array<{ userId: string; userType: 'user' | 'customer'; }> {
  const onlineUsers = new Map<string, 'user' | 'customer'>();

  connections.forEach(connection => {
    if (connection.siteId === siteId) {
      onlineUsers.set(connection.userId, connection.userType);
    }
  });

  return Array.from(onlineUsers.entries()).map(([userId, userType]) => ({
    userId,
    userType,
  }));
}

// ทำความสะอาด connections ที่ปิดแล้ว
export function cleanupConnections(): void {
  const toRemove: string[] = [];

  connections.forEach((connection, connectionId) => {
    if (connection.ws.readyState !== 1) {
      // Not WebSocket.OPEN
      toRemove.push(connectionId);
    }
  });

  toRemove.forEach(connectionId => {
    removeConnection(connectionId);
  });

  // แสดง log เฉพาะเมื่อมี connection ที่ถูกลบ
  if (toRemove.length > 0) {
    console.log(`🧹 Cleaned up ${toRemove.length} dead WebSocket connections`);
  }
}

// ตัวแปรสำหรับเก็บ interval ID
let cleanupInterval: NodeJS.Timeout | null = null;

// เริ่ม cleanup process
export function startCleanupProcess(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
  }

  // เรียกใช้ cleanup ทุก 30 วินาที
  cleanupInterval = setInterval(cleanupConnections, 30000);
  console.log('🚀 WebSocket cleanup process started (every 30 seconds)');
}

// หยุด cleanup process
export function stopCleanupProcess(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('⏹️ WebSocket cleanup process stopped');
  }
}

// เริ่ม cleanup process อัตโนมัติ
startCleanupProcess();
