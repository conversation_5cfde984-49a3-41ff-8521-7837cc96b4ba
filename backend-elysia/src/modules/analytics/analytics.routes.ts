import { isSiteOwner } from '@/core/middleware/checkUser';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import { t } from 'elysia';
import { AnalyticsService } from './analytics.service';

const analyticsService = new AnalyticsService();

export const analyticsRoutes = new Elysia({ prefix: '/analytics' })
  // Public downloads route (ไม่ต้องการ authentication)
  .get('/dashboard/:siteId/downloads/:fileName', async ({ params, set }: any) => {
    try {
      const { fileName } = params;

      // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่ (ในที่นี้จะสร้างไฟล์ตัวอย่าง)
      if (!fileName || !fileName.includes('analytics_')) {
        throw new HttpError(404, 'ไม่พบไฟล์ที่ต้องการ');
      }

      // สร้างไฟล์ตัวอย่าง (ในที่นี้จะสร้าง CSV content)
      const csvContent =
        `Site ID,Generated At,Period,Total Revenue,Total Orders,Total Customers,Average Order Value\nmdo3quxut3oQO,${
          new Date().toISOString()
        },month,150000,45,23,3333.33`;

      // ตั้งค่า headers สำหรับการดาวน์โหลด
      set.headers['Content-Type'] = 'text/csv';
      set.headers['Content-Disposition'] = `attachment; filename="${fileName}"`;
      set.headers['Cache-Control'] = 'public, max-age=3600';
      set.headers['X-Expires-At'] = new Date(Date.now() + 60 * 60 * 1000).toISOString();

      return csvContent;
    }
    catch (error: any) {
      console.error('[analytics] Download error:', error);
      if (error instanceof HttpError) throw error;
      throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดาวน์โหลดไฟล์');
    }
  })
  // Protected routes ที่ต้องการ authentication
  .use(userAuthPlugin)
  // ดึงสถิติรวมของเว็บไซต์
  .get(
    '/overview',
    async ({ query, user, site }: any) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const dateRange = {
          start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: query.endDate ? new Date(query.endDate) : new Date(),
        };

        const analytics = await analyticsService.getSiteAnalytics(site._id, dateRange);

        return {
          success: true,
          message: 'ดึงสถิติรวมสำเร็จ',
          statusMessage: 'สำเร็จ!',
          timestamp: new Date().toISOString(),
          data: analytics,
        };
      }
      catch (error: any) {
        console.error('[analytics] Unexpected error:', error);
        // ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะติดต่อกับระบบการวิเคราะห์');
      }
    },
    {
      query: t.Object({
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
      }),
    },
  )
  // Dashboard Analytics Routes (ใช้ siteId จาก URL)
  .group('/dashboard/:siteId', app =>
    app
      .get(
        '/sales',
        async ({ params, query, user }: any) => {
          try {
            console.log('🔍 user sales', user);
            const { siteId } = params;
            const userId = user?._id;

            if (!userId) {
              throw new Error('ไม่พบข้อมูลผู้ใช้');
            }

            // ตรวจสอบว่าเป็น site owner หรือไม่
            const isOwner = await isSiteOwner(siteId, userId);
            if (!isOwner) {
              throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
            }

            const dateRange = {
              start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              end: query.endDate ? new Date(query.endDate) : new Date(),
            };

            const salesAnalytics = await analyticsService.getSalesAnalytics(siteId, dateRange);

            return {
              success: true,
              statusMessage: 'สำเร็จ!',
              timestamp: new Date().toISOString(),
              message: 'ดึงสถิติยอดขายสำเร็จ',
              data: salesAnalytics,
            };
          }
          catch (error: any) {
            console.error('[analytics] Unexpected error:', error);
            // ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
            if (error instanceof HttpError) throw error;
            throw new HttpError(500, 'เกิดข้อผิดพลาดขณะติดต่อกับระบบการวิเคราะห์');
          }
        },
        {
          query: t.Object({
            startDate: t.Optional(t.String()),
            endDate: t.Optional(t.String()),
          }),
        },
      )
      .get(
        '/products',
        async ({ params, query, user }: any) => {
          try {
            console.log('🔍 user', user);
            const { siteId } = params;
            const userId = user?._id;

            if (!userId) {
              throw new Error('ไม่พบข้อมูลผู้ใช้');
            }

            // ตรวจสอบว่าเป็น site owner หรือไม่
            const isOwner = await isSiteOwner(siteId, userId);
            if (!isOwner) {
              throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
            }

            const dateRange = {
              start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              end: query.endDate ? new Date(query.endDate) : new Date(),
            };

            const productAnalytics = await analyticsService.getProductAnalytics(siteId, dateRange);

            return {
              success: true,
              statusMessage: 'สำเร็จ!',
              timestamp: new Date().toISOString(),
              message: 'ดึงสถิติสินค้าสำเร็จ',
              data: productAnalytics,
            };
          }
          catch (error: any) {
            console.error('[analytics] Unexpected error:', error);
            // ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
            if (error instanceof HttpError) throw error;
            throw new HttpError(500, 'เกิดข้อผิดพลาดขณะติดต่อกับระบบการวิเคราะห์');
          }
        },
        {
          query: t.Object({
            startDate: t.Optional(t.String()),
            endDate: t.Optional(t.String()),
          }),
        },
      )
      .get(
        '/customers',
        async ({ params, query, user }: any) => {
          try {
            console.log('🔍 user', user);
            const { siteId } = params;
            const userId = user?._id;

            if (!userId) {
              throw new Error('ไม่พบข้อมูลผู้ใช้');
            }

            // ตรวจสอบว่าเป็น site owner หรือไม่
            const isOwner = await isSiteOwner(siteId, userId);
            if (!isOwner) {
              throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
            }

            const dateRange = {
              start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              end: query.endDate ? new Date(query.endDate) : new Date(),
            };

            const customerAnalytics = await analyticsService.getCustomerAnalytics(siteId, dateRange);

            return {
              success: true,
              statusMessage: 'สำเร็จ!',
              timestamp: new Date().toISOString(),
              message: 'ดึงสถิติลูกค้าสำเร็จ',
              data: customerAnalytics,
            };
          }
          catch (error: any) {
            console.error('[analytics] Unexpected error:', error);
            // ถ้าเป็น HttpError ให้ throw ต่อ, ถ้าไม่ใช่ให้แปลงเป็น HttpError 500
            if (error instanceof HttpError) throw error;
            throw new HttpError(500, 'เกิดข้อผิดพลาดขณะติดต่อกับระบบการวิเคราะห์');
          }
        },
        {
          query: t.Object({
            startDate: t.Optional(t.String()),
            endDate: t.Optional(t.String()),
          }),
        },
      )
      // ส่งออกรายงาน
      .post(
        '/export',
        async ({ params, body, user }: any) => {
          try {
            const { siteId } = params;
            const userId = user?._id;

            if (!userId) {
              throw new Error('ไม่พบข้อมูลผู้ใช้');
            }

            // ตรวจสอบว่าเป็น site owner หรือไม่
            const isOwner = await isSiteOwner(siteId, userId);
            if (!isOwner) {
              throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
            }

            const { format = 'csv', _period = 'month', _startDate, _endDate } = body;

            // สร้างไฟล์ export (mock สำหรับตอนนี้)
            const fileName = `analytics_${siteId}_${new Date().toISOString().split('T')[0]}.${format}`;
            const downloadUrl = `/analytics/dashboard/${siteId}/downloads/${fileName}`;

            return {
              success: true,
              statusMessage: 'สำเร็จ!',
              timestamp: new Date().toISOString(),
              message: 'ส่งออกรายงานสำเร็จ',
              data: { downloadUrl },
            };
          }
          catch (error: any) {
            console.error('[analytics] Export error:', error);
            if (error instanceof HttpError) throw error;
            throw new HttpError(500, 'เกิดข้อผิดพลาดในการส่งออกรายงาน');
          }
        },
        {
          body: t.Object({
            format: t.Optional(t.Union([t.Literal('csv'), t.Literal('excel'), t.Literal('pdf')])),
            period: t.Optional(t.String()),
            startDate: t.Optional(t.String()),
            endDate: t.Optional(t.String()),
          }),
        },
      ));
