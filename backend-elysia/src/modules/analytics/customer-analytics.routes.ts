import { userPlugin } from '@/core/middleware/checkUser';
import { Elysia, t } from 'elysia';
import * as customerAnalyticsService from './customer-analytics.service';

export const customerAnalyticsRoutes = new Elysia({ prefix: '/customer-analytics' })
  .use(userPlugin)
  .get(
    '/',
    async ({ query, _user, store }: any) => {
      const { _period = 'month', startDate, endDate, _limit = 10 } = query;

      const analytics = await customerAnalyticsService.getCustomerAnalytics(
        store.siteId,
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date(),
      );

      return {
        success: true,
        data: analytics,
      };
    },
    {
      query: t.Object({
        period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
        limit: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/segments',
    async ({ query, store }: any) => {
      const { _period = 'month', startDate, endDate } = query;

      const segments = await customerAnalyticsService.getCustomerSegments(
        store.siteId,
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date(),
      );

      return {
        success: true,
        data: segments,
      };
    },
    {
      query: t.Object({
        period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/behaviors',
    async ({ query, store }: any) => {
      const { _period = 'month', startDate, endDate } = query;

      const behaviors = await customerAnalyticsService.getCustomerBehaviors(
        store.siteId,
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date(),
      );

      return {
        success: true,
        data: behaviors,
      };
    },
    {
      query: t.Object({
        period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/top-customers',
    async ({ query, store }: any) => {
      const { _period = 'month', startDate, endDate, _limit = 10 } = query;

      const topCustomers = await customerAnalyticsService.getTopCustomers(
        store.siteId,
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date(),
      );

      return {
        success: true,
        data: topCustomers,
      };
    },
    {
      query: t.Object({
        period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
        limit: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/acquisition',
    async ({ query, store }: any) => {
      const { _period = 'month', startDate, endDate } = query;

      const acquisition = await customerAnalyticsService.getCustomerAcquisition(
        store.siteId,
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date(),
      );

      return {
        success: true,
        data: acquisition,
      };
    },
    {
      query: t.Object({
        period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/retention',
    async ({ query, store }: any) => {
      const { _period = 'month', startDate, endDate } = query;

      const retention = await customerAnalyticsService.getCustomerRetention(
        store.siteId,
        startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate ? new Date(endDate) : new Date(),
      );

      return {
        success: true,
        data: retention,
      };
    },
    {
      query: t.Object({
        period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
      }),
    },
  )
  .post(
    '/generate',
    async ({ body, store }: any) => {
      const { _period, startDate, endDate } = body;

      const analytics = await customerAnalyticsService.generateCustomerAnalytics(
        store.siteId,
        new Date(startDate),
        new Date(endDate),
      );

      return {
        success: true,
        data: analytics,
      };
    },
    {
      body: t.Object({
        period: t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')]),
        startDate: t.String(),
        endDate: t.String(),
      }),
    },
  );
