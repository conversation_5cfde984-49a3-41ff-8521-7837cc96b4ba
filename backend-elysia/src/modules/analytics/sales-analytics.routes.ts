import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import {
  salesAnalyticsFilterSchema,
  salesAnalyticsResponseSchema,
  salesTrendsResponseSchema,
  topCustomersResponseSchema,
  topProductsResponseSchema,
} from './sales-analytics.schema';
import {
  getLatestSalesAnalytics,
  getSalesAnalytics,
  getSalesTrends,
  getTopCustomers,
  getTopProducts,
} from './sales-analytics.service';
export const salesAnalyticsRoutes = new Elysia({ prefix: '/sales-analytics' })
  .use(userAuthPlugin)
  // ดึง sales analytics
  .get(
    '/analytics',
    async ({ query }: any) => {
      const { siteId, period, startDate, endDate } = query;

      if (!siteId || !period || !startDate || !endDate) {
        throw new HttpError(400, 'กรุณาระบุ siteId, period, startDate และ endDate');
      }

      const analytics = await getSalesAnalytics(siteId, period, new Date(startDate), new Date(endDate));

      return {
        success: true,
        message: 'ดึง sales analytics สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: analytics,
      };
    },
    {
      query: salesAnalyticsFilterSchema,
      response: salesAnalyticsResponseSchema,
    },
  )
  // ดึง latest sales analytics
  .get(
    '/latest/:period',
    async ({ params, query }: any) => {
      const { period } = params;
      const { siteId } = query;

      const analytics = await getLatestSalesAnalytics(siteId, period);

      return {
        success: true,
        message: 'ดึง latest sales analytics สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: analytics,
      };
    },
    {
      response: salesAnalyticsResponseSchema,
    },
  )
  // ดึง sales trends
  .get(
    '/trends',
    async ({ query }: any) => {
      const { siteId, startDate, endDate } = query;

      if (!siteId || !startDate || !endDate) {
        throw new HttpError(400, 'กรุณาระบุ siteId, startDate และ endDate');
      }

      const trends = await getSalesTrends(siteId, new Date(startDate), new Date(endDate));

      return {
        success: true,
        message: 'ดึง sales trends สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: trends,
      };
    },
    {
      response: salesTrendsResponseSchema,
    },
  )
  // ดึง top products
  .get(
    '/top-products',
    async ({ query }: any) => {
      const { siteId, startDate, endDate, limit = 10 } = query;

      if (!siteId || !startDate || !endDate) {
        throw new HttpError(400, 'กรุณาระบุ siteId, startDate และ endDate');
      }

      const topProducts = await getTopProducts(siteId, new Date(startDate), new Date(endDate), parseInt(limit));

      return {
        success: true,
        message: 'ดึง top products สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: topProducts,
      };
    },
    {
      response: topProductsResponseSchema,
    },
  )
  // ดึง top customers
  .get(
    '/top-customers',
    async ({ query }: any) => {
      const { siteId, startDate, endDate, limit = 10 } = query;

      if (!siteId || !startDate || !endDate) {
        throw new HttpError(400, 'กรุณาระบุ siteId, startDate และ endDate');
      }

      const topCustomers = await getTopCustomers(siteId, new Date(startDate), new Date(endDate), parseInt(limit));

      return {
        success: true,
        message: 'ดึง top customers สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: topCustomers,
      };
    },
    {
      response: topCustomersResponseSchema,
    },
  );
