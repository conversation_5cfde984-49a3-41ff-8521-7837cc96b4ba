import { t } from 'elysia';

export const HeatmapCreateSchema = t.Object({
  page: t.String({ error: 'page ต้องเป็นข้อความ' }),
  data: t.Array(
    t.Object({
      x: t.Number({ error: 'x ต้องเป็นตัวเลข' }),
      y: t.Number({ error: 'y ต้องเป็นตัวเลข' }),
      value: t.Number({ error: 'value ต้องเป็นตัวเลข' }),
    }),
    { error: 'data ต้องเป็น array ของ object' },
  ),
  device: t.Optional(t.String({ error: 'device ต้องเป็นข้อความ' })),
  sessionId: t.Optional(t.String({ error: 'sessionId ต้องเป็นข้อความ' })),
});

export const UserBehaviorCreateSchema = t.Object({
  userId: t.String({ error: 'userId ต้องเป็นข้อความ' }),
  sessionId: t.String({ error: 'sessionId ต้องเป็นข้อความ' }),
  actions: t.Array(
    t.Object({
      type: t.String({ error: 'type ต้องเป็นข้อความ' }),
      timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
      details: t.Optional(t.Object({}, { error: 'details ต้องเป็น object' })),
    }),
    { error: 'actions ต้องเป็น array ของ object' },
  ),
  device: t.Optional(t.String({ error: 'device ต้องเป็นข้อความ' })),
  page: t.Optional(t.String({ error: 'page ต้องเป็นข้อความ' })),
});

export const ABTestCreateSchema = t.Object({
  name: t.String({ error: 'name ต้องเป็นข้อความ' }),
  variants: t.Array(
    t.Object({
      name: t.String({ error: 'name ต้องเป็นข้อความ' }),
      description: t.Optional(t.String({ error: 'description ต้องเป็นข้อความ' })),
      traffic: t.Number({ error: 'traffic ต้องเป็นตัวเลข' }),
    }),
    { error: 'variants ต้องเป็น array ของ object' },
  ),
  startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
  endDate: t.Optional(t.String({ error: 'endDate ต้องเป็นข้อความ' })),
  status: t.Optional(t.String({ error: 'status ต้องเป็นข้อความ' })),
});

export const ConversionFunnelCreateSchema = t.Object({
  name: t.String({ error: 'name ต้องเป็นข้อความ' }),
  steps: t.Array(
    t.Object({
      name: t.String({ error: 'name ต้องเป็นข้อความ' }),
      order: t.Number({ error: 'order ต้องเป็นตัวเลข' }),
      conversionRate: t.Optional(t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' })),
    }),
    { error: 'steps ต้องเป็น array ของ object' },
  ),
  startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
  endDate: t.Optional(t.String({ error: 'endDate ต้องเป็นข้อความ' })),
});

export const CohortAnalysisCreateSchema = t.Object({
  name: t.String({ error: 'name ต้องเป็นข้อความ' }),
  cohortType: t.String({ error: 'cohortType ต้องเป็นข้อความ' }),
  startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
  endDate: t.Optional(t.String({ error: 'endDate ต้องเป็นข้อความ' })),
  data: t.Array(
    t.Object({
      cohort: t.String({ error: 'cohort ต้องเป็นข้อความ' }),
      values: t.Array(t.Number({ error: 'values ต้องเป็น array ของตัวเลข' }), {
        error: 'values ต้องเป็น array ของตัวเลข',
      }),
    }),
    { error: 'data ต้องเป็น array ของ object' },
  ),
});
