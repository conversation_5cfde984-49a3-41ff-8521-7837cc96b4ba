import { generateId } from '@/core/utils';
import mongoose, { type Document, Schema } from 'mongoose';

export interface IPerformanceOptimization extends Document {
  _id: string;
  siteId: string;
  type: 'cache' | 'database' | 'api' | 'frontend' | 'image';
  status: 'active' | 'inactive' | 'maintenance';
  metrics: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  };
  optimization: {
    enabled: boolean;
    strategy: string;
    settings: Record<string, any>;
    lastOptimized: Date;
    nextOptimization: Date;
  };
  cache: {
    enabled: boolean;
    type: 'redis' | 'memory' | 'file';
    hitRate: number;
    missRate: number;
    size: number;
    maxSize: number;
    ttl: number;
  };
  database: {
    connectionPool: number;
    queryOptimization: boolean;
    indexing: boolean;
    slowQueries: number;
    averageQueryTime: number;
  };
  api: {
    rateLimit: boolean;
    compression: boolean;
    caching: boolean;
    averageResponseTime: number;
    requestsPerSecond: number;
  };
  frontend: {
    minification: boolean;
    compression: boolean;
    lazyLoading: boolean;
    bundleSize: number;
    loadTime: number;
  };
  image: {
    optimization: boolean;
    formats: string[];
    compression: number;
    averageSize: number;
    totalSize: number;
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    threshold: number;
    currentValue: number;
    createdAt: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

const PerformanceOptimizationSchema = new Schema<IPerformanceOptimization>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    type: {
      type: String,
      required: true,
      enum: ['cache', 'database', 'api', 'frontend', 'image'],
      default: 'api',
    },
    status: {
      type: String,
      required: true,
      enum: ['active', 'inactive', 'maintenance'],
      default: 'active',
    },
    metrics: {
      responseTime: { type: Number, default: 0 },
      throughput: { type: Number, default: 0 },
      errorRate: { type: Number, default: 0 },
      cpuUsage: { type: Number, default: 0 },
      memoryUsage: { type: Number, default: 0 },
      diskUsage: { type: Number, default: 0 },
    },
    optimization: {
      enabled: { type: Boolean, default: true },
      strategy: { type: String, default: 'auto' },
      settings: { type: Schema.Types.Mixed, default: {} },
      lastOptimized: { type: Date, default: Date.now },
      nextOptimization: { type: Date, default: Date.now },
    },
    cache: {
      enabled: { type: Boolean, default: true },
      type: {
        type: String,
        enum: ['redis', 'memory', 'file'],
        default: 'memory',
      },
      hitRate: { type: Number, default: 0 },
      missRate: { type: Number, default: 0 },
      size: { type: Number, default: 0 },
      maxSize: { type: Number, default: 100 },
      ttl: { type: Number, default: 3600 },
    },
    database: {
      connectionPool: { type: Number, default: 10 },
      queryOptimization: { type: Boolean, default: true },
      indexing: { type: Boolean, default: true },
      slowQueries: { type: Number, default: 0 },
      averageQueryTime: { type: Number, default: 0 },
    },
    api: {
      rateLimit: { type: Boolean, default: true },
      compression: { type: Boolean, default: true },
      caching: { type: Boolean, default: true },
      averageResponseTime: { type: Number, default: 0 },
      requestsPerSecond: { type: Number, default: 0 },
    },
    frontend: {
      minification: { type: Boolean, default: true },
      compression: { type: Boolean, default: true },
      lazyLoading: { type: Boolean, default: true },
      bundleSize: { type: Number, default: 0 },
      loadTime: { type: Number, default: 0 },
    },
    image: {
      optimization: { type: Boolean, default: true },
      formats: [{ type: String, default: ['webp', 'jpg'] }],
      compression: { type: Number, default: 80 },
      averageSize: { type: Number, default: 0 },
      totalSize: { type: Number, default: 0 },
    },
    alerts: [
      {
        type: {
          type: String,
          enum: ['warning', 'error', 'info'],
          default: 'info',
        },
        message: { type: String, required: true },
        threshold: { type: Number, required: true },
        currentValue: { type: Number, required: true },
        createdAt: { type: Date, default: Date.now },
      },
    ],
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
PerformanceOptimizationSchema.index({ siteId: 1, type: 1 });
PerformanceOptimizationSchema.index({ siteId: 1, status: 1 });
PerformanceOptimizationSchema.index({ createdAt: -1 });

// Static methods
PerformanceOptimizationSchema.statics.getOptimizationByType = async function(siteId: string, type: string) {
  return this.findOne({ siteId, type });
};

PerformanceOptimizationSchema.statics.getActiveOptimizations = async function(siteId: string) {
  return this.find({ siteId, status: 'active' });
};

PerformanceOptimizationSchema.statics.getOptimizationMetrics = async function(siteId: string) {
  return this.aggregate([
    { $match: { siteId } },
    {
      $group: {
        _id: null,
        averageResponseTime: { $avg: '$metrics.responseTime' },
        averageThroughput: { $avg: '$metrics.throughput' },
        averageErrorRate: { $avg: '$metrics.errorRate' },
        averageCpuUsage: { $avg: '$metrics.cpuUsage' },
        averageMemoryUsage: { $avg: '$metrics.memoryUsage' },
        averageDiskUsage: { $avg: '$metrics.diskUsage' },
      },
    },
  ]);
};

export const PerformanceOptimization = mongoose.model<IPerformanceOptimization>(
  'PerformanceOptimization',
  PerformanceOptimizationSchema,
);
