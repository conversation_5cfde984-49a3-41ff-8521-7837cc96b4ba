# ระบบจัดการไฟล์และรูปภาพ (Media Management System)

ระบบจัดการไฟล์และรูปภาพที่สมบูรณ์ รองรับการอัพโหลด, จัดการ, ปรับแต่ง และเพิ่มประสิทธิภาพไฟล์

## คุณสมบัติ

### ประเภทไฟล์ที่รองรับ

- ✅ **รูปภาพ** - JPEG, PNG, GIF, WebP, SVG
- ✅ **วิดีโอ** - MP4, AVI, MOV, WMV, WebM
- ✅ **เสียง** - MP3, WAV, OGG, M4A
- ✅ **เอกสาร** - PDF, TXT, DOC, DOCX
- ✅ **ไฟล์บีบอัด** - ZIP, RAR, 7Z

### การจัดหมวดหมู่

- ✅ **Product** - รูปภาพสินค้า
- ✅ **Banner** - แบนเนอร์โฆษณา
- ✅ **Avatar** - รูปโปรไฟล์
- ✅ **Content** - เนื้อหาทั่วไป
- ✅ **Document** - เอกสาร
- ✅ **Other** - อื่นๆ

### การจัดการรูปภาพ

- ✅ **Auto Thumbnails** - สร้าง thumbnail อัตโนมัติ
- ✅ **Image Resize** - ปรับขนาดรูปภาพ
- ✅ **Format Conversion** - แปลงรูปแบบ (JPEG, PNG, WebP, AVIF)
- ✅ **Quality Control** - ควบคุมคุณภาพ
- ✅ **Metadata Extraction** - ดึงข้อมูล EXIF

### การค้นหาและกรอง

- ✅ **Full-text Search** - ค้นหาในชื่อไฟล์และ metadata
- ✅ **Filter by Type** - กรองตามประเภทไฟล์
- ✅ **Filter by Category** - กรองตามหมวดหมู่
- ✅ **Filter by Size** - กรองตามขนาดไฟล์
- ✅ **Filter by Date** - กรองตามวันที่
- ✅ **Tag System** - ระบบแท็ก

### การจัดการขั้นสูง

- ✅ **Bulk Operations** - จัดการหลายไฟล์พร้อมกัน
- ✅ **Usage Tracking** - ติดตามการใช้งาน
- ✅ **Auto Expiry** - หมดอายุอัตโนมัติ
- ✅ **Public/Private** - ควบคุมการเข้าถึง
- ✅ **Statistics** - สถิติการใช้งาน

## API Endpoints

### อัพโหลดไฟล์

#### อัพโหลดไฟล์เดียว

```
POST /v1/media/upload
Content-Type: multipart/form-data
```

**Form Data:**

```
file: [File]
category: "product"
alt: "รูปภาพสินค้า"
title: "สินค้าใหม่"
description: "รูปภาพสินค้าใหม่ล่าสุด"
tags: ["product", "new", "electronics"]
isPublic: true
```

#### อัพโหลดหลายไฟล์

```
POST /v1/media/upload-multiple
Content-Type: multipart/form-data
```

**Form Data:**

```
files: [File, File, File]
category: "banner"
tags: ["promotion", "sale"]
isPublic: true
```

### จัดการไฟล์

#### ดึงรายการไฟล์

```
GET /v1/media?type=image&category=product&page=1&limit=20
```

**Query Parameters:**

- `type`: image, video, audio, document, archive, other
- `category`: product, banner, avatar, content, document, other
- `isPublic`: true/false
- `isOptimized`: true/false
- `uploadedBy`: user ID
- `tags`: tag1,tag2,tag3
- `search`: search term
- `minSize`: minimum file size in bytes
- `maxSize`: maximum file size in bytes
- `startDate`: start date
- `endDate`: end date
- `sortBy`: createdAt, size, usageCount, filename
- `sortOrder`: asc, desc
- `page`: page number
- `limit`: items per page (1-100)

#### ดึงไฟล์ตาม ID

```
GET /v1/media/:id
```

#### อัพเดทข้อมูลไฟล์

```
PATCH /v1/media/:id
```

**Body:**

```json
{
  "alt": "รูปภาพใหม่",
  "title": "ชื่อใหม่",
  "description": "คำอธิบายใหม่",
  "tags": ["updated", "modified"],
  "category": "banner",
  "isPublic": false
}
```

#### ลบไฟล์

```
DELETE /v1/media/:id
```

### การจัดการรูปภาพ

#### ปรับขนาดรูปภาพ

```
POST /v1/media/:id/resize
```

**Body:**

```json
{
  "width": 800,
  "height": 600,
  "quality": 80,
  "format": "webp",
  "fit": "cover"
}
```

**Fit Options:**

- `cover`: ครอบคลุมพื้นที่ทั้งหมด (อาจตัดส่วนเกิน)
- `contain`: พอดีในกรอบ (อาจมีพื้นที่ว่าง)
- `fill`: ยืดให้พอดีกรอบ (อาจบิดเบี้ยว)
- `inside`: ปรับให้อยู่ในกรอบ
- `outside`: ปรับให้ครอบคลุมกรอบ

#### สร้าง Thumbnails

```
POST /v1/media/:id/thumbnails
```

**Body:**

```json
{
  "sizes": {
    "small": { "width": 150, "height": 150 },
    "medium": { "width": 300, "height": 300 },
    "large": { "width": 600, "height": 600 }
  },
  "quality": 80,
  "format": "webp"
}
```

### การจัดการหลายไฟล์

#### Bulk Actions

```
POST /v1/media/bulk-action
```

**Body:**

```json
{
  "mediaIds": ["id1", "id2", "id3"],
  "action": "makePublic",
  "data": {
    "tags": ["bulk", "updated"],
    "category": "product"
  }
}
```

**Actions:**

- `delete`: ลบไฟล์
- `optimize`: เพิ่มประสิทธิภาพ (สร้าง thumbnails)
- `makePublic`: เปลี่ยนเป็น public
- `makePrivate`: เปลี่ยนเป็น private
- `addTags`: เพิ่มแท็ก
- `removeTags`: ลบแท็ก
- `changeCategory`: เปลี่ยนหมวดหมู่

### สถิติและรายงาน

#### ดึงสถิติไฟล์

```
GET /v1/media/stats
```

**Response:**

```json
{
  "success": true,
  "data": {
    "totalFiles": 1250,
    "totalSize": 524288000,
    "filesByType": [
      { "type": "image", "count": 800, "size": 400000000 },
      { "type": "document", "count": 300, "size": 100000000 },
      { "type": "video", "count": 150, "size": 24288000 }
    ],
    "filesByCategory": [
      { "category": "product", "count": 600, "size": 300000000 },
      { "category": "banner", "count": 200, "size": 100000000 }
    ],
    "recentUploads": 45,
    "mostUsed": [...]
  }
}
```

### API สำหรับแสดงผล (Public)

#### ดึงรูปภาพสำหรับ Gallery

```
GET /v1/media/gallery/product?page=1&limit=12
```

#### ค้นหาไฟล์

```
GET /v1/media/search/สินค้า?type=image&limit=10
```

## ตัวอย่างการใช้งาน

### 1. อัพโหลดรูปภาพสินค้า

```bash
curl -X POST http://localhost:5000/v1/media/upload \
  -H "Authorization: Bearer <admin_token>" \
  -F "file=@product-image.jpg" \
  -F "category=product" \
  -F "alt=รูปภาพสินค้าใหม่" \
  -F "title=สินค้าใหม่ล่าสุด" \
  -F "tags=electronics,smartphone,new" \
  -F "isPublic=true"
```

### 2. อัพโหลดหลายไฟล์

```bash
curl -X POST http://localhost:5000/v1/media/upload-multiple \
  -H "Authorization: Bearer <admin_token>" \
  -F "files=@banner1.jpg" \
  -F "files=@banner2.jpg" \
  -F "files=@banner3.jpg" \
  -F "category=banner" \
  -F "tags=promotion,sale"
```

### 3. ปรับขนาดรูปภาพ

```bash
curl -X POST http://localhost:5000/v1/media/media123/resize \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "width": 800,
    "height": 600,
    "quality": 85,
    "format": "webp",
    "fit": "cover"
  }'
```

### 4. สร้าง Thumbnails

```bash
curl -X POST http://localhost:5000/v1/media/media123/thumbnails \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "sizes": {
      "small": { "width": 150, "height": 150 },
      "medium": { "width": 300, "height": 300 },
      "large": { "width": 600, "height": 600 }
    },
    "quality": 80,
    "format": "webp"
  }'
```

### 5. Bulk Actions

```bash
curl -X POST http://localhost:5000/v1/media/bulk-action \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "mediaIds": ["media1", "media2", "media3"],
    "action": "addTags",
    "data": {
      "tags": ["featured", "homepage"]
    }
  }'
```

### 6. ค้นหาไฟล์

```bash
curl -X GET "http://localhost:5000/v1/media?search=สินค้า&type=image&category=product&limit=10" \
  -H "Authorization: Bearer <token>"
```

## Frontend Integration

### JavaScript สำหรับอัพโหลดไฟล์

```javascript
// อัพโหลดไฟล์เดียว
async function uploadFile(file, options = {}) {
  const formData = new FormData();
  formData.append('file', file);

  Object.keys(options).forEach(key => {
    if (Array.isArray(options[key])) {
      options[key].forEach(value => formData.append(key, value));
    }
    else {
      formData.append(key, options[key]);
    }
  });

  const response = await fetch('/v1/media/upload', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  return response.json();
}

// อัพโหลดหลายไฟล์
async function uploadMultipleFiles(files, options = {}) {
  const formData = new FormData();

  files.forEach(file => {
    formData.append('files', file);
  });

  Object.keys(options).forEach(key => {
    formData.append(key, options[key]);
  });

  const response = await fetch('/v1/media/upload-multiple', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  return response.json();
}

// ดึงรายการไฟล์
async function getMediaList(filters = {}) {
  const params = new URLSearchParams(filters);

  const response = await fetch(`/v1/media?${params}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.json();
}

// ปรับขนาดรูปภาพ
async function resizeImage(mediaId, options) {
  const response = await fetch(`/v1/media/${mediaId}/resize`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(options),
  });

  return response.json();
}
```

### React Component ตัวอย่าง

```jsx
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

function MediaUploader({ onUpload, category = 'other' }) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const onDrop = useCallback(
    async acceptedFiles => {
      setUploading(true);
      setProgress(0);

      try {
        const results = await uploadMultipleFiles(acceptedFiles, {
          category,
          isPublic: true,
        });

        onUpload(results.data.uploaded);
      }
      catch (error) {
        console.error('Upload failed:', error);
      }
      finally {
        setUploading(false);
        setProgress(0);
      }
    },
    [category, onUpload],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
      'video/*': ['.mp4', '.avi', '.mov'],
      'application/pdf': ['.pdf'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed p-6 text-center cursor-pointer
        ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
        ${uploading ? 'pointer-events-none opacity-50' : ''}
      `}
    >
      <input {...getInputProps()} />

      {uploading ? (
        <div>
          <div className="mb-2">กำลังอัพโหลด...</div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      ) : (
        <div>
          {isDragActive ? <p>วางไฟล์ที่นี่...</p> : (
            <div>
              <p>ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์</p>
              <p className="text-sm text-gray-500 mt-2">
                รองรับ: รูปภาพ, วิดีโอ, PDF (สูงสุด 50MB)
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
```

## Response Format

### สำเร็จ

```json
{
  "success": true,
  "message": "ดำเนินการสำเร็จ",
  "data": { ... }
}
```

### ผิดพลาด

```json
{
  "success": false,
  "message": "ข้อความข้อผิดพลาด"
}
```

## การทดสอบ

รันการทดสอบด้วยคำสั่ง:

```bash
bun test src/modules/media/media.service.test.ts
```

## Database Schema

### Media Collection

```typescript
{
  _id: string,
  siteId: string,
  filename: string,
  originalName: string,
  mimeType: string,
  size: number,
  path: string,
  url: string,
  type: 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other',
  category: 'product' | 'banner' | 'avatar' | 'content' | 'document' | 'other',
  alt?: string,
  title?: string,
  description?: string,
  tags: string[],
  metadata: {
    width?: number,
    height?: number,
    duration?: number,
    pages?: number,
    compression?: string,
    colorSpace?: string,
    format?: string,
    quality?: number
  },
  thumbnails?: {
    small?: string,
    medium?: string,
    large?: string
  },
  isPublic: boolean,
  isOptimized: boolean,
  uploadedBy: string,
  usageCount: number,
  lastUsed?: Date,
  expiresAt?: Date,
  createdAt: Date,
  updatedAt: Date
}
```

## การกำหนดค่า

### ขนาดไฟล์สูงสุด

- ไฟล์ทั่วไป: 50MB
- สามารถปรับแต่งได้ใน MediaService

### โฟลเดอร์เก็บไฟล์

- ตำแหน่ง: `public/uploads/{siteId}/{YYYY-MM}/`
- จัดเรียงตามเว็บไซต์และเดือน

### รูปแบบ Thumbnail

- Small: 150x150px
- Medium: 300x300px
- Large: 600x600px
- รูปแบบ: WebP (ประหยัดพื้นที่)

## หมายเหตุ

- เฉพาะ Admin เท่านั้นที่สามารถอัพโหลดและจัดการไฟล์ได้
- ระบบจะสร้าง thumbnail อัตโนมัติสำหรับรูปภาพ
- รองรับการแปลงรูปแบบและปรับคุณภาพ
- มีระบบติดตามการใช้งานและสถิติ
- รองรับการหมดอายุอัตโนมัติ
- ระบบค้นหาแบบ full-text search
