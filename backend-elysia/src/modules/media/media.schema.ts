import { t } from 'elysia';

export const uploadMediaSchema = t.Object({
  category: t.Optional(
    t.Union([
      t.Literal('product'),
      t.Literal('banner'),
      t.Literal('avatar'),
      t.Literal('content'),
      t.Literal('document'),
      t.Literal('other'),
    ]),
  ),
  alt: t.Optional(t.String({ maxLength: 200 })),
  title: t.Optional(t.String({ maxLength: 200 })),
  description: t.Optional(t.String({ maxLength: 500 })),
  tags: t.Optional(t.Array(t.String({ maxLength: 50 }))),
  isPublic: t.Optional(t.<PERSON>an()),
  expiresAt: t.Optional(t.Date()),
});

export const updateMediaSchema = t.Object({
  alt: t.Optional(t.String({ maxLength: 200 })),
  title: t.Optional(t.String({ maxLength: 200 })),
  description: t.Optional(t.String({ maxLength: 500 })),
  tags: t.Optional(t.Array(t.String({ maxLength: 50 }))),
  category: t.Optional(
    t.Union([
      t.Literal('product'),
      t.Literal('banner'),
      t.Literal('avatar'),
      t.Literal('content'),
      t.Literal('document'),
      t.Literal('other'),
    ]),
  ),
  isPublic: t.Optional(t.Boolean()),
  expiresAt: t.Optional(t.Date()),
});

export const mediaQuerySchema = t.Object({
  type: t.Optional(
    t.Union([
      t.Literal('image'),
      t.Literal('video'),
      t.Literal('audio'),
      t.Literal('document'),
      t.Literal('archive'),
      t.Literal('other'),
    ]),
  ),
  category: t.Optional(
    t.Union([
      t.Literal('product'),
      t.Literal('banner'),
      t.Literal('avatar'),
      t.Literal('content'),
      t.Literal('document'),
      t.Literal('other'),
    ]),
  ),
  isPublic: t.Optional(t.Boolean()),
  isOptimized: t.Optional(t.Boolean()),
  uploadedBy: t.Optional(t.String()),
  tags: t.Optional(t.String()),
  search: t.Optional(t.String()),
  minSize: t.Optional(t.Number({ minimum: 0 })),
  maxSize: t.Optional(t.Number({ minimum: 0 })),
  startDate: t.Optional(t.Date()),
  endDate: t.Optional(t.Date()),
  sortBy: t.Optional(
    t.Union([t.Literal('createdAt'), t.Literal('size'), t.Literal('usageCount'), t.Literal('filename')]),
  ),
  sortOrder: t.Optional(t.Union([t.Literal('asc'), t.Literal('desc')])),
  page: t.Optional(t.Number({ minimum: 1 })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
});

export const bulkActionSchema = t.Object({
  mediaIds: t.Array(t.String({ minLength: 1 })),
  action: t.Union([
    t.Literal('delete'),
    t.Literal('optimize'),
    t.Literal('makePublic'),
    t.Literal('makePrivate'),
    t.Literal('addTags'),
    t.Literal('removeTags'),
    t.Literal('changeCategory'),
  ]),
  data: t.Optional(
    t.Object({
      tags: t.Optional(t.Array(t.String())),
      category: t.Optional(
        t.Union([
          t.Literal('product'),
          t.Literal('banner'),
          t.Literal('avatar'),
          t.Literal('content'),
          t.Literal('document'),
          t.Literal('other'),
        ]),
      ),
    }),
  ),
});

export const resizeImageSchema = t.Object({
  width: t.Optional(t.Number({ minimum: 1, maximum: 4000 })),
  height: t.Optional(t.Number({ minimum: 1, maximum: 4000 })),
  quality: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
  format: t.Optional(t.Union([t.Literal('jpeg'), t.Literal('png'), t.Literal('webp'), t.Literal('avif')])),
  fit: t.Optional(
    t.Union([t.Literal('cover'), t.Literal('contain'), t.Literal('fill'), t.Literal('inside'), t.Literal('outside')]),
  ),
});

export const generateThumbnailsSchema = t.Object({
  sizes: t.Optional(
    t.Object({
      small: t.Optional(
        t.Object({
          width: t.Number({ minimum: 1 }),
          height: t.Number({ minimum: 1 }),
        }),
      ),
      medium: t.Optional(
        t.Object({
          width: t.Number({ minimum: 1 }),
          height: t.Number({ minimum: 1 }),
        }),
      ),
      large: t.Optional(
        t.Object({
          width: t.Number({ minimum: 1 }),
          height: t.Number({ minimum: 1 }),
        }),
      ),
    }),
  ),
  quality: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
  format: t.Optional(t.Union([t.Literal('jpeg'), t.Literal('png'), t.Literal('webp'), t.Literal('avif')])),
});
