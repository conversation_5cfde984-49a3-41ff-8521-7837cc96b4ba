import { AddonService } from '../addon/addon.service';
import { validateSiteAccess } from '../site/site.service';
import { Blog, BlogComment } from './content.model';

export class BlogService {
  private addonService: AddonService;

  constructor() {
    this.addonService = new AddonService();
  }

  // ดึงรายการบล็อก
  async getBlogsList(siteId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าเปิดใช้งาน addon blog หรือไม่
      const isActive = await this.addonService.isAddonActive(siteId, 'blog');
      if (!isActive) {
        throw new Error('ระบบบล็อกยังไม่ได้เปิดใช้งาน');
      }

      const blogs = await Blog.find({ siteId })
        .populate('authorId', 'name')
        .sort({ createdAt: -1 });

      return {
        success: true,
        data: blogs,
      };
    }
    catch (error) {
      console.error('Error getting blogs list:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถดึงรายการบล็อกได้');
    }
  }

  // สร้างบล็อกใหม่
  async createBlog(siteId: string, blogData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าเปิดใช้งาน addon blog หรือไม่
      const isActive = await this.addonService.isAddonActive(siteId, 'blog');
      if (!isActive) {
        throw new Error('ระบบบล็อกยังไม่ได้เปิดใช้งาน');
      }

      // สร้าง slug จากหัวข้อ
      const slug = this.generateSlug(blogData.title);

      const newBlog = new Blog({
        siteId,
        title: blogData.title,
        slug,
        content: blogData.content,
        excerpt: blogData.excerpt || this.generateExcerpt(blogData.content),
        tags: blogData.tags || [],
        featuredImage: blogData.featuredImage || null,
        published: blogData.published || false,
        authorId: userId,
        publishedAt: blogData.published ? new Date() : null,
      });

      await newBlog.save();

      return {
        success: true,
        message: 'สร้างบล็อกเรียบร้อยแล้ว',
        data: newBlog,
      };
    }
    catch (error) {
      console.error('Error creating blog:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถสร้างบล็อกได้');
    }
  }

  // อัปเดตบล็อก
  async updateBlog(siteId: string, blogId: string, blogData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าบล็อกมีอยู่จริง
      const existingBlog = await Blog.findOne({ _id: blogId, siteId });

      if (!existingBlog) {
        throw new Error('ไม่พบบล็อกที่ระบุ');
      }

      const updateData = {
        title: blogData.title,
        slug: this.generateSlug(blogData.title),
        content: blogData.content,
        excerpt: blogData.excerpt || this.generateExcerpt(blogData.content),
        tags: blogData.tags || [],
        featuredImage: blogData.featuredImage || null,
        published: blogData.published,
        publishedAt: blogData.published && !existingBlog.published ? new Date() : existingBlog.publishedAt,
      };

      await Blog.findByIdAndUpdate(blogId, updateData);

      return {
        success: true,
        message: 'อัปเดตบล็อกเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error updating blog:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถอัปเดตบล็อกได้');
    }
  }

  // ลบบล็อก
  async deleteBlog(siteId: string, blogId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าบล็อกมีอยู่จริง
      const existingBlog = await Blog.findOne({ _id: blogId, siteId });

      if (!existingBlog) {
        throw new Error('ไม่พบบล็อกที่ระบุ');
      }

      // ลบความคิดเห็นที่เกี่ยวข้อง
      await BlogComment.deleteMany({ blogId });

      // ลบบล็อก
      await Blog.findByIdAndDelete(blogId);

      return {
        success: true,
        message: 'ลบบล็อกเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error deleting blog:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถลบบล็อกได้');
    }
  }

  // เปลี่ยนสถานะการเผยแพร่
  async togglePublish(siteId: string, blogId: string, published: boolean, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      const updateData: any = { published };
      if (published) {
        updateData.publishedAt = new Date();
      }

      await Blog.findOneAndUpdate(
        { _id: blogId, siteId },
        updateData,
      );

      return {
        success: true,
        message: published ? 'เผยแพร่บล็อกเรียบร้อยแล้ว' : 'ยกเลิกการเผยแพร่บล็อกเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error toggling blog publish status:', error);
      throw new Error('ไม่สามารถเปลี่ยนสถานะการเผยแพร่ได้');
    }
  }

  // สร้าง slug จากข้อความ
  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  // สร้างสรุปเนื้อหา
  private generateExcerpt(content: string, length: number = 200): string {
    const plainText = content.replace(/<[^>]*>/g, '');
    return plainText.length > length
      ? plainText.substring(0, length) + '...'
      : plainText;
  }
}
