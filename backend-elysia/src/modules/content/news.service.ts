import { AddonService } from '../addon/addon.service';
import { validateSiteAccess } from '../site/site.service';
import { News, NewsCategory } from './content.model';

export class NewsService {
  private addonService: AddonService;

  constructor() {
    this.addonService = new AddonService();
  }

  // ดึงรายการข่าว
  async getNewsList(siteId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าเปิดใช้งาน addon news หรือไม่
      const isActive = await this.addonService.isAddonActive(siteId, 'news');
      if (!isActive) {
        throw new Error('ระบบข่าวสารยังไม่ได้เปิดใช้งาน');
      }

      const news = await News.find({ siteId })
        .populate('categoryId', 'name')
        .populate('authorId', 'name')
        .sort({ createdAt: -1 });

      return {
        success: true,
        data: news,
      };
    }
    catch (error) {
      console.error('Error getting news list:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถดึงรายการข่าวได้');
    }
  }

  // สร้างข่าวใหม่
  async createNews(siteId: string, newsData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าเปิดใช้งาน addon news หรือไม่
      const isActive = await this.addonService.isAddonActive(siteId, 'news');
      if (!isActive) {
        throw new Error('ระบบข่าวสารยังไม่ได้เปิดใช้งาน');
      }

      // สร้าง slug จากหัวข้อ
      const slug = this.generateSlug(newsData.title);

      const newNews = new News({
        siteId,
        title: newsData.title,
        slug,
        content: newsData.content,
        excerpt: newsData.excerpt || this.generateExcerpt(newsData.content),
        categoryId: newsData.categoryId || null,
        featuredImage: newsData.featuredImage || null,
        published: newsData.published || false,
        authorId: userId,
        publishedAt: newsData.published ? new Date() : null,
      });

      await newNews.save();

      return {
        success: true,
        message: 'สร้างข่าวเรียบร้อยแล้ว',
        data: newNews,
      };
    }
    catch (error) {
      console.error('Error creating news:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถสร้างข่าวได้');
    }
  }

  // อัปเดตข่าว
  async updateNews(siteId: string, newsId: string, newsData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าข่าวมีอยู่จริง
      const existingNews = await News.findOne({ _id: newsId, siteId });

      if (!existingNews) {
        throw new Error('ไม่พบข่าวที่ระบุ');
      }

      const updateData = {
        title: newsData.title,
        slug: this.generateSlug(newsData.title),
        content: newsData.content,
        excerpt: newsData.excerpt || this.generateExcerpt(newsData.content),
        categoryId: newsData.categoryId || null,
        featuredImage: newsData.featuredImage || null,
        published: newsData.published,
        publishedAt: newsData.published && !existingNews.published ? new Date() : existingNews.publishedAt,
      };

      await News.findByIdAndUpdate(newsId, updateData);

      return {
        success: true,
        message: 'อัปเดตข่าวเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error updating news:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถอัปเดตข่าวได้');
    }
  }

  // ลบข่าว
  async deleteNews(siteId: string, newsId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าข่าวมีอยู่จริง
      const existingNews = await News.findOne({ _id: newsId, siteId });

      if (!existingNews) {
        throw new Error('ไม่พบข่าวที่ระบุ');
      }

      await News.findByIdAndDelete(newsId);

      return {
        success: true,
        message: 'ลบข่าวเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error deleting news:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถลบข่าวได้');
    }
  }

  // เปลี่ยนสถานะการเผยแพร่
  async togglePublish(siteId: string, newsId: string, published: boolean, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      const updateData: any = { published };
      if (published) {
        updateData.publishedAt = new Date();
      }

      await News.findOneAndUpdate(
        { _id: newsId, siteId },
        updateData,
      );

      return {
        success: true,
        message: published ? 'เผยแพร่ข่าวเรียบร้อยแล้ว' : 'ยกเลิกการเผยแพร่ข่าวเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error toggling news publish status:', error);
      throw new Error('ไม่สามารถเปลี่ยนสถานะการเผยแพร่ได้');
    }
  }

  // ดึงหมวดหมู่ข่าว
  async getCategories(siteId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      const categories = await NewsCategory.find({ siteId }).sort({ name: 1 });

      return {
        success: true,
        data: categories,
      };
    }
    catch (error) {
      console.error('Error getting news categories:', error);
      throw new Error('ไม่สามารถดึงหมวดหมู่ข่าวได้');
    }
  }

  // สร้างหมวดหมู่ข่าวใหม่
  async createCategory(siteId: string, categoryData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      const newCategory = new NewsCategory({
        siteId,
        name: categoryData.name,
        slug: this.generateSlug(categoryData.name),
        description: categoryData.description || '',
      });

      await newCategory.save();

      return {
        success: true,
        message: 'สร้างหมวดหมู่เรียบร้อยแล้ว',
        data: newCategory,
      };
    }
    catch (error) {
      console.error('Error creating news category:', error);
      throw new Error('ไม่สามารถสร้างหมวดหมู่ได้');
    }
  }

  // สร้าง slug จากข้อความ
  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  // สร้างสรุปเนื้อหา
  private generateExcerpt(content: string, length: number = 200): string {
    const plainText = content.replace(/<[^>]*>/g, '');
    return plainText.length > length
      ? plainText.substring(0, length) + '...'
      : plainText;
  }
}
