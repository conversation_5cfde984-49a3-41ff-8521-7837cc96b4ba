import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia, t } from 'elysia';
import { invitationQuerySchema } from './invitation.schema';
import {
  acceptInvitation,
  cancelInvitation,
  getInvitationByCode,
  getInvitationById,
  getReceivedInvitations,
  rejectInvitation,
  resendInvitation,
} from './invitation.service';

export const invitationRoutes = new Elysia({ prefix: '/invitations' })
  .use(userAuthPlugin)
  // ดูคำเชิญที่ได้รับ
  .get(
    '/received',
    async ({ query, user }: any) => {
      const userId = user._id;
      const result = await getReceivedInvitations(userId, query);
      return result;
    },
    {
      query: invitationQuerySchema,
    },
  )
  // ดูคำเชิญเฉพาะ
  .get(
    '/:id',
    async ({ params, user }: any) => {
      const { id: invitationId } = params;
      const userId = user._id;

      const result = await getInvitationById(invitationId, userId);
      return result;
    },
    {
      params: t.Object({ id: t.String() }),
    },
  )
  // รับคำเชิญด้วยโค้ด (ไม่ต้อง login)
  .get(
    '/code/:inviteCode',
    async ({ params }: any) => {
      const { inviteCode } = params;

      const result = await getInvitationByCode(inviteCode);
      return result;
    },
    {
      params: t.Object({ inviteCode: t.String() }),
    },
  )
  // รับคำเชิญ
  .post(
    '/:id/accept',
    async ({ params, user }: any) => {
      console.log('🚀 Route: Accepting invitation - START');
      console.log('📋 Raw params:', params);

      const { id: invitationId } = params;
      const userId = user._id;

      console.log('� Extracted invitationId:', invitationId);
      console.log('📋 invitationId type:', typeof invitationId);
      console.log('📋 invitationId length:', invitationId?.length);
      console.log('📋 userId:', userId);

      try {
        const result = await acceptInvitation(invitationId, userId);
        console.log('✅ acceptInvitation result:', result);

        return {
          success: true,
          message: 'ยืนยันคำเชิญเรียบร้อยแล้ว',
          statusMessage: 'ยืนยันคำเชิญเรียบร้อยแล้ว',
          timestamp: new Date().toISOString(),
          data: result,
        };
      }
      catch (error) {
        console.error('❌ Error in accept route:', error);
        throw error;
      }
    },
    {
      params: t.Object({ id: t.String() }),
    },
  )
  // ปฏิเสธคำเชิญ
  .post(
    '/:id/reject',
    async ({ params, user }: any) => {
      const { id: invitationId } = params;
      const userId = user._id;

      const result = await rejectInvitation(invitationId, userId);
      return result;
    },
    {
      params: t.Object({ id: t.String() }),
    },
  )
  // ยกเลิกคำเชิญ
  .delete(
    '/:id',
    async ({ params, user }: any) => {
      const { id: invitationId } = params;
      const fromUserId = user._id;

      const result = await cancelInvitation(invitationId, fromUserId);
      return result;
    },
    {
      params: t.Object({ id: t.String() }),
    },
  )
  // ส่งคำเชิญใหม่
  .post(
    '/:id/resend',
    async ({ params, user }: any) => {
      const { id: invitationId } = params;
      const fromUserId = user._id;

      const result = await resendInvitation(invitationId, fromUserId);
      return result;
    },
    {
      params: t.Object({ id: t.String() }),
    },
  );
