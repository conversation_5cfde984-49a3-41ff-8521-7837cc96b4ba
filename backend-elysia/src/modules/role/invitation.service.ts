import { Site } from '@/modules/site/site.model';
import { User } from '@/modules/user/user.model';
import { Invitation } from './invitation.model';
import { Role, type SiteRole } from './role.model';
import { HttpError } from './role.service';

// สร้างคำเชิญ
export async function createInvitation(
  siteId: string,
  fromUserId: string,
  toUserId?: string,
  role?: SiteRole,
  message?: string,
  toEmail?: string,
): Promise<any> {
  // ตรวจสอบว่าผู้ส่งมีสิทธิ์เชิญหรือไม่
  const senderRole = await Role.findOne({ siteId, userId: fromUserId });
  if (!senderRole || (senderRole.role !== 'owner' && senderRole.role !== 'admin')) {
    throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งคำเชิญ');
  }

  // ถ้ามี toEmail ให้ค้นหา user จาก email
  if (toEmail && !toUserId) {
    const targetUser = await User.findOne({ email: toEmail });
    if (targetUser) {
      toUserId = targetUser._id.toString();
    }
  }

  // ตรวจสอบว่าผู้รับมี role ในเว็บไซต์นี้แล้วหรือไม่ (ถ้ามี toUserId)
  if (toUserId) {
    const existingRole = await Role.findOne({ siteId, userId: toUserId });
    if (existingRole) {
      throw new HttpError(409, 'ผู้ใช้มีบทบาทในเว็บไซต์นี้แล้ว');
    }

    // ตรวจสอบว่ามีคำเชิญที่รอดำเนินการอยู่หรือไม่
    const existingInvitation = await Invitation.findOne({
      siteId,
      toUserId,
      status: 'pending',
      expiresAt: { $gt: new Date() },
    });
    if (existingInvitation) {
      throw new HttpError(409, 'มีคำเชิญที่รอดำเนินการอยู่แล้ว');
    }

    // ตรวจสอบว่า toUserId มีอยู่จริง
    const targetUser = await User.findById(toUserId);
    if (!targetUser) {
      throw new HttpError(404, 'ไม่พบผู้ใช้ที่ต้องการเชิญ');
    }
  }
  else if (toEmail) {
    // ตรวจสอบว่ามีคำเชิญที่รอดำเนินการสำหรับ email นี้หรือไม่
    const existingInvitation = await Invitation.findOne({
      siteId,
      toEmail,
      status: 'pending',
      expiresAt: { $gt: new Date() },
    });
    if (existingInvitation) {
      throw new HttpError(409, 'มีคำเชิญที่รอดำเนินการสำหรับอีเมลนี้อยู่แล้ว');
    }
  }

  const invitation = new Invitation({
    siteId,
    fromUserId,
    toUserId,
    toEmail,
    role: role || 'viewer',
    message,
    status: 'pending',
  });

  await invitation.save();

  // TODO: ส่งอีเมลแจ้งเตือน
  // await sendInvitationEmail(invitation);

  // TODO: ส่งการแจ้งเตือนในระบบ (ถ้ามี toUserId)
  if (toUserId) {
    try {
      const { createNotification } = await import('@/modules/notification/notification.service');
      await createNotification(toUserId, {
        type: 'team_invitation',
        title: 'คำเชิญเข้าร่วมทีมงาน',
        message: `คุณได้รับคำเชิญให้เข้าร่วมทีมงานในฐานะ ${role}`,
        data: {
          invitationId: invitation._id,
          siteId,
          role,
          fromUserId,
        },
      });
    }
    catch (error) {
      console.error('Failed to create notification:', error);
      // ไม่ throw error เพราะคำเชิญยังสร้างสำเร็จ
    }
  }

  return {
    success: true,
    message: 'ส่งคำเชิญสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: invitation,
  };
}

// รับคำเชิญ
export async function acceptInvitation(invitationId: string, userId: string): Promise<any> {
  console.log('🔍 Accepting invitation:', { invitationId, userId });

  // ตรวจสอบว่า invitationId ไม่ว่าง
  if (!invitationId || invitationId.trim().length === 0) {
    console.log('❌ Empty invitation ID');
    throw new HttpError(400, 'รหัสคำเชิญไม่ถูกต้อง');
  }

  const invitation = await Invitation.findById(invitationId);
  console.log('📋 Found invitation:', invitation ? 'YES' : 'NO');

  if (!invitation) {
    console.log('❌ Invitation not found:', invitationId);
    throw new HttpError(404, 'ไม่พบคำเชิญ');
  }

  // ตรวจสอบสิทธิ์ - อนุญาตให้รับคำเชิญได้ถ้า:
  // 1. toUserId ตรงกับ userId ที่ส่งมา
  // 2. หรือ toEmail ตรงกับ email ของ user ที่ส่งมา (สำหรับคำเชิญที่ส่งทาง email)
  // 3. หรือไม่มี toUserId และ toEmail (สำหรับคำเชิญที่แชร์ลิงก์)
  const currentUser = await User.findById(userId);
  if (!currentUser) {
    throw new HttpError(404, 'ไม่พบผู้ใช้');
  }

  let canAccept = false;

  console.log('🔍 Checking permissions:', {
    invitationToUserId: invitation.toUserId,
    invitationToEmail: invitation.toEmail,
    currentUserId: userId,
    currentUserEmail: currentUser.email,
  });

  // กรณีที่เชิญโดยตรง (มี toUserId หรือ toEmail)
  if (invitation.toUserId || invitation.toEmail) {
    canAccept = (invitation.toUserId === userId)
      || Boolean(invitation.toEmail && invitation.toEmail === currentUser.email);
  }
  else {
    // กรณีที่แชร์ลิงก์ (ไม่มี toUserId และ toEmail) - อนุญาตให้ใครก็ได้
    canAccept = true;
  }

  console.log('✅ Can accept:', canAccept);

  if (!canAccept) {
    console.log('❌ User cannot accept this invitation');
    throw new HttpError(403, 'คำเชิญนี้ไม่ใช่ของคุณ');
  }

  console.log('📊 Invitation status:', invitation.status);
  console.log('⏰ Expires at:', invitation.expiresAt);
  console.log('🕐 Current time:', new Date());

  if (invitation.status !== 'pending') {
    console.log('❌ Invitation already processed:', invitation.status);
    throw new HttpError(400, 'คำเชิญนี้ได้ดำเนินการแล้ว');
  }

  if (invitation.expiresAt < new Date()) {
    console.log('❌ Invitation expired');
    invitation.status = 'expired';
    await invitation.save();
    throw new HttpError(400, 'คำเชิญหมดอายุแล้ว');
  }

  // ตรวจสอบว่าผู้ใช้มี role ในเว็บไซต์นี้แล้วหรือไม่
  const existingRole = await Role.findOne({ siteId: invitation.siteId, userId });
  console.log('🔍 Checking existing role:', {
    siteId: invitation.siteId,
    userId,
    existingRole: existingRole ? 'YES' : 'NO',
  });

  if (existingRole) {
    console.log('❌ User already has role in this site');
    throw new HttpError(409, 'คุณมีบทบาทในเว็บไซต์นี้แล้ว');
  }

  // สร้าง role ใหม่
  const newRole = new Role({
    siteId: invitation.siteId,
    userId,
    role: invitation.role,
  });

  await newRole.save();

  // อัพเดตสถานะคำเชิญและ toUserId (สำหรับกรณีที่เชิญทาง email หรือลิงก์)
  invitation.status = 'accepted';
  if (!invitation.toUserId) {
    invitation.toUserId = userId;
  }
  await invitation.save();

  return {
    success: true,
    message: 'รับคำเชิญสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: { invitation, role: newRole },
  };
}

// ปฏิเสธคำเชิญ
export async function rejectInvitation(invitationId: string, userId: string): Promise<any> {
  console.log('🔍 Rejecting invitation:', { invitationId, userId });

  // ตรวจสอบว่า invitationId ไม่ว่าง
  if (!invitationId || invitationId.trim().length === 0) {
    console.log('❌ Empty invitation ID');
    throw new HttpError(400, 'รหัสคำเชิญไม่ถูกต้อง');
  }

  const invitation = await Invitation.findById(invitationId);
  console.log('📋 Found invitation:', invitation ? 'YES' : 'NO');

  if (!invitation) {
    console.log('❌ Invitation not found:', invitationId);
    throw new HttpError(404, 'ไม่พบคำเชิญ');
  }

  // ตรวจสอบสิทธิ์ - อนุญาตให้ปฏิเสธคำเชิญได้ถ้า:
  // 1. toUserId ตรงกับ userId ที่ส่งมา
  // 2. หรือ toEmail ตรงกับ email ของ user ที่ส่งมา (สำหรับคำเชิญที่ส่งทาง email)
  // 3. หรือไม่มี toUserId และ toEmail (สำหรับคำเชิญที่แชร์ลิงก์)
  const currentUser = await User.findById(userId);
  if (!currentUser) {
    throw new HttpError(404, 'ไม่พบผู้ใช้');
  }

  let canReject = false;

  // กรณีที่เชิญโดยตรง (มี toUserId หรือ toEmail)
  if (invitation.toUserId || invitation.toEmail) {
    canReject = (invitation.toUserId === userId)
      || Boolean(invitation.toEmail && invitation.toEmail === currentUser.email);
  }
  else {
    // กรณีที่แชร์ลิงก์ (ไม่มี toUserId และ toEmail) - อนุญาตให้ใครก็ได้
    canReject = true;
  }

  if (!canReject) {
    throw new HttpError(403, 'คำเชิญนี้ไม่ใช่ของคุณ');
  }

  if (invitation.status !== 'pending') {
    throw new HttpError(400, 'คำเชิญนี้ได้ดำเนินการแล้ว');
  }

  invitation.status = 'rejected';
  // อัพเดต toUserId สำหรับกรณีที่เชิญทาง email หรือลิงก์
  if (!invitation.toUserId) {
    invitation.toUserId = userId;
  }
  await invitation.save();

  return {
    success: true,
    message: 'ปฏิเสธคำเชิญสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: { invitation },
  };
}

// ดูคำเชิญที่ส่งออกไป
export async function getSentInvitations(siteId: string, fromUserId: string, query: any = {}): Promise<any> {
  const { status, role, page = 1, limit = 20 } = query;
  const skip = (page - 1) * limit;

  const filter: any = { siteId, fromUserId };
  if (status) filter.status = status;
  if (role) filter.role = role;

  const invitations = await Invitation.find(filter)
    .populate('toUserId', 'email avatar')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Invitation.countDocuments(filter);

  return {
    success: true,
    message: 'ดึงคำเชิญที่ส่งสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: {
      invitations,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    },
  };
}

// ดูคำเชิญที่ได้รับ
export async function getReceivedInvitations(userId: string, query: any = {}): Promise<any> {
  const { status, role, page = 1, limit = 20 } = query;
  const skip = (page - 1) * limit;

  // ดึงข้อมูล user เพื่อเอา email
  const currentUser = await User.findById(userId);
  if (!currentUser) {
    throw new HttpError(404, 'ไม่พบผู้ใช้');
  }

  const filter: any = {
    $or: [
      { toUserId: userId },
      { toEmail: currentUser.email }, // เฉพาะคำเชิญที่ส่งมาที่ email ของ user นี้
    ],
  };

  // ถ้าระบุ status ให้กรองตาม status
  if (status) {
    filter.status = status;
  }
  else {
    // ถ้าไม่ระบุ status ให้แสดงเฉพาะ pending และยังไม่หมดอายุ
    filter.status = 'pending';
    filter.expiresAt = { $gt: new Date() };
  }

  if (role) filter.role = role;

  const invitations = await Invitation.find(filter)
    .populate('fromUserId', 'email avatar firstName lastName')
    .populate({
      path: 'siteId',
      select: 'name description logo',
      model: 'Site',
    })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Invitation.countDocuments(filter);

  // เพิ่มข้อมูลผู้ส่งและเว็บไซต์
  const enrichedInvitations = invitations.map(invitation => ({
    ...invitation.toObject(),
    fromUserName: (invitation.fromUserId as any)?.firstName && (invitation.fromUserId as any)?.lastName
      ? `${(invitation.fromUserId as any).firstName} ${(invitation.fromUserId as any).lastName}`
      : (invitation.fromUserId as any)?.email || 'Unknown User',
    fromUserEmail: (invitation.fromUserId as any)?.email || '',
    siteName: (invitation.siteId as any)?.name || 'Unknown Site',
  }));

  return {
    success: true,
    message: 'ดึงคำเชิญที่ได้รับสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: {
      invitations: enrichedInvitations,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    },
  };
}

// ยกเลิกคำเชิญ
export async function cancelInvitation(invitationId: string, fromUserId: string): Promise<any> {
  const invitation = await Invitation.findById(invitationId);

  if (!invitation) {
    throw new HttpError(404, 'ไม่พบคำเชิญ');
  }

  if (invitation.fromUserId !== fromUserId) {
    throw new HttpError(403, 'คุณไม่ใช่ผู้ส่งคำเชิญนี้');
  }

  if (invitation.status !== 'pending') {
    throw new HttpError(400, 'ไม่สามารถยกเลิกคำเชิญที่ดำเนินการแล้ว');
  }

  await invitation.deleteOne();

  return {
    success: true,
    message: 'ยกเลิกคำเชิญสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
  };
}
// ดูคำเชิญเฉพาะ
export async function getInvitationById(invitationId: string, userId: string): Promise<any> {
  const invitation = await Invitation.findById(invitationId)
    .populate('fromUserId', 'email avatar')
    .populate('toUserId', 'email avatar')
    .populate({
      path: 'siteId',
      select: 'name description logo',
      model: 'Site',
    });

  if (!invitation) {
    throw new HttpError(404, 'ไม่พบคำเชิญ');
  }

  // ตรวจสอบสิทธิ์ (ต้องเป็นผู้ส่งหรือผู้รับ)
  if (invitation.fromUserId !== userId && invitation.toUserId !== userId) {
    throw new HttpError(403, 'คุณไม่มีสิทธิ์ดูคำเชิญนี้');
  }

  return {
    success: true,
    message: 'ดึงข้อมูลคำเชิญสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: invitation,
  };
}

// สร้างคำเชิญหลายรายการ
export async function createBulkInvitations(
  siteId: string,
  fromUserId: string,
  invitations: Array<{
    toUserId?: string;
    toEmail?: string;
    role: SiteRole;
    message?: string;
  }>,
): Promise<any> {
  // ตรวจสอบสิทธิ์
  const senderRole = await Role.findOne({ siteId, userId: fromUserId });
  if (!senderRole || (senderRole.role !== 'owner' && senderRole.role !== 'admin')) {
    throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งคำเชิญ');
  }

  const results = [];
  const errors = [];

  for (let i = 0; i < invitations.length; i++) {
    const invitationData = invitations[i];

    try {
      // ตรวจสอบว่าผู้รับมี role ในเว็บไซต์นี้แล้วหรือไม่
      if (invitationData.toUserId) {
        const existingRole = await Role.findOne({ siteId, userId: invitationData.toUserId });
        if (existingRole) {
          errors.push({
            index: i,
            error: 'ผู้ใช้มีบทบาทในเว็บไซต์นี้แล้ว',
            data: invitationData,
          });
          continue;
        }

        // ตรวจสอบคำเชิญที่รอดำเนินการ
        const existingInvitation = await Invitation.findOne({
          siteId,
          toUserId: invitationData.toUserId,
          status: 'pending',
          expiresAt: { $gt: new Date() },
        });
        if (existingInvitation) {
          errors.push({
            index: i,
            error: 'มีคำเชิญที่รอดำเนินการอยู่แล้ว',
            data: invitationData,
          });
          continue;
        }
      }

      const invitation = new Invitation({
        siteId,
        fromUserId,
        toUserId: invitationData.toUserId,
        toEmail: invitationData.toEmail,
        role: invitationData.role,
        message: invitationData.message,
        status: 'pending',
      });

      await invitation.save();
      results.push(invitation);

      // TODO: ส่งอีเมลแจ้งเตือน
      // await sendInvitationEmail(invitation);
    }
    catch (error: any) {
      errors.push({
        index: i,
        error: error.message,
        data: invitationData,
      });
    }
  }

  return {
    success: true,
    message: `ส่งคำเชิญสำเร็จ ${results.length} รายการ${errors.length > 0 ? `, ล้มเหลว ${errors.length} รายการ` : ''}`,
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: {
      successful: results,
      failed: errors,
      summary: {
        total: invitations.length,
        successful: results.length,
        failed: errors.length,
      },
    },
  };
}

// ส่งคำเชิญใหม่
export async function resendInvitation(invitationId: string, fromUserId: string): Promise<any> {
  const invitation = await Invitation.findById(invitationId);

  if (!invitation) {
    throw new HttpError(404, 'ไม่พบคำเชิญ');
  }

  if (invitation.fromUserId !== fromUserId) {
    throw new HttpError(403, 'คุณไม่ใช่ผู้ส่งคำเชิญนี้');
  }

  if (invitation.status !== 'pending') {
    throw new HttpError(400, 'ไม่สามารถส่งคำเชิญที่ดำเนินการแล้วใหม่ได้');
  }

  // อัปเดตวันหมดอายุ
  invitation.expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 วันใหม่
  await invitation.save();

  // TODO: ส่งอีเมลแจ้งเตือนใหม่
  // await sendInvitationEmail(invitation);

  return {
    success: true,
    message: 'ส่งคำเชิญใหม่สำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: invitation,
  };
}

// ฟังก์ชันสำหรับทำความสะอาดคำเชิญที่หมดอายุ
export async function cleanupExpiredInvitations(): Promise<any> {
  const result = await Invitation.updateMany(
    {
      status: 'pending',
      expiresAt: { $lt: new Date() },
    },
    {
      $set: { status: 'expired' },
    },
  );

  return {
    success: true,
    message: `อัปเดตคำเชิญที่หมดอายุ ${result.modifiedCount} รายการ`,
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: {
      modifiedCount: result.modifiedCount,
    },
  };
}

// ดูสถิติคำเชิญ
export async function getInvitationStats(siteId: string, fromUserId: string): Promise<any> {
  const stats = await Invitation.aggregate([
    { $match: { siteId, fromUserId } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
      },
    },
  ]);

  const totalInvitations = await Invitation.countDocuments({ siteId, fromUserId });
  const recentInvitations = await Invitation.countDocuments({
    siteId,
    fromUserId,
    createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // 30 วันที่แล้ว
  });

  const formattedStats = {
    pending: 0,
    accepted: 0,
    rejected: 0,
    expired: 0,
  };

  stats.forEach(stat => {
    formattedStats[stat._id as keyof typeof formattedStats] = stat.count;
  });

  return {
    success: true,
    message: 'ดึงสถิติคำเชิญสำเร็จ',
    statusMessage: 'สำเร็จ!',
    timestamp: new Date().toISOString(),
    data: {
      byStatus: formattedStats,
      total: totalInvitations,
      recent: recentInvitations,
    },
  };
}

// รับคำเชิญด้วยโค้ด
export async function getInvitationByCode(inviteCode: string): Promise<any> {
  const invitation = await Invitation.findById(inviteCode);

  if (!invitation) {
    throw new HttpError(404, 'ไม่พบคำเชิญ');
  }

  if (invitation.status !== 'pending') {
    throw new HttpError(400, 'คำเชิญนี้ได้ดำเนินการแล้ว');
  }

  if (invitation.expiresAt < new Date()) {
    invitation.status = 'expired';
    await invitation.save();
    throw new HttpError(400, 'คำเชิญหมดอายุแล้ว');
  }

  // ดึงข้อมูลเพิ่มเติม
  const site = await Site.findById(invitation.siteId);
  const fromUser = await User.findById(invitation.fromUserId);

  return {
    success: true,
    data: {
      invitation: {
        _id: invitation._id,
        siteId: invitation.siteId,
        siteName: (site as any)?.name || 'Unknown Site',
        fromUserId: invitation.fromUserId,
        fromUserName: fromUser?.firstName + ' ' + fromUser?.lastName || 'Unknown User',
        fromUserEmail: fromUser?.email || '',
        role: invitation.role,
        message: invitation.message,
        status: invitation.status,
        createdAt: invitation.createdAt,
        expiresAt: invitation.expiresAt,
      },
    },
  };
}
