import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export type InvitationStatus = 'pending' | 'accepted' | 'rejected' | 'expired';
export type SiteRole = 'owner' | 'admin' | 'editor' | 'viewer';

export interface IInvitation extends Document {
  _id: string;
  siteId: string;
  fromUserId: string; // ผู้ส่งคำเชิญ
  toUserId: string; // ผู้รับคำเชิญ
  toEmail?: string; // อีเมลของผู้รับ (กรณีที่ยังไม่มี account)
  role: SiteRole;
  status: InvitationStatus;
  limit: number;
  message?: string; // ข้อความเพิ่มเติม
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const invitationSchema = new Schema<IInvitation>(
  {
    _id: { type: String, default: () => generateFileId(8) },
    siteId: { type: String, required: true, index: true },
    fromUserId: { type: String, required: true },
    toUserId: { type: String },
    toEmail: { type: String },
    role: {
      type: String,
      enum: ['owner', 'admin', 'editor', 'viewer'],
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'accepted', 'rejected', 'expired'],
      default: 'pending',
    },
    limit: {
      type: Number,
      default: 1,
    },
    message: { type: String },
    expiresAt: {
      type: Date,
      default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 วัน
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

invitationSchema.index({ siteId: 1, toUserId: 1 });
invitationSchema.index({ siteId: 1, toEmail: 1 });
invitationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

export const Invitation = mongoose.model<IInvitation>('Invitation', invitationSchema);
