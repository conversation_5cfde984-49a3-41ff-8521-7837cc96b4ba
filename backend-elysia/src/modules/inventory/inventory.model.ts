import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export type InventoryAction = 'in' | 'out' | 'adjust' | 'transfer' | 'damage' | 'return';
export type InventoryStatus = 'active' | 'inactive' | 'discontinued';

export interface IInventoryItem {
  _id: string;
  productId: string;
  variantId?: string;
  sku: string;
  quantity: number;
  reservedQuantity: number; // สต็อกที่ถูกจองไว้
  availableQuantity: number; // สต็อกที่พร้อมขาย
  lowStockThreshold: number; // เกณฑ์แจ้งเตือนสต็อกต่ำ
  reorderPoint: number; // จุดสั่งซื้อใหม่
  reorderQuantity: number; // จำนวนสั่งซื้อใหม่
  costPrice: number;
  lastPurchasePrice: number;
  lastPurchaseDate?: Date;
  supplierId?: string;
  location?: string; // ตำแหน่งในคลัง
  expiryDate?: Date;
  batchNumber?: string;
}

export interface IInventoryTransaction {
  _id: string;
  siteId: string;
  productId: string;
  variantId?: string;
  action: InventoryAction;
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  unitCost: number;
  totalCost: number;
  referenceId?: string; // orderId, transferId, etc.
  referenceType?: string; // 'order', 'transfer', 'adjustment', etc.
  notes?: string;
  performedBy: string; // userId
  performedAt: Date;
}

export interface IInventory extends Document {
  _id: string;
  siteId: string;

  // Product info
  productId: string;
  variantId?: string;
  sku: string;

  // Stock levels
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;

  // Thresholds
  lowStockThreshold: number;
  reorderPoint: number;
  reorderQuantity: number;

  // Cost tracking
  costPrice: number;
  lastPurchasePrice: number;
  lastPurchaseDate?: Date;

  // Supplier info
  supplierId?: string;
  supplierName?: string;

  // Location & batch
  location?: string;
  expiryDate?: Date;
  batchNumber?: string;

  // Status
  status: InventoryStatus;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  lastUpdated: Date;
}

const _inventoryItemSchema = new Schema(
  {
    _id: { type: String, default: () => generateFileId(3) },
    productId: { type: String, required: true },
    variantId: { type: String },
    sku: { type: String, required: true },
    quantity: { type: Number, required: true, default: 0 },
    reservedQuantity: { type: Number, default: 0 },
    availableQuantity: { type: Number, default: 0 },
    lowStockThreshold: { type: Number, default: 10 },
    reorderPoint: { type: Number, default: 5 },
    reorderQuantity: { type: Number, default: 50 },
    costPrice: { type: Number, default: 0 },
    lastPurchasePrice: { type: Number, default: 0 },
    lastPurchaseDate: { type: Date },
    supplierId: { type: String },
    location: { type: String },
    expiryDate: { type: Date },
    batchNumber: { type: String },
  },
  { _id: false },
);

const _inventoryTransactionSchema = new Schema(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    productId: { type: String, required: true, index: true },
    variantId: { type: String },
    action: {
      type: String,
      enum: ['in', 'out', 'adjust', 'transfer', 'damage', 'return'],
      required: true,
    },
    quantity: { type: Number, required: true },
    previousQuantity: { type: Number, required: true },
    newQuantity: { type: Number, required: true },
    unitCost: { type: Number, default: 0 },
    totalCost: { type: Number, default: 0 },
    referenceId: { type: String },
    referenceType: { type: String },
    notes: { type: String },
    performedBy: { type: String, required: true },
    performedAt: { type: Date, default: Date.now },
  },
  { _id: false },
);

const inventorySchema = new Schema<IInventory>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },

    // Product info
    productId: { type: String, required: true, index: true },
    variantId: { type: String, index: true },
    sku: { type: String, required: true, unique: true },

    // Stock levels
    quantity: { type: Number, required: true, default: 0 },
    reservedQuantity: { type: Number, default: 0 },
    availableQuantity: { type: Number, default: 0 },

    // Thresholds
    lowStockThreshold: { type: Number, default: 10 },
    reorderPoint: { type: Number, default: 5 },
    reorderQuantity: { type: Number, default: 50 },

    // Cost tracking
    costPrice: { type: Number, default: 0 },
    lastPurchasePrice: { type: Number, default: 0 },
    lastPurchaseDate: { type: Date },

    // Supplier info
    supplierId: { type: String },
    supplierName: { type: String },

    // Location & batch
    location: { type: String },
    expiryDate: { type: Date },
    batchNumber: { type: String },

    // Status
    status: {
      type: String,
      enum: ['active', 'inactive', 'discontinued'],
      default: 'active',
      index: true,
    },

    // Timestamps
    lastUpdated: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
inventorySchema.index({ siteId: 1, productId: 1 }, { unique: true });
inventorySchema.index({ siteId: 1, sku: 1 }, { unique: true });
inventorySchema.index({ siteId: 1, status: 1 });
inventorySchema.index({ siteId: 1, quantity: 1 });
inventorySchema.index({ siteId: 1, availableQuantity: 1 });

// Methods
inventorySchema.methods.updateStock = function(
  quantity: number,
  action: InventoryAction,
  _performedBy: string,
  _referenceId?: string,
  _referenceType?: string,
  _notes?: string,
) {
  const _previousQuantity = this.quantity;

  switch (action) {
    case 'in':
      this.quantity += quantity;
      break;
    case 'out':
      this.quantity -= quantity;
      break;
    case 'adjust':
      this.quantity = quantity;
      break;
    case 'transfer':
      this.quantity += quantity;
      break;
    case 'damage':
      this.quantity -= quantity;
      break;
    case 'return':
      this.quantity += quantity;
      break;
  }

  this.availableQuantity = Math.max(0, this.quantity - this.reservedQuantity);
  this.lastUpdated = new Date();

  return this.save();
};

inventorySchema.methods.reserveStock = function(quantity: number) {
  if (this.availableQuantity >= quantity) {
    this.reservedQuantity += quantity;
    this.availableQuantity = Math.max(0, this.quantity - this.reservedQuantity);
    return this.save();
  }
  throw new Error('Insufficient stock');
};

inventorySchema.methods.releaseStock = function(quantity: number) {
  this.reservedQuantity = Math.max(0, this.reservedQuantity - quantity);
  this.availableQuantity = Math.max(0, this.quantity - this.reservedQuantity);
  return this.save();
};

inventorySchema.methods.isLowStock = function() {
  return this.availableQuantity <= this.lowStockThreshold;
};

inventorySchema.methods.needsReorder = function() {
  return this.availableQuantity <= this.reorderPoint;
};

// Pre-save hook to update available quantity
inventorySchema.pre('save', function(next) {
  this.availableQuantity = Math.max(0, this.quantity - this.reservedQuantity);
  this.lastUpdated = new Date();
  next();
});

export const Inventory = mongoose.model<IInventory>('Inventory', inventorySchema);

// Inventory Transaction Model
export interface IInventoryTransactionDoc extends Document {
  _id: string;
  siteId: string;
  productId: string;
  variantId?: string;
  action: InventoryAction;
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  unitCost: number;
  totalCost: number;
  referenceId?: string;
  referenceType?: string;
  notes?: string;
  performedBy: string;
  performedAt: Date;
}

const inventoryTransactionDocSchema = new Schema<IInventoryTransactionDoc>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    productId: { type: String, required: true, index: true },
    variantId: { type: String },
    action: {
      type: String,
      enum: ['in', 'out', 'adjust', 'transfer', 'damage', 'return'],
      required: true,
      index: true,
    },
    quantity: { type: Number, required: true },
    previousQuantity: { type: Number, required: true },
    newQuantity: { type: Number, required: true },
    unitCost: { type: Number, default: 0 },
    totalCost: { type: Number, default: 0 },
    referenceId: { type: String, index: true },
    referenceType: { type: String },
    notes: { type: String },
    performedBy: { type: String, required: true },
    performedAt: { type: Date, default: Date.now, index: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
inventoryTransactionDocSchema.index({ siteId: 1, productId: 1 });
inventoryTransactionDocSchema.index({ siteId: 1, action: 1 });
inventoryTransactionDocSchema.index({ siteId: 1, performedAt: -1 });

export const InventoryTransaction = mongoose.model<IInventoryTransactionDoc>(
  'InventoryTransaction',
  inventoryTransactionDocSchema,
);
