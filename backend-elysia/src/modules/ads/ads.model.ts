import { generateFileId } from '@/core/utils/idGenerator';
import { model, Schema } from 'mongoose';

export interface IAdsBase {
  _id: string;
  siteId: string;
  title: string;
  type: 'banner' | 'modal' | 'slide' | 'marquee' | 'popup' | 'floating';
  position: 'header' | 'footer' | 'sidebar' | 'content-top' | 'content-bottom' | 'custom';
  content: {
    html?: string;
    imageUrl?: string;
    videoUrl?: string;
    text?: string;
    linkUrl?: string;
    linkTarget: '_self' | '_blank';
    backgroundColor?: string;
    textColor?: string;
    fontSize?: string;
    fontWeight?: string;
    borderRadius?: string;
    padding?: string;
    margin?: string;
    customCSS?: string;
    customJS?: string;
  };
  displaySettings: {
    showOnPages: 'all' | 'specific' | 'exclude';
    specificPages?: string[]; // page slugs or IDs
    excludePages?: string[];
    showOnMobile: boolean;
    showOnTablet: boolean;
    showOnDesktop: boolean;
    startDate?: Date;
    endDate?: Date;
    maxViews?: number;
    maxClicks?: number;
    currentViews: number;
    currentClicks: number;
    showToVisitors: boolean;
    showToCustomers: boolean;
    showToAdmins: boolean;
  };
  behaviorSettings: {
    autoClose?: number; // seconds
    showDelay?: number; // seconds
    showFrequency: 'always' | 'once-per-session' | 'once-per-day' | 'once-per-week';
    animationType?: 'fade' | 'slide' | 'bounce' | 'zoom' | 'none';
    animationDuration?: number; // milliseconds
    closeButton: boolean;
    overlayClose: boolean;
    escapeClose: boolean;
  };
  slideSettings?: {
    slides: Array<{
      id: string;
      imageUrl: string;
      title?: string;
      description?: string;
      linkUrl?: string;
      linkTarget: '_self' | '_blank';
      order: number;
    }>;
    autoPlay: boolean;
    autoPlayInterval: number; // seconds
    showDots: boolean;
    showArrows: boolean;
    infinite: boolean;
    pauseOnHover: boolean;
  };
  marqueeSettings?: {
    text: string;
    speed: number; // pixels per second
    direction: 'left' | 'right' | 'up' | 'down';
    pauseOnHover: boolean;
    backgroundColor?: string;
    textColor?: string;
    fontSize?: string;
    height?: string;
  };
  isActive: boolean;
  priority: number;
  createdBy: string;
}

export interface IAds extends IAdsBase {
  createdAt: Date;
  updatedAt: Date;
}

const adsSchema = new Schema<IAds>(
  {
    _id: {
      type: String,
      required: true,
      trim: true,
      default: () => generateFileId(8),
    },
    siteId: {
      type: String,
      required: true,
      ref: 'Site',
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['banner', 'modal', 'slide', 'marquee', 'popup', 'floating'],
    },
    position: {
      type: String,
      required: true,
      enum: ['header', 'footer', 'sidebar', 'content-top', 'content-bottom', 'custom'],
    },
    content: {
      html: { type: String, default: '' },
      imageUrl: { type: String, default: '' },
      videoUrl: { type: String, default: '' },
      text: { type: String, default: '' },
      linkUrl: { type: String, default: '' },
      linkTarget: { type: String, enum: ['_self', '_blank'], default: '_self' },
      backgroundColor: { type: String, default: '' },
      textColor: { type: String, default: '' },
      fontSize: { type: String, default: '' },
      fontWeight: { type: String, default: '' },
      borderRadius: { type: String, default: '' },
      padding: { type: String, default: '' },
      margin: { type: String, default: '' },
      customCSS: { type: String, default: '' },
      customJS: { type: String, default: '' },
    },
    displaySettings: {
      showOnPages: {
        type: String,
        enum: ['all', 'specific', 'exclude'],
        default: 'all',
      },
      specificPages: [{ type: String }],
      excludePages: [{ type: String }],
      showOnMobile: { type: Boolean, default: true },
      showOnTablet: { type: Boolean, default: true },
      showOnDesktop: { type: Boolean, default: true },
      startDate: { type: Date },
      endDate: { type: Date },
      maxViews: { type: Number },
      maxClicks: { type: Number },
      currentViews: { type: Number, default: 0 },
      currentClicks: { type: Number, default: 0 },
      showToVisitors: { type: Boolean, default: true },
      showToCustomers: { type: Boolean, default: true },
      showToAdmins: { type: Boolean, default: true },
    },
    behaviorSettings: {
      autoClose: { type: Number },
      showDelay: { type: Number, default: 0 },
      showFrequency: {
        type: String,
        enum: ['always', 'once-per-session', 'once-per-day', 'once-per-week'],
        default: 'always',
      },
      animationType: {
        type: String,
        enum: ['fade', 'slide', 'bounce', 'zoom', 'none'],
        default: 'fade',
      },
      animationDuration: { type: Number, default: 300 },
      closeButton: { type: Boolean, default: true },
      overlayClose: { type: Boolean, default: true },
      escapeClose: { type: Boolean, default: true },
    },
    slideSettings: {
      slides: [
        {
          id: { type: String, required: true },
          imageUrl: { type: String, required: true },
          title: { type: String, default: '' },
          description: { type: String, default: '' },
          linkUrl: { type: String, default: '' },
          linkTarget: { type: String, enum: ['_self', '_blank'], default: '_self' },
          order: { type: Number, default: 0 },
        },
      ],
      autoPlay: { type: Boolean, default: true },
      autoPlayInterval: { type: Number, default: 5 },
      showDots: { type: Boolean, default: true },
      showArrows: { type: Boolean, default: true },
      infinite: { type: Boolean, default: true },
      pauseOnHover: { type: Boolean, default: true },
    },
    marqueeSettings: {
      text: { type: String, default: '' },
      speed: { type: Number, default: 50 },
      direction: { type: String, enum: ['left', 'right', 'up', 'down'], default: 'left' },
      pauseOnHover: { type: Boolean, default: true },
      backgroundColor: { type: String, default: '' },
      textColor: { type: String, default: '' },
      fontSize: { type: String, default: '' },
      height: { type: String, default: '' },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    priority: {
      type: Number,
      default: 0,
    },
    createdBy: {
      type: String,
      required: true,
      ref: 'User',
    },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
  },
);

// Indexes for better performance
adsSchema.index({ siteId: 1, isActive: 1 });
adsSchema.index({ siteId: 1, type: 1 });
adsSchema.index({ siteId: 1, position: 1 });
adsSchema.index({ siteId: 1, priority: -1 });
adsSchema.index({ 'displaySettings.startDate': 1, 'displaySettings.endDate': 1 });

export const Ads = model<IAds>('Ads', adsSchema);
