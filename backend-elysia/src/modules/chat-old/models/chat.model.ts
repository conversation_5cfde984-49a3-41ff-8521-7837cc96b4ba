import { generateId } from '@/core/utils';
import mongoose, { type Document, Schema } from 'mongoose';

export interface IChatRoom extends Document {
  _id: string;
  siteId: string;
  roomId: string;
  type: 'user_customer' | 'user_support' | 'customer_support';
  participants: Array<{
    userId: string;
    userType: 'user' | 'customer' | 'support';
    name: string;
    avatar?: string;
    isOnline: boolean;
    lastSeen: Date;
  }>;
  status: 'active' | 'closed' | 'archived';
  metadata: {
    subject?: string;
    category?: string;
    priority: 'low' | 'medium' | 'high';
    tags: string[];
  };
  settings: {
    autoClose: boolean;
    closeAfterHours: number;
    allowFileSharing: boolean;
    maxFileSize: number;
    allowedFileTypes: string[];
  };
  stats: {
    totalMessages: number;
    lastMessageAt: Date;
    averageResponseTime: number;
    satisfactionRating?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IChatMessage extends Document {
  _id: string;
  siteId: string;
  roomId: string;
  senderId: string;
  senderType: 'user' | 'customer' | 'support';
  senderName: string;
  messageType: 'text' | 'image' | 'file' | 'system' | 'typing';
  content: string;
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileSize: number;
    fileType: string;
  }>;
  metadata?: {
    replyTo?: string;
    editedAt?: Date;
    deletedAt?: Date;
    readBy: string[];
    deliveredTo: string[];
  };
  isRead: boolean;
  isDelivered: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IChatSession extends Document {
  _id: string;
  siteId: string;
  roomId: string;
  userId: string;
  userType: 'user' | 'customer' | 'support';
  sessionId: string;
  isActive: boolean;
  lastActivity: Date;
  userAgent: string;
  ipAddress: string;
  createdAt: Date;
  updatedAt: Date;
}

const ChatRoomSchema = new Schema<IChatRoom>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    roomId: { type: String, required: true, unique: true },
    type: {
      type: String,
      required: true,
      enum: ['user_customer', 'user_support', 'customer_support'],
      default: 'user_customer',
    },
    participants: [
      {
        userId: { type: String, required: true },
        userType: {
          type: String,
          required: true,
          enum: ['user', 'customer', 'support'],
        },
        name: { type: String, required: true },
        avatar: { type: String },
        isOnline: { type: Boolean, default: false },
        lastSeen: { type: Date, default: Date.now },
      },
    ],
    status: {
      type: String,
      required: true,
      enum: ['active', 'closed', 'archived'],
      default: 'active',
    },
    metadata: {
      subject: { type: String },
      category: { type: String },
      priority: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium',
      },
      tags: [{ type: String }],
    },
    settings: {
      autoClose: { type: Boolean, default: false },
      closeAfterHours: { type: Number, default: 24 },
      allowFileSharing: { type: Boolean, default: true },
      maxFileSize: { type: Number, default: 10 * 1024 * 1024 }, // 10MB
      allowedFileTypes: [{ type: String, default: ['jpg', 'png', 'pdf', 'doc'] }],
    },
    stats: {
      totalMessages: { type: Number, default: 0 },
      lastMessageAt: { type: Date, default: Date.now },
      averageResponseTime: { type: Number, default: 0 },
      satisfactionRating: { type: Number, min: 1, max: 5 },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ChatMessageSchema = new Schema<IChatMessage>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    roomId: { type: String, required: true, index: true },
    senderId: { type: String, required: true, index: true },
    senderType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'support'],
    },
    senderName: { type: String, required: true },
    messageType: {
      type: String,
      required: true,
      enum: ['text', 'image', 'file', 'system', 'typing'],
      default: 'text',
    },
    content: { type: String, required: true },
    attachments: [
      {
        fileName: { type: String, required: true },
        fileUrl: { type: String, required: true },
        fileSize: { type: Number, required: true },
        fileType: { type: String, required: true },
      },
    ],
    metadata: {
      replyTo: { type: String },
      editedAt: { type: Date },
      deletedAt: { type: Date },
      readBy: [{ type: String }],
      deliveredTo: [{ type: String }],
    },
    isRead: { type: Boolean, default: false },
    isDelivered: { type: Boolean, default: false },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ChatSessionSchema = new Schema<IChatSession>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    roomId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    userType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'support'],
    },
    sessionId: { type: String, required: true, unique: true },
    isActive: { type: Boolean, default: true },
    lastActivity: { type: Date, default: Date.now },
    userAgent: { type: String },
    ipAddress: { type: String },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
// ChatRoomSchema.index({ siteId: 1, roomId: 1 });
// ChatRoomSchema.index({ siteId: 1, status: 1 });
// ChatRoomSchema.index({ 'participants.userId': 1 });
// ChatRoomSchema.index({ createdAt: -1 });

// ChatMessageSchema.index({ siteId: 1, roomId: 1 });
// ChatMessageSchema.index({ senderId: 1 });
// ChatMessageSchema.index({ createdAt: -1 });
// ChatMessageSchema.index({ isRead: 1 });

// ChatSessionSchema.index({ siteId: 1, roomId: 1 });
// ChatSessionSchema.index({ userId: 1 });
// ChatSessionSchema.index({ sessionId: 1 });
// ChatSessionSchema.index({ isActive: 1 });

// Static methods
ChatRoomSchema.statics.findByRoomId = async function(siteId: string, roomId: string) {
  return this.findOne({ siteId, roomId });
};

ChatRoomSchema.statics.findByParticipant = async function(siteId: string, userId: string) {
  return this.find({
    siteId,
    'participants.userId': userId,
    status: { $ne: 'archived' },
  }).sort({ updatedAt: -1 });
};

ChatRoomSchema.statics.findActiveRooms = async function(siteId: string) {
  return this.find({ siteId, status: 'active' }).sort({ updatedAt: -1 });
};

ChatMessageSchema.statics.findByRoom = async function(siteId: string, roomId: string, limit: number = 50) {
  return this.find({ siteId, roomId }).sort({ createdAt: -1 }).limit(limit);
};

ChatMessageSchema.statics.getUnreadCount = async function(siteId: string, roomId: string, userId: string) {
  return this.countDocuments({
    siteId,
    roomId,
    senderId: { $ne: userId },
    isRead: false,
  });
};

ChatSessionSchema.statics.findActiveSession = async function(siteId: string, userId: string) {
  return this.findOne({ siteId, userId, isActive: true });
};

ChatSessionSchema.statics.updateLastActivity = async function(sessionId: string) {
  return this.findOneAndUpdate({ sessionId }, { $set: { lastActivity: new Date() } }, { new: true });
};

export const ChatRoom = mongoose.model<IChatRoom>('ChatRoom', ChatRoomSchema);
export const ChatMessage = mongoose.model<IChatMessage>('ChatMessage', ChatMessageSchema);
export const ChatSession = mongoose.model<IChatSession>('ChatSession', ChatSessionSchema);
