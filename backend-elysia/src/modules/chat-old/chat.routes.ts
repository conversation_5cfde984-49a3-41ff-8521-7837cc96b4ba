import { checkSite } from '@/core/middleware/checkSite';
import { checkUser } from '@/core/middleware/checkUser';
import { logger } from '@/core/utils/logger';
import { Elysia } from 'elysia';
import {
  chatQuerySchema,
  createChatBotSchema,
  createChatRoomSchema,
  createFAQSchema,
  createLiveChatSessionSchema,
  createSessionSchema,
  sendLiveChatMessageSchema,
  sendMessageSchema,
  updateChatBotSchema,
  updateFAQSchema,
} from './chat.schema';
import { ChatService, LiveChatService } from './services';
const chatService = new ChatService();
const liveChatService = new LiveChatService();

export const chatRoutes = new Elysia({ prefix: '/chat' })
  .use(checkUser)
  .use(checkSite)
  // ===== GENERAL CHAT ROUTES =====

  // ดึงห้องแชทของผู้ใช้
  .get(
    '/',
    async ({ _query, user, site }) => {
      try {
        const rooms = await chatService.getUserChatRooms(site._id, user._id);

        return {
          success: true,
          message: 'ดึงห้องแชทสำเร็จ',
          data: rooms,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดึงห้องแชท:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดึงห้องแชท',
        };
      }
    },
    {
      query: chatQuerySchema,
    },
  )
  // สร้างห้องแชท
  .post(
    '/',
    async ({ body, _user, site }) => {
      try {
        const room = await chatService.createChatRoom(site._id, body.participants, body.options);

        return {
          success: true,
          message: 'สร้างห้องแชทสำเร็จ',
          data: room,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้างห้องแชท:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้างห้องแชท',
        };
      }
    },
    {
      body: createChatRoomSchema,
    },
  )
  // ดึงห้องแชทตาม ID
  .get('/:roomId', async ({ params, site }) => {
    try {
      const room = await chatService.getChatRoom(site._id, params.roomId);

      return {
        success: true,
        message: 'ดึงห้องแชทสำเร็จ',
        data: room,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงห้องแชท:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงห้องแชท',
      };
    }
  })
  // ส่งข้อความ
  .post(
    '/:roomId/messages',
    async ({ params, body, user, site }) => {
      try {
        const message = await chatService.sendMessage(
          site._id,
          params.roomId,
          user._id,
          body.senderType,
          body.content,
          body.options,
        );

        return {
          success: true,
          message: 'ส่งข้อความสำเร็จ',
          data: message,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการส่งข้อความ:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการส่งข้อความ',
        };
      }
    },
    {
      body: sendMessageSchema,
    },
  )
  // ดึงข้อความ
  .get('/:roomId/messages', async ({ params, query, site }) => {
    try {
      const result = await chatService.getMessages(site._id, params.roomId, query);

      return {
        success: true,
        message: 'ดึงข้อความสำเร็จ',
        data: result,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อความ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อความ',
      };
    }
  })
  // ทำเครื่องหมายข้อความว่าอ่านแล้ว
  .patch('/:roomId/messages/:messageId/read', async ({ params, user, site }) => {
    try {
      const message = await chatService.markAsRead(site._id, params.roomId, params.messageId, user._id);

      return {
        success: true,
        message: 'ทำเครื่องหมายข้อความสำเร็จ',
        data: message,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการทำเครื่องหมายข้อความ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการทำเครื่องหมายข้อความ',
      };
    }
  })
  // นับข้อความที่ยังไม่ได้อ่าน
  .get('/:roomId/unread', async ({ params, user, site }) => {
    try {
      const count = await chatService.getUnreadCount(site._id, params.roomId, user._id);

      return {
        success: true,
        message: 'ดึงจำนวนข้อความที่ยังไม่ได้อ่านสำเร็จ',
        data: { count },
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการนับข้อความที่ยังไม่ได้อ่าน:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการนับข้อความที่ยังไม่ได้อ่าน',
      };
    }
  })
  // สร้าง session
  .post(
    '/sessions',
    async ({ body, user, site }) => {
      try {
        const session = await chatService.createSession(site._id, body.roomId, user._id, body.userType, body.metadata);

        return {
          success: true,
          message: 'สร้าง session สำเร็จ',
          data: session,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้าง session:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้าง session',
        };
      }
    },
    {
      body: createSessionSchema,
    },
  )
  // ปิด session
  .delete('/sessions/:sessionId', async ({ params }) => {
    try {
      const session = await chatService.closeSession(params.sessionId);

      return {
        success: true,
        message: 'ปิด session สำเร็จ',
        data: session,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการปิด session:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการปิด session',
      };
    }
  })
  // ===== LIVE CHAT ROUTES =====

  // สร้าง LiveChat session
  .post(
    '/support',
    async ({ body, user, site }) => {
      try {
        const session = await liveChatService.createSession(
          site._id,
          user._id,
          body.customerName,
          body.description,
          body.options,
        );

        return {
          success: true,
          message: 'สร้าง LiveChat session สำเร็จ',
          data: session,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้าง LiveChat session:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้าง LiveChat session',
        };
      }
    },
    {
      body: createLiveChatSessionSchema,
    },
  )
  // ดึง LiveChat sessions
  .get('/support', async ({ query, user, site }) => {
    try {
      const result = await liveChatService.getSessions(site._id, {
        ...query,
        customerId: user._id,
      });

      return {
        success: true,
        message: 'ดึง LiveChat sessions สำเร็จ',
        data: result,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึง LiveChat sessions:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึง LiveChat sessions',
      };
    }
  })
  // ดึง LiveChat session ตาม ID
  .get('/support/:sessionId', async ({ params, site }) => {
    try {
      const session = await liveChatService.getSession(site._id, params.sessionId);

      return {
        success: true,
        message: 'ดึง LiveChat session สำเร็จ',
        data: session,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึง LiveChat session:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึง LiveChat session',
      };
    }
  })
  // ส่งข้อความใน LiveChat
  .post(
    '/support/:sessionId/messages',
    async ({ params, body, user, site }) => {
      try {
        const message = await liveChatService.sendMessage(
          site._id,
          params.sessionId,
          user._id,
          'customer',
          body.senderName || 'Customer',
          body.content,
          body.options,
        );

        return {
          success: true,
          message: 'ส่งข้อความ LiveChat สำเร็จ',
          data: message,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการส่งข้อความ LiveChat:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการส่งข้อความ LiveChat',
        };
      }
    },
    {
      body: sendLiveChatMessageSchema,
    },
  )
  // ดึงข้อความ LiveChat
  .get('/support/:sessionId/messages', async ({ params, site }) => {
    try {
      const messages = await liveChatService.getMessages(site._id, params.sessionId);

      return {
        success: true,
        message: 'ดึงข้อความ LiveChat สำเร็จ',
        data: messages,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อความ LiveChat:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อความ LiveChat',
      };
    }
  })
  // ให้คะแนน LiveChat session
  .post('/support/:sessionId/rate', async ({ params, body, site }) => {
    try {
      const session = await liveChatService.rateSession(site._id, params.sessionId, body.rating, body.feedback);

      return {
        success: true,
        message: 'ให้คะแนนสำเร็จ',
        data: session,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการให้คะแนน:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการให้คะแนน',
      };
    }
  })
  // ===== ADMIN ROUTES =====

  // ดึง sessions ที่รอ (Admin only)
  .get('/admin/waiting', async ({ user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      const sessions = await liveChatService.getWaitingSessions(site._id);

      return {
        success: true,
        message: 'ดึง sessions ที่รอสำเร็จ',
        data: sessions,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึง sessions ที่รอ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึง sessions ที่รอ',
      };
    }
  })
  // มอบหมาย agent (Admin only)
  .patch('/admin/support/:sessionId/assign', async ({ params, body, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      const session = await liveChatService.assignAgent(site._id, params.sessionId, body.agentId, body.agentName);

      return {
        success: true,
        message: 'มอบหมาย agent สำเร็จ',
        data: session,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการมอบหมาย agent:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการมอบหมาย agent',
      };
    }
  })
  // ===== CHATBOT ROUTES =====

  // ดึง ChatBot
  .get('/bot', async ({ site }) => {
    try {
      const bot = await liveChatService.getChatBot(site._id);

      return {
        success: true,
        message: 'ดึง ChatBot สำเร็จ',
        data: bot,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึง ChatBot:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึง ChatBot',
      };
    }
  })
  // สร้าง ChatBot (Admin only)
  .post(
    '/bot',
    async ({ body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const bot = await liveChatService.createChatBot(site._id, body);

        return {
          success: true,
          message: 'สร้าง ChatBot สำเร็จ',
          data: bot,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้าง ChatBot:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้าง ChatBot',
        };
      }
    },
    {
      body: createChatBotSchema,
    },
  )
  // อัพเดท ChatBot (Admin only)
  .patch(
    '/bot/:botId',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const bot = await liveChatService.updateChatBot(site._id, params.botId, body);

        return {
          success: true,
          message: 'อัพเดท ChatBot สำเร็จ',
          data: bot,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการอัพเดท ChatBot:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการอัพเดท ChatBot',
        };
      }
    },
    {
      body: updateChatBotSchema,
    },
  )
  // ===== FAQ ROUTES =====

  // ดึง FAQ
  .get('/faq', async ({ query, site }) => {
    try {
      const faqs = await liveChatService.getFAQs(site._id, query.category);

      return {
        success: true,
        message: 'ดึง FAQ สำเร็จ',
        data: faqs,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึง FAQ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึง FAQ',
      };
    }
  })
  // ค้นหา FAQ
  .get('/faq/search', async ({ query, site }) => {
    try {
      const faqs = await liveChatService.searchFAQs(site._id, query.q);

      return {
        success: true,
        message: 'ค้นหา FAQ สำเร็จ',
        data: faqs,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการค้นหา FAQ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการค้นหา FAQ',
      };
    }
  })
  // สร้าง FAQ (Admin only)
  .post(
    '/faq',
    async ({ body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const faq = await liveChatService.createFAQ(site._id, body);

        return {
          success: true,
          message: 'สร้าง FAQ สำเร็จ',
          data: faq,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้าง FAQ:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้าง FAQ',
        };
      }
    },
    {
      body: createFAQSchema,
    },
  )
  // อัพเดท FAQ (Admin only)
  .patch(
    '/faq/:faqId',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const faq = await liveChatService.updateFAQ(site._id, params.faqId, body);

        return {
          success: true,
          message: 'อัพเดท FAQ สำเร็จ',
          data: faq,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการอัพเดท FAQ:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการอัพเดท FAQ',
        };
      }
    },
    {
      body: updateFAQSchema,
    },
  )
  // ลบ FAQ (Admin only)
  .delete('/faq/:faqId', async ({ params, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      await liveChatService.deleteFAQ(site._id, params.faqId);

      return {
        success: true,
        message: 'ลบ FAQ สำเร็จ',
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการลบ FAQ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการลบ FAQ',
      };
    }
  })
  // ดึงสถิติ (Admin only)
  .get('/stats', async ({ query, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      const stats = await liveChatService.getStats(site._id, query.agentId);

      return {
        success: true,
        message: 'ดึงสถิติสำเร็จ',
        data: stats,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติ',
      };
    }
  });
