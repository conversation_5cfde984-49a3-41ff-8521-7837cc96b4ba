import { generateFileId } from '@/core/utils/idGenerator';
import { logger } from '@/core/utils/logger';
import { Customer } from '@/modules/customer/customer.model';
import { User } from '@/modules/user/user.model';
import { ChatMessage, ChatRoom, ChatSession } from '../models/chat.model';

export class ChatService {
  // สร้างห้องแชท
  async createChatRoom(
    siteId: string,
    participants: Array<{
      userId: string;
      userType: 'user' | 'customer' | 'support';
      name?: string;
      avatar?: string;
    }>,
    options: {
      type?: 'user_customer' | 'user_support' | 'customer_support';
      subject?: string;
      category?: string;
      priority?: 'low' | 'medium' | 'high';
    } = {},
  ) {
    try {
      // ตรวจสอบและเติมข้อมูลผู้เข้าร่วม
      for (const participant of participants) {
        if (!participant.name) {
          if (participant.userType === 'user') {
            const user = await User.findById(participant.userId);
            if (!user) throw new Error(`ไม่พบ user: ${participant.userId}`);
            participant.name = `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email;
            participant.avatar = user.avatar;
          }
          else if (participant.userType === 'customer') {
            const customer = await Customer.findById(participant.userId);
            if (!customer) throw new Error(`ไม่พบ customer: ${participant.userId}`);
            participant.name = `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email;
            participant.avatar = customer.avatar;
          }
        }
      }

      const roomId = generateFileId(16);

      const chatRoom = new ChatRoom({
        siteId,
        roomId,
        type: options.type || 'user_customer',
        participants: participants.map(p => ({
          ...p,
          isOnline: false,
          lastSeen: new Date(),
        })),
        metadata: {
          subject: options.subject,
          category: options.category,
          priority: options.priority || 'medium',
          tags: [],
        },
      });

      await chatRoom.save();

      logger.info(`สร้างห้องแชท: ${roomId} สำหรับเว็บไซต์ ${siteId}`);

      return chatRoom;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการสร้างห้องแชท:', error);
      throw error;
    }
  }

  // ดึงห้องแชท
  async getChatRoom(siteId: string, roomId: string) {
    try {
      const chatRoom = await ChatRoom.findOne({ siteId, roomId });
      if (!chatRoom) {
        throw new Error('ไม่พบห้องแชท');
      }
      return chatRoom;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงห้องแชท:', error);
      throw error;
    }
  }

  // ดึงห้องแชทของผู้ใช้
  async getUserChatRooms(siteId: string, userId: string) {
    try {
      return await ChatRoom.find({
        siteId,
        'participants.userId': userId,
        status: { $ne: 'archived' },
      }).sort({ updatedAt: -1 });
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงห้องแชทของผู้ใช้:', error);
      throw error;
    }
  }

  // ส่งข้อความ
  async sendMessage(
    siteId: string,
    roomId: string,
    senderId: string,
    senderType: 'user' | 'customer' | 'support',
    content: string,
    options: {
      messageType?: 'text' | 'image' | 'file' | 'system';
      attachments?: Array<{
        fileName: string;
        fileUrl: string;
        fileSize: number;
        fileType: string;
      }>;
      replyTo?: string;
    } = {},
  ) {
    try {
      // ตรวจสอบห้องแชท
      const chatRoom = await this.getChatRoom(siteId, roomId);
      if (chatRoom.status !== 'active') {
        throw new Error('ห้องแชทไม่ได้เปิดใช้งาน');
      }

      // ตรวจสอบสิทธิ์
      const isParticipant = chatRoom.participants.some(p => p.userId === senderId);
      if (!isParticipant) {
        throw new Error('คุณไม่มีสิทธิ์ส่งข้อความในห้องนี้');
      }

      // หาชื่อผู้ส่ง
      const participant = chatRoom.participants.find(p => p.userId === senderId);
      const senderName = participant?.name || 'Unknown';

      const message = new ChatMessage({
        siteId,
        roomId,
        senderId,
        senderType,
        senderName,
        messageType: options.messageType || 'text',
        content,
        attachments: options.attachments,
        metadata: {
          replyTo: options.replyTo,
          readBy: [],
          deliveredTo: [],
        },
      });

      await message.save();

      // อัพเดทสถิติห้อง
      await ChatRoom.findByIdAndUpdate(chatRoom._id, {
        $inc: { 'stats.totalMessages': 1 },
        $set: { 'stats.lastMessageAt': new Date() },
      });

      logger.info(`ส่งข้อความในห้อง ${roomId} โดย ${senderName}`);

      return message;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการส่งข้อความ:', error);
      throw error;
    }
  }

  // ดึงข้อความ
  async getMessages(
    siteId: string,
    roomId: string,
    options: {
      page?: number;
      limit?: number;
      before?: Date;
      after?: Date;
    } = {},
  ) {
    try {
      const page = options.page || 1;
      const limit = options.limit || 50;
      const skip = (page - 1) * limit;

      const filter: any = { siteId, roomId };

      if (options.before || options.after) {
        filter.createdAt = {};
        if (options.before) filter.createdAt.$lt = options.before;
        if (options.after) filter.createdAt.$gt = options.after;
      }

      const [messages, total] = await Promise.all([
        ChatMessage.find(filter).sort({ createdAt: -1 }).skip(skip).limit(limit),
        ChatMessage.countDocuments(filter),
      ]);

      return {
        messages: messages.reverse(), // เรียงจากเก่าไปใหม่
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อความ:', error);
      throw error;
    }
  }

  // ทำเครื่องหมายข้อความว่าอ่านแล้ว
  async markAsRead(siteId: string, roomId: string, messageId: string, userId: string) {
    try {
      const message = await ChatMessage.findOneAndUpdate(
        { siteId, roomId, _id: messageId },
        {
          $set: { isRead: true },
          $addToSet: { 'metadata.readBy': userId },
        },
        { new: true },
      );

      if (!message) {
        throw new Error('ไม่พบข้อความ');
      }

      return message;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการทำเครื่องหมายข้อความ:', error);
      throw error;
    }
  }

  // นับข้อความที่ยังไม่ได้อ่าน
  async getUnreadCount(siteId: string, roomId: string, userId: string) {
    try {
      return await ChatMessage.countDocuments({
        siteId,
        roomId,
        senderId: { $ne: userId },
        'metadata.readBy': { $ne: userId },
      });
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการนับข้อความที่ยังไม่ได้อ่าน:', error);
      throw error;
    }
  }

  // สร้าง session
  async createSession(
    siteId: string,
    roomId: string,
    userId: string,
    userType: 'user' | 'customer' | 'support',
    metadata: {
      userAgent?: string;
      ipAddress?: string;
    } = {},
  ) {
    try {
      // ปิด session เก่า
      await ChatSession.updateMany({ siteId, userId, isActive: true }, { isActive: false });

      const sessionId = generateFileId(24);

      const session = new ChatSession({
        siteId,
        roomId,
        userId,
        userType,
        sessionId,
        userAgent: metadata.userAgent,
        ipAddress: metadata.ipAddress,
      });

      await session.save();

      // อัพเดทสถานะออนไลน์
      await ChatRoom.updateOne(
        { siteId, roomId, 'participants.userId': userId },
        {
          $set: {
            'participants.$.isOnline': true,
            'participants.$.lastSeen': new Date(),
          },
        },
      );

      logger.info(`สร้าง session ${sessionId} สำหรับผู้ใช้ ${userId}`);

      return session;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการสร้าง session:', error);
      throw error;
    }
  }

  // ปิด session
  async closeSession(sessionId: string) {
    try {
      const session = await ChatSession.findOneAndUpdate({ sessionId }, { isActive: false }, { new: true });

      if (session) {
        // อัพเดทสถานะออฟไลน์
        await ChatRoom.updateOne(
          { siteId: session.siteId, 'participants.userId': session.userId },
          {
            $set: {
              'participants.$.isOnline': false,
              'participants.$.lastSeen': new Date(),
            },
          },
        );

        logger.info(`ปิด session ${sessionId}`);
      }

      return session;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการปิด session:', error);
      throw error;
    }
  }

  // ดึงผู้ใช้ออนไลน์
  async getOnlineUsers(siteId: string, roomId: string) {
    try {
      const chatRoom = await this.getChatRoom(siteId, roomId);
      return chatRoom.participants.filter(p => p.isOnline);
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการดึงผู้ใช้ออนไลน์:', error);
      throw error;
    }
  }

  // ค้นหาข้อความ
  async searchMessages(
    siteId: string,
    roomId: string,
    query: string,
    options: {
      page?: number;
      limit?: number;
    } = {},
  ) {
    try {
      const page = options.page || 1;
      const limit = options.limit || 20;
      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        ChatMessage.find({
          siteId,
          roomId,
          content: { $regex: query, $options: 'i' },
          'metadata.deletedAt': { $exists: false },
        })
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        ChatMessage.countDocuments({
          siteId,
          roomId,
          content: { $regex: query, $options: 'i' },
          'metadata.deletedAt': { $exists: false },
        }),
      ]);

      return {
        messages,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการค้นหาข้อความ:', error);
      throw error;
    }
  }

  // ปิดห้องแชท
  async closeChatRoom(siteId: string, roomId: string) {
    try {
      const chatRoom = await ChatRoom.findOneAndUpdate({ siteId, roomId }, { status: 'closed' }, { new: true });

      if (!chatRoom) {
        throw new Error('ไม่พบห้องแชท');
      }

      // ปิด sessions ทั้งหมดในห้อง
      await ChatSession.updateMany({ siteId, roomId, isActive: true }, { isActive: false });

      logger.info(`ปิดห้องแชท ${roomId}`);

      return chatRoom;
    }
    catch (error) {
      logger.error('เกิดข้อผิดพลาดในการปิดห้องแชท:', error);
      throw error;
    }
  }
}
