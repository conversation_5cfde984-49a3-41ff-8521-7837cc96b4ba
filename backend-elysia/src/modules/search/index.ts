// Models
export * from './search.model';

// Services
export * from './search.service';

// Routes
export { searchRoutes } from './search.routes';

// Schemas
export * from './search.schema';

// Cache
export { searchCache } from './search.cache';

// Utils
export { AutocompleteEngine, SearchAnalytics, ThaiTextProcessor } from './search.utils';

// Real-time
export { RealTimeSearch, realTimeSearch } from './search.realtime';

// Analytics
export { AdvancedSearchAnalytics } from './search.analytics';

// Experiments
export { SearchExperimentEngine } from './search.experiments';

// Types
export interface SearchConfig {
  enableCache: boolean;
  enableAnalytics: boolean;
  enableExperiments: boolean;
  enableRealTime: boolean;
  cacheSettings: {
    searchResults: number;
    suggestions: number;
    filters: number;
    popularSearches: number;
  };
  thaiLanguage: {
    enableSynonyms: boolean;
    enableFuzzySearch: boolean;
    enableStopWords: boolean;
  };
  performance: {
    maxResultsPerPage: number;
    defaultPageSize: number;
    maxSuggestions: number;
    debounceTime: number;
  };
}

export const defaultSearchConfig: SearchConfig = {
  enableCache: true,
  enableAnalytics: true,
  enableExperiments: true,
  enableRealTime: true,
  cacheSettings: {
    searchResults: 180, // 3 minutes
    suggestions: 600, // 10 minutes
    filters: 1800, // 30 minutes
    popularSearches: 3600, // 1 hour
  },
  thaiLanguage: {
    enableSynonyms: true,
    enableFuzzySearch: true,
    enableStopWords: true,
  },
  performance: {
    maxResultsPerPage: 100,
    defaultPageSize: 20,
    maxSuggestions: 10,
    debounceTime: 150,
  },
};
