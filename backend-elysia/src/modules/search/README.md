# Advanced Search Module

ระบบค้นหาขั้นสูงที่รองรับภาษาไทย พร้อมฟีเจอร์ครบครัน

## ✨ Features

### 🔍 Core Search

- **Full-text search** พร้อม Thai language processing
- **Fuzzy search** สำหรับการพิมพ์ผิด
- **Synonym expansion** ขยายคำค้นหาด้วย synonyms
- **Advanced filtering** กรองข้อมูลแบบหลายเงื่อนไข
- **Smart sorting** จัดเรียงผลลัพธ์อัจฉริยะ

### ⚡ Performance

- **Redis caching** เพิ่มความเร็วการค้นหา
- **Optimized indexes** MongoDB indexes ที่ปรับแต่งแล้ว
- **Compound queries** การค้นหาแบบซับซ้อนที่เร็ว
- **Lazy loading** โหลดข้อมูลแบบ progressive

### 🎯 Real-time Features

- **Live autocomplete** แนะนำคำค้นหาแบบ real-time
- **Search-as-you-type** ค้นหาขณะพิมพ์
- **Session tracking** ติดตามพฤติกรรมการค้นหา
- **Popular suggestions** แนะนำคำค้นหายอดนิยม

### 📊 Analytics & Insights

- **Search dashboard** แดชบอร์ดสถิติการค้นหา
- **User behavior analysis** วิเคราะห์พฤติกรรมผู้ใช้
- **Performance metrics** ตัวชี้วัดประสิทธิภาพ
- **No-results tracking** ติดตามคำค้นหาที่ไม่มีผลลัพธ์

### 🧪 A/B Testing

- **Search experiments** ทดสอบ algorithm ต่างๆ
- **Traffic splitting** แบ่งผู้ใช้เข้า variants
- **Statistical analysis** วิเคราะห์ผลทดสอบ
- **Winner determination** หา algorithm ที่ดีที่สุด

## 🚀 Quick Start

### 1. Basic Search

```typescript
// GET /search?q=มือถือ&page=1&limit=20
const results = await searchService.search(
  siteId,
  'มือถือ',
  {}, // filters
  {}, // sort
  1, // page
  20, // limit
);
```

### 2. Advanced Search with Filters

```typescript
const results = await searchService.search(siteId, 'smartphone', {
  category: ['electronics'],
  brand: ['apple', 'samsung'],
  priceRange: { min: 10000, max: 50000 },
  rating: 4,
});
```

### 3. Real-time Autocomplete

```typescript
// Start search session
const session = await realTimeSearch.startSearchSession(
  'session-123',
  siteId,
  userId,
);

// Handle typing
const suggestions = await realTimeSearch.handleTyping('session-123', 'มือ');
```

### 4. Analytics Dashboard

```typescript
const dashboard = await AdvancedSearchAnalytics.generateDashboardData(siteId, {
  start: new Date('2024-01-01'),
  end: new Date('2024-01-31'),
});
```

### 5. A/B Testing

```typescript
// Create experiment
const experiment = await SearchExperimentEngine.createExperiment({
  id: 'semantic-vs-standard',
  name: 'Semantic Search Test',
  siteId,
  variants: [
    {
      name: 'control',
      algorithm: 'standard',
      parameters: {},
    },
    {
      name: 'semantic',
      algorithm: 'semantic',
      parameters: { synonymExpansion: true },
    },
  ],
  trafficAllocation: { control: 50, semantic: 50 },
  targetMetric: 'click_through_rate',
});

// Start experiment
await SearchExperimentEngine.startExperiment(experiment.id);
```

## 📡 API Endpoints

### Core Search

- `GET /search` - Basic search
- `GET /search/enhanced` - Enhanced search with experiments
- `GET /search/suggestions` - Get search suggestions
- `POST /search/suggestions` - Create search suggestion

### Real-time

- `POST /search/session/start` - Start search session
- `POST /search/session/:id/end` - End search session
- `POST /search/autocomplete` - Get autocomplete suggestions

### Analytics

- `GET /search/analytics/dashboard` - Analytics dashboard
- `GET /search/analytics/funnel` - Search funnel analysis
- `GET /search/analytics/query/:query` - Query performance report

### A/B Testing

- `POST /search/experiments` - Create experiment
- `GET /search/experiments` - List experiments
- `POST /search/experiments/:id/start` - Start experiment
- `POST /search/experiments/:id/stop` - Stop experiment

### Management

- `POST /search/index` - Create search index
- `PUT /search/index/:type/:id` - Update search index
- `DELETE /search/index/:type/:id` - Delete search index
- `POST /search/reindex` - Reindex all data

## 🔧 Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/your_db
```

### Cache Settings

```typescript
// Default TTL settings
const cacheSettings = {
  searchResults: 180, // 3 minutes
  suggestions: 600, // 10 minutes
  filters: 1800, // 30 minutes
  popularSearches: 3600, // 1 hour
};
```

## 🎛️ Advanced Configuration

### Thai Language Processing

```typescript
// Customize synonyms
ThaiTextProcessor.addSynonyms('มือถือ', [
  'โทรศัพท์',
  'สมาร์ทโฟน',
  'phone',
  'smartphone',
]);

// Add stop words
ThaiTextProcessor.addStopWords(['ครับ', 'ค่ะ', 'นะ']);
```

### Search Algorithms

```typescript
// Available algorithms for A/B testing
const algorithms = {
  standard: 'MongoDB text search',
  semantic: 'Synonym expansion + fuzzy matching',
  popularity_boost: 'Popularity-weighted results',
  personalized: 'User behavior-based ranking',
};
```

### Performance Tuning

```typescript
// Index optimization
SearchIndexSchema.index({
  siteId: 1,
  entityType: 1,
  'metadata.status': 1,
  popularity: -1,
});

// Query optimization
const pipeline = [
  { $match: { siteId, $text: { $search: query } } },
  { $sort: { score: { $meta: 'textScore' }, popularity: -1 } },
  { $limit: limit },
];
```

## 📈 Monitoring & Metrics

### Key Metrics

- **Search Volume**: จำนวนการค้นหาต่อวัน
- **Success Rate**: เปอร์เซ็นต์การค้นหาที่มีผลลัพธ์
- **Click-through Rate**: อัตราการคลิกผลลัพธ์
- **Average Response Time**: เวลาตอบสนองเฉลี่ย
- **Cache Hit Rate**: อัตราการใช้ cache

### Health Check

```bash
GET /search/health
```

Response:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "indexSize": 10000,
    "features": {
      "realTimeSearch": true,
      "analytics": true,
      "experiments": true,
      "thaiLanguageSupport": true,
      "caching": true
    }
  }
}
```

## 🔍 Search Quality Optimization

### 1. Index Management

```typescript
// Regular reindexing
await searchService.reindexAll(siteId);

// Selective reindexing
await searchService.updateSearchIndex(siteId, 'product', productId, indexData);
```

### 2. Query Analysis

```typescript
// Analyze no-results queries
const noResults = await AdvancedSearchAnalytics.getNoResultsQueries(
  siteId,
  timeRange,
);

// Optimize based on insights
for (const query of noResults) {
  console.log(`No results for: ${query.query} (${query.count} times)`);
}
```

### 3. Performance Monitoring

```typescript
// Track search performance
const metrics = await AdvancedSearchAnalytics.getPerformanceMetrics(
  siteId,
  timeRange,
);

console.log(`Average response time: ${metrics.avgResponseTime}ms`);
console.log(`Success rate: ${metrics.successRate * 100}%`);
```

## 🚨 Troubleshooting

### Common Issues

1. **Slow Search Performance**
   - Check MongoDB indexes
   - Verify Redis connection
   - Monitor query complexity

2. **No Search Results**
   - Check text indexing
   - Verify data in SearchIndex collection
   - Test with simple queries

3. **Cache Issues**
   - Check Redis connectivity
   - Verify cache TTL settings
   - Monitor cache hit rates

4. **Thai Language Problems**
   - Verify text processing
   - Check synonym dictionary
   - Test with different queries

### Debug Mode

```typescript
// Enable debug logging
process.env.SEARCH_DEBUG = 'true';

// Check search processing
const processed = ThaiTextProcessor.processQuery('มือถือ');
console.log('Processed query:', processed);
```

## 🔮 Future Enhancements

### Planned Features

- **Vector Search**: Semantic search ด้วย embeddings
- **Voice Search**: รองรับการค้นหาด้วยเสียง
- **Image Search**: ค้นหาด้วยรูปภาพ
- **ML Ranking**: Machine learning สำหรับ ranking
- **Multi-language**: รองรับหลายภาษา

### Performance Improvements

- **Elasticsearch Integration**: สำหรับ large-scale search
- **GraphQL Support**: API แบบ GraphQL
- **Microservices**: แยก search เป็น microservice
- **CDN Integration**: Cache ผลลัพธ์ใน CDN

## 📚 Resources

- [MongoDB Text Search](https://docs.mongodb.com/manual/text-search/)
- [Redis Caching Strategies](https://redis.io/docs/manual/patterns/)
- [Thai Language Processing](https://github.com/PyThaiNLP/pythainlp)
- [A/B Testing Best Practices](https://blog.optimizely.com/2016/12/20/ab-testing-best-practices/)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Submit pull request

## 📄 License

MIT License - see LICENSE file for details
