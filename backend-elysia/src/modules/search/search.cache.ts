import { Redis } from 'ioredis';

class SearchCache {
  private redis: Redis | null = null;
  private memoryCache: Map<string, { data: any; expires: number; }> = new Map();
  private defaultTTL = 300; // 5 minutes
  private useRedis = false;

  constructor() {
    this.initializeRedis();

    // Clean up expired memory cache entries every 5 minutes
    setInterval(
      () => {
        this.cleanupMemoryCache();
      },
      5 * 60 * 1000,
    );
  }

  private async initializeRedis() {
    try {
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD || undefined,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        connectTimeout: 10000,
        commandTimeout: 5000,
      });

      // Test connection
      await this.redis.ping();
      this.useRedis = true;
      console.log('✅ Redis connected successfully');
    }
    catch (error) {
      console.warn('⚠️ Redis connection failed, using memory cache:', (error as Error).message);
      this.redis = null;
      this.useRedis = false;
    }
  }

  private cleanupMemoryCache() {
    const now = Date.now();
    const entries = Array.from(this.memoryCache.entries());
    for (const [key, value] of entries) {
      if (value.expires < now) {
        this.memoryCache.delete(key);
      }
    }
  }

  private getKey(siteId: string, type: string, identifier: string): string {
    return `search:${siteId}:${type}:${identifier}`;
  }

  async get<T>(siteId: string, type: string, identifier: string): Promise<T | null> {
    const key = this.getKey(siteId, type, identifier);

    if (this.useRedis && this.redis) {
      try {
        const cached = await this.redis.get(key);
        return cached ? JSON.parse(cached) : null;
      }
      catch (error) {
        console.error('Redis get error:', error);
        // Fall back to memory cache
      }
    }

    // Use memory cache
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem && memoryItem.expires > Date.now()) {
      return memoryItem.data;
    }

    return null;
  }

  async set(siteId: string, type: string, identifier: string, data: any, ttl?: number): Promise<void> {
    const key = this.getKey(siteId, type, identifier);
    const ttlSeconds = ttl || this.defaultTTL;

    if (this.useRedis && this.redis) {
      try {
        const serialized = JSON.stringify(data);
        await this.redis.setex(key, ttlSeconds, serialized);
        return;
      }
      catch (error) {
        console.error('Redis set error:', error);
        // Fall back to memory cache
      }
    }

    // Use memory cache
    this.memoryCache.set(key, {
      data,
      expires: Date.now() + ttlSeconds * 1000,
    });
  }

  async del(siteId: string, type: string, identifier: string): Promise<void> {
    const key = this.getKey(siteId, type, identifier);

    if (this.useRedis && this.redis) {
      try {
        await this.redis.del(key);
      }
      catch (error) {
        console.error('Redis delete error:', error);
      }
    }

    // Also remove from memory cache
    this.memoryCache.delete(key);
  }

  async invalidatePattern(pattern: string): Promise<void> {
    if (this.useRedis && this.redis) {
      try {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
      catch (error) {
        console.error('Redis invalidate pattern error:', error);
      }
    }

    // For memory cache, remove matching keys
    const patternRegex = new RegExp(pattern.replace(/\*/g, '.*'));
    const keys = Array.from(this.memoryCache.keys());
    for (const key of keys) {
      if (patternRegex.test(key)) {
        this.memoryCache.delete(key);
      }
    }
  }

  async cacheSearchResults(siteId: string, query: string, filters: any, results: any): Promise<void> {
    const cacheKey = this.generateSearchCacheKey(query, filters);
    await this.set(siteId, 'search', cacheKey, results, 180); // 3 minutes for search results
  }

  async getCachedSearchResults(siteId: string, query: string, filters: any): Promise<any> {
    const cacheKey = this.generateSearchCacheKey(query, filters);
    return await this.get(siteId, 'search', cacheKey);
  }

  async cacheSuggestions(siteId: string, query: string, suggestions: any[]): Promise<void> {
    await this.set(siteId, 'suggestions', query.toLowerCase(), suggestions, 600); // 10 minutes
  }

  async getCachedSuggestions(siteId: string, query: string): Promise<any[]> {
    return (await this.get(siteId, 'suggestions', query.toLowerCase())) || [];
  }

  async cacheFilters(siteId: string, filters: any[]): Promise<void> {
    await this.set(siteId, 'filters', 'active', filters, 1800); // 30 minutes
  }

  async getCachedFilters(siteId: string): Promise<any[]> {
    return (await this.get(siteId, 'filters', 'active')) || [];
  }

  async cachePopularSearches(siteId: string, searches: any[]): Promise<void> {
    await this.set(siteId, 'popular', 'searches', searches, 3600); // 1 hour
  }

  async getCachedPopularSearches(siteId: string): Promise<any[]> {
    return (await this.get(siteId, 'popular', 'searches')) || [];
  }

  private generateSearchCacheKey(query: string, filters: any): string {
    const filterStr = JSON.stringify(filters || {});
    return Buffer.from(`${query}:${filterStr}`).toString('base64');
  }

  async invalidateSearchCache(siteId: string): Promise<void> {
    await this.invalidatePattern(`search:${siteId}:search:*`);
    await this.invalidatePattern(`search:${siteId}:suggestions:*`);
    await this.invalidatePattern(`search:${siteId}:filters:*`);
    await this.invalidatePattern(`search:${siteId}:popular:*`);
  }

  async close(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
    }
  }
}

export const searchCache = new SearchCache();
