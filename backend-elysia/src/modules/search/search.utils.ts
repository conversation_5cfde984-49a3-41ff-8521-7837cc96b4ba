import { distance } from 'fastest-levenshtein';

export class ThaiTextProcessor {
  private static synonyms: Map<string, string[]> = new Map([
    ['มือถือ', ['โทรศัพท์', 'สมาร์ทโฟน', 'phone']],
    ['คอมพิวเตอร์', ['คอม', 'PC', 'computer']],
    ['เสื้อผ้า', ['เสื้อ', 'เครื่องแต่งกาย', 'แฟชั่น']],
    ['อาหาร', ['กิน', 'ของกิน', 'food']],
    ['เครื่องดื่ม', ['น้ำ', 'drink', 'beverage']],
    ['รองเท้า', ['shoes', 'footwear']],
    ['กระเป๋า', ['bag', 'handbag']],
    ['นาฬิกา', ['watch', 'clock']],
    ['แว่นตา', ['glasses', 'eyewear']],
    ['เครื่องสำอาง', ['cosmetics', 'makeup', 'beauty']],
  ]);

  private static stopWords = new Set([
    'และ',
    'หรือ',
    'แต่',
    'ที่',
    'ใน',
    'บน',
    'ของ',
    'กับ',
    'จาก',
    'ไป',
    'มา',
    'เป็น',
    'คือ',
    'มี',
    'ได้',
    'จะ',
    'ไม่',
    'ไม่ใช่',
    'อยู่',
    'อยาก',
    'ต้อง',
    'ควร',
    'น่า',
    'เพื่อ',
    'เพราะ',
    'ถ้า',
    'หาก',
    'เมื่อ',
    'ขณะ',
    'ระหว่าง',
    'ก่อน',
    'หลัง',
    'แล้ว',
    'เสร็จ',
    'ยัง',
    'เคย',
    'เพิ่ง',
    'กำลัง',
    'อาจ',
    'คง',
    'น่าจะ',
    'ประมาณ',
    'เกือบ',
    'เกิน',
    'เท่า',
    'เท่านั้น',
    'เท่านั่น',
  ]);

  static processQuery(query: string): {
    original: string;
    processed: string;
    tokens: string[];
    synonyms: string[];
    fuzzyVariants: string[];
  } {
    const cleaned = this.cleanText(query);
    const tokens = this.tokenize(cleaned);
    const filteredTokens = this.removeStopWords(tokens);
    const synonyms = this.expandSynonyms(filteredTokens);
    const fuzzyVariants = this.generateFuzzyVariants(cleaned);

    return {
      original: query,
      processed: filteredTokens.join(' '),
      tokens: filteredTokens,
      synonyms,
      fuzzyVariants,
    };
  }

  private static cleanText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u0E00-\u0E7Fa-zA-Z0-9\s]/g, ' ') // Keep Thai, English, numbers, spaces
      .replace(/\s+/g, ' ')
      .trim();
  }

  private static tokenize(text: string): string[] {
    // Simple tokenization - in production, use proper Thai segmentation library
    const words = text.split(/\s+/);
    const tokens: string[] = [];

    for (const word of words) {
      if (word.length > 0) {
        // Split mixed Thai-English words
        const segments = word.match(/[\u0E00-\u0E7F]+|[a-zA-Z0-9]+/g) || [word];
        tokens.push(...segments);
      }
    }

    return tokens.filter(token => token.length > 0);
  }

  private static removeStopWords(tokens: string[]): string[] {
    return tokens.filter(token => !this.stopWords.has(token) && token.length > 1);
  }

  private static expandSynonyms(tokens: string[]): string[] {
    const expanded = new Set(tokens);

    for (const token of tokens) {
      const synonymList = this.synonyms.get(token);
      if (synonymList) {
        synonymList.forEach(synonym => expanded.add(synonym));
      }

      // Check reverse mapping
      const synonymEntries = Array.from(this.synonyms.entries());
      for (const [key, values] of synonymEntries) {
        if (values.includes(token)) {
          expanded.add(key);
          values.forEach(synonym => expanded.add(synonym));
        }
      }
    }

    return Array.from(expanded);
  }

  private static generateFuzzyVariants(query: string): string[] {
    const variants = new Set<string>();
    const words = query.split(' ');

    for (const word of words) {
      if (word.length > 3) {
        // Generate common typos
        variants.add(this.swapAdjacent(word));
        variants.add(this.removeChar(word));
        variants.add(this.addChar(word));
        variants.add(this.replaceChar(word));
      }
    }

    return Array.from(variants).filter(v => v !== query);
  }

  private static swapAdjacent(word: string): string {
    if (word.length < 2) return word;
    const pos = Math.floor(Math.random() * (word.length - 1));
    const chars = word.split('');
    [chars[pos], chars[pos + 1]] = [chars[pos + 1], chars[pos]];
    return chars.join('');
  }

  private static removeChar(word: string): string {
    if (word.length < 2) return word;
    const pos = Math.floor(Math.random() * word.length);
    return word.slice(0, pos) + word.slice(pos + 1);
  }

  private static addChar(word: string): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzกขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรลวศษสหฬอฮ';
    const pos = Math.floor(Math.random() * (word.length + 1));
    const char = chars[Math.floor(Math.random() * chars.length)];
    return word.slice(0, pos) + char + word.slice(pos);
  }

  private static replaceChar(word: string): string {
    if (word.length < 1) return word;
    const chars = 'abcdefghijklmnopqrstuvwxyzกขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรลวศษสหฬอฮ';
    const pos = Math.floor(Math.random() * word.length);
    const char = chars[Math.floor(Math.random() * chars.length)];
    return word.slice(0, pos) + char + word.slice(pos + 1);
  }

  static calculateRelevanceScore(query: string, document: any): number {
    const processed = this.processQuery(query);
    let score = 0;

    // Title match (highest weight)
    if (document.title) {
      score += this.calculateFieldScore(processed, document.title, 10);
    }

    // Name match
    if (document.searchableFields?.name) {
      score += this.calculateFieldScore(processed, document.searchableFields.name, 8);
    }

    // Tags match
    if (document.tags && Array.isArray(document.tags)) {
      const tagsText = document.tags.join(' ');
      score += this.calculateFieldScore(processed, tagsText, 5);
    }

    // Description match
    if (document.description) {
      score += this.calculateFieldScore(processed, document.description, 3);
    }

    // Content match (lowest weight)
    if (document.content) {
      score += this.calculateFieldScore(processed, document.content, 1);
    }

    // Popularity boost
    if (document.popularity) {
      score += Math.log(document.popularity + 1) * 0.1;
    }

    return score;
  }

  private static calculateFieldScore(processed: any, fieldText: string, weight: number): number {
    const fieldProcessed = this.processQuery(fieldText);
    let score = 0;

    // Exact match bonus
    if (fieldText.toLowerCase().includes(processed.original.toLowerCase())) {
      score += weight * 2;
    }

    // Token matches
    for (const token of processed.tokens) {
      if (fieldProcessed.tokens.includes(token)) {
        score += weight;
      }
    }

    // Synonym matches
    for (const synonym of processed.synonyms) {
      if (fieldProcessed.tokens.includes(synonym)) {
        score += weight * 0.7;
      }
    }

    // Fuzzy matches
    for (const variant of processed.fuzzyVariants) {
      for (const fieldToken of fieldProcessed.tokens) {
        if (distance(variant, fieldToken) <= 2) {
          score += weight * 0.3;
        }
      }
    }

    return score;
  }
}

export class SearchAnalytics {
  static async trackSearch(siteId: string, query: string, results: any, userId?: string): Promise<void> {
    // Track search patterns for analytics
    const analytics = {
      siteId,
      userId,
      query: query.toLowerCase(),
      resultCount: results.total || 0,
      timestamp: new Date(),
      hasResults: (results.total || 0) > 0,
    };

    // Store in time-series format for analytics
    // This could be sent to analytics service or stored in separate collection
    console.log('Search Analytics:', analytics);
  }

  static async trackClick(
    siteId: string,
    query: string,
    itemId: string,
    position: number,
    userId?: string,
  ): Promise<void> {
    const clickData = {
      siteId,
      userId,
      query: query.toLowerCase(),
      itemId,
      position,
      timestamp: new Date(),
    };

    console.log('Click Analytics:', clickData);
  }

  static generateSearchInsights(searchLogs: any[]): any {
    const insights = {
      totalSearches: searchLogs.length,
      uniqueQueries: new Set(searchLogs.map(log => log.query)).size,
      avgResultsPerSearch: searchLogs.reduce((sum, log) => sum + (log.results?.total || 0), 0) / searchLogs.length,
      noResultsRate: searchLogs.filter(log => (log.results?.total || 0) === 0).length / searchLogs.length,
      topQueries: this.getTopQueries(searchLogs),
      searchTrends: this.getSearchTrends(searchLogs),
    };

    return insights;
  }

  private static getTopQueries(searchLogs: any[]): any[] {
    const queryCount = new Map();

    searchLogs.forEach(log => {
      const count = queryCount.get(log.query) || 0;
      queryCount.set(log.query, count + 1);
    });

    return Array.from(queryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));
  }

  private static getSearchTrends(searchLogs: any[]): any[] {
    const trends = new Map();

    searchLogs.forEach(log => {
      const date = new Date(log.createdAt).toISOString().split('T')[0];
      const count = trends.get(date) || 0;
      trends.set(date, count + 1);
    });

    return Array.from(trends.entries())
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([date, count]) => ({ date, count }));
  }
}

export class AutocompleteEngine {
  private static readonly MAX_SUGGESTIONS = 10;

  static async generateSuggestions(siteId: string, query: string, searchIndex: any[]): Promise<string[]> {
    const processed = ThaiTextProcessor.processQuery(query);
    const suggestions = new Set<string>();

    // Add exact matches from search index
    for (const item of searchIndex) {
      if (item.title?.toLowerCase().includes(query.toLowerCase())) {
        suggestions.add(item.title);
      }

      if (item.searchableFields?.name?.toLowerCase().includes(query.toLowerCase())) {
        suggestions.add(item.searchableFields.name);
      }

      // Add tag matches
      if (item.tags) {
        for (const tag of item.tags) {
          if (tag.toLowerCase().includes(query.toLowerCase())) {
            suggestions.add(tag);
          }
        }
      }
    }

    // Add synonym suggestions
    for (const synonym of processed.synonyms) {
      if (synonym.includes(query.toLowerCase()) || query.toLowerCase().includes(synonym)) {
        suggestions.add(synonym);
      }
    }

    return Array.from(suggestions)
      .slice(0, this.MAX_SUGGESTIONS)
      .sort((a, b) => a.length - b.length); // Shorter suggestions first
  }

  static async getPopularCompletions(siteId: string, query: string, searchLogs: any[]): Promise<string[]> {
    const completions = new Map<string, number>();

    for (const log of searchLogs) {
      if (log.query.toLowerCase().startsWith(query.toLowerCase()) && log.query !== query) {
        const count = completions.get(log.query) || 0;
        completions.set(log.query, count + 1);
      }
    }

    return Array.from(completions.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, this.MAX_SUGGESTIONS)
      .map(([completion]) => completion);
  }
}
