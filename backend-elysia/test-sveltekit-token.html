<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Token ใน SvelteKit</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .token-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 ทดสอบ Token ใน SvelteKit</h1>
        
        <div class="status info">
            <strong>วัตถุประสงค์:</strong><br>
            ทดสอบการจัดการ token ใน SvelteKit dashboard และแก้ไขปัญหา "ไม่พบ token การยืนยันตัวตน"
        </div>

        <div>
            <h3>📋 ขั้นตอนการทดสอบ:</h3>
            <button onclick="testCookies()">🍪 ตรวจสอบ Cookies</button>
            <button onclick="testLocalStorage()">💾 ตรวจสอบ LocalStorage</button>
            <button onclick="testLogin()">🔑 ทดสอบ Login</button>
            <button onclick="testNotificationAPI()">🔔 ทดสอบ Notification API</button>
            <button onclick="clearLog()">🗑️ ล้าง Log</button>
        </div>

        <div>
            <h3>🔧 Manual Token Test:</h3>
            <input type="text" id="manualToken" class="token-input" placeholder="ใส่ token ที่ต้องการทดสอบ...">
            <button onclick="testManualToken()">🧪 ทดสอบ Token</button>
            <button onclick="saveTokenToCookies()">💾 บันทึกลง Cookies</button>
            <button onclick="saveTokenToLocalStorage()">💾 บันทึกลง LocalStorage</button>
        </div>

        <div id="status" class="status info">
            <strong>สถานะ:</strong> พร้อมทดสอบ
        </div>

        <h3>📋 Log:</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<strong>สถานะ:</strong> ${message}`;
            statusDiv.className = `status ${type}`;
        }

        function testCookies() {
            log('🍪 ตรวจสอบ Cookies...');
            
            const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                if (key && value) {
                    acc[key] = decodeURIComponent(value);
                }
                return acc;
            }, {});

            log(`📋 พบ Cookies: ${Object.keys(cookies).length} รายการ`);
            Object.keys(cookies).forEach(key => {
                const value = cookies[key];
                const isToken = key.includes('token') || key.includes('auth');
                log(`   ${key}: ${isToken ? value.substring(0, 20) + '...' : value}`, isToken ? 'success' : 'info');
            });

            const authToken = cookies['auth_token'] || cookies['accessToken'];
            if (authToken) {
                log('✅ พบ Auth Token ใน Cookies!', 'success');
                updateStatus('พบ Token ใน Cookies', 'success');
            } else {
                log('❌ ไม่พบ Auth Token ใน Cookies', 'error');
                updateStatus('ไม่พบ Token ใน Cookies', 'error');
            }
        }

        function testLocalStorage() {
            log('💾 ตรวจสอบ LocalStorage...');
            
            try {
                const keys = Object.keys(localStorage);
                log(`📋 พบ LocalStorage items: ${keys.length} รายการ`);
                
                keys.forEach(key => {
                    const value = localStorage.getItem(key);
                    const isToken = key.includes('token') || key.includes('auth');
                    log(`   ${key}: ${isToken ? value.substring(0, 20) + '...' : value}`, isToken ? 'success' : 'info');
                });

                const authToken = localStorage.getItem('auth_token') || localStorage.getItem('accessToken');
                if (authToken) {
                    log('✅ พบ Auth Token ใน LocalStorage!', 'success');
                    updateStatus('พบ Token ใน LocalStorage', 'success');
                } else {
                    log('❌ ไม่พบ Auth Token ใน LocalStorage', 'error');
                    updateStatus('ไม่พบ Token ใน LocalStorage', 'error');
                }
            } catch (error) {
                log(`❌ Error accessing LocalStorage: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            log('🔑 ทดสอบ Login...');
            updateStatus('กำลังทดสอบ Login...', 'info');

            try {
                const response = await fetch('http://localhost:5000/v1/user/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    log('✅ Login สำเร็จ!', 'success');
                    log(`📋 Token Length: ${result.data.token.length}`);
                    log(`📋 Token Preview: ${result.data.token.substring(0, 30)}...`);
                    
                    // แสดง token ใน input field
                    document.getElementById('manualToken').value = result.data.token;
                    
                    updateStatus('Login สำเร็จ', 'success');
                } else {
                    log(`❌ Login ล้มเหลว: ${result.message}`, 'error');
                    updateStatus('Login ล้มเหลว', 'error');
                }
            } catch (error) {
                log(`❌ Login Error: ${error.message}`, 'error');
                updateStatus('Login Error', 'error');
            }
        }

        async function testNotificationAPI() {
            log('🔔 ทดสอบ Notification API...');
            
            // ลองหา token จากหลายแหล่ง
            let token = null;
            
            // จาก cookies
            const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                if (key && value) {
                    acc[key] = decodeURIComponent(value);
                }
                return acc;
            }, {});
            token = cookies['auth_token'] || cookies['accessToken'];
            
            // จาก localStorage
            if (!token) {
                token = localStorage.getItem('auth_token') || localStorage.getItem('accessToken');
            }
            
            // จาก manual input
            if (!token) {
                token = document.getElementById('manualToken').value;
            }

            if (!token) {
                log('❌ ไม่พบ Token สำหรับทดสอบ API', 'error');
                updateStatus('ไม่พบ Token', 'error');
                return;
            }

            log(`🔑 ใช้ Token: ${token.substring(0, 20)}...`);

            try {
                const response = await fetch('http://localhost:5000/v1/notifications/user', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    log('✅ Notification API สำเร็จ!', 'success');
                    log(`📋 Notifications: ${result.data?.notifications?.length || 0} รายการ`);
                    log(`📋 Unread Count: ${result.data?.unreadCount || 0} รายการ`);
                    updateStatus('API ทำงานได้', 'success');
                } else {
                    log(`❌ API Error: ${result.message}`, 'error');
                    updateStatus('API Error', 'error');
                }
            } catch (error) {
                log(`❌ API Request Error: ${error.message}`, 'error');
                updateStatus('API Request Error', 'error');
            }
        }

        async function testManualToken() {
            const token = document.getElementById('manualToken').value;
            if (!token) {
                log('❌ กรุณาใส่ Token ที่ต้องการทดสอบ', 'error');
                return;
            }

            log(`🧪 ทดสอบ Manual Token: ${token.substring(0, 20)}...`);
            
            try {
                const response = await fetch('http://localhost:5000/v1/notifications/user', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    log('✅ Manual Token ใช้งานได้!', 'success');
                    updateStatus('Manual Token ใช้งานได้', 'success');
                } else {
                    log(`❌ Manual Token ใช้งานไม่ได้: ${result.message}`, 'error');
                    updateStatus('Manual Token ใช้งานไม่ได้', 'error');
                }
            } catch (error) {
                log(`❌ Manual Token Test Error: ${error.message}`, 'error');
            }
        }

        function saveTokenToCookies() {
            const token = document.getElementById('manualToken').value;
            if (!token) {
                log('❌ กรุณาใส่ Token ที่ต้องการบันทึก', 'error');
                return;
            }

            document.cookie = `auth_token=${token}; path=/; max-age=86400`;
            log('✅ บันทึก Token ลง Cookies แล้ว', 'success');
            updateStatus('บันทึก Token ลง Cookies แล้ว', 'success');
        }

        function saveTokenToLocalStorage() {
            const token = document.getElementById('manualToken').value;
            if (!token) {
                log('❌ กรุณาใส่ Token ที่ต้องการบันทึก', 'error');
                return;
            }

            localStorage.setItem('auth_token', token);
            log('✅ บันทึก Token ลง LocalStorage แล้ว', 'success');
            updateStatus('บันทึก Token ลง LocalStorage แล้ว', 'success');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            updateStatus('พร้อมทดสอบ', 'info');
        }

        // Auto-run initial tests
        window.onload = function() {
            log('🌐 หน้าเว็บโหลดเสร็จแล้ว');
            log('💡 คลิกปุ่มต่างๆ เพื่อทดสอบ Token');
        };
    </script>
</body>
</html>