const WebSocket = require('ws');

// ข้อมูลทดสอบ
const TEST_TOKEN = 'your_jwt_token_here'; // ต้องแทนที่ด้วย token จริง
const TEST_SITE_ID = 'your_site_id_here'; // ต้องแทนที่ด้วย site ID จริง
const WS_URL = 'ws://localhost:3000/api/notifications/ws';

async function testWebSocket() {
  console.log('🔌 เริ่มทดสอบ WebSocket Notification...');

  const ws = new WebSocket(WS_URL);

  ws.on('open', () => {
    console.log('✅ WebSocket connected');

    // ส่ง authentication
    ws.send(JSON.stringify({
      type: 'auth',
      data: {
        token: TEST_TOKEN,
        siteId: TEST_SITE_ID,
      },
    }));
  });

  ws.on('message', data => {
    try {
      const message = JSON.parse(data.toString());
      console.log('📨 Received:', message);

      switch (message.type) {
        case 'auth_success':
          console.log('✅ Authentication successful');
          break;

        case 'auth_error':
          console.log('❌ Authentication failed:', message.data?.message);
          break;

        case 'notification':
          console.log('🔔 New notification:', message.data);
          break;

        case 'unread_count':
          console.log('📊 Unread count updated:', message.data?.count);
          break;

        case 'pong':
          console.log('🏓 Pong received');
          break;

        default:
          console.log('❓ Unknown message type:', message.type);
      }
    }
    catch (error) {
      console.error('❌ Error parsing message:', error);
    }
  });

  ws.on('close', (code, reason) => {
    console.log('🔌 WebSocket closed:', code, reason);
  });

  ws.on('error', error => {
    console.error('❌ WebSocket error:', error);
  });

  // ส่ง ping ทุก 10 วินาที
  setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: 'ping' }));
    }
  }, 10000);

  // ปิดการเชื่อมต่อหลังจาก 60 วินาที
  setTimeout(() => {
    console.log('⏰ Closing connection after 60 seconds...');
    ws.close(1000, 'Test completed');
  }, 60000);
}

// ฟังก์ชันทดสอบการส่งการแจ้งเตือน
async function testNotificationSending() {
  console.log('\n🧪 ทดสอบการส่งการแจ้งเตือน...');

  try {
    const response = await fetch('http://localhost:3000/api/notifications/topup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`,
      },
      body: JSON.stringify({
        userId: 'test_user_id',
        amount: 1000,
        balance: 5000,
      }),
    });

    const result = await response.json();
    console.log('📤 Notification sent:', result);
  }
  catch (error) {
    console.error('❌ Error sending notification:', error);
  }
}

// เริ่มการทดสอบ
if (require.main === module) {
  console.log('🚀 Starting WebSocket Notification Tests...');
  console.log('📝 Note: Please update TEST_TOKEN and TEST_SITE_ID with real values');

  testWebSocket();

  // ทดสอบการส่งการแจ้งเตือนหลังจาก 5 วินาที
  setTimeout(testNotificationSending, 5000);
}

module.exports = { testWebSocket, testNotificationSending };
