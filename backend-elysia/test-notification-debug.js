// Test Notification System Debug
// ใช้ใน browser console เพื่อทดสอบ notification system

console.log('🔔 Starting Notification System Debug...');

// 1. ตรวจสอบ cookies
function checkCookies() {
  console.log('🍪 Checking cookies...');
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    if (key && value) {
      acc[key] = decodeURIComponent(value);
    }
    return acc;
  }, {});

  console.log('Available cookies:', Object.keys(cookies));
  console.log('auth_token:', cookies['auth_token'] ? 'exists' : 'missing');
  console.log('accessToken:', cookies['accessToken'] ? 'exists' : 'missing');

  return cookies;
}

// 2. ทดสอบ token helper
function testTokenHelper() {
  console.log('🔐 Testing token helper...');

  // ใช้ token helper function (ถ้ามี)
  if (typeof getAuthToken === 'function') {
    const tokenInfo = getAuthToken();
    console.log('Token info from helper:', tokenInfo);
  }
  else {
    console.log('getAuthToken function not available');
  }
}

// 3. ทดสอบ notification API
async function testNotificationAPI() {
  console.log('📡 Testing notification API...');

  const cookies = checkCookies();
  const token = cookies['auth_token'] || cookies['accessToken'];

  if (!token) {
    console.error('❌ No token found for API test');
    return;
  }

  try {
    const response = await fetch('/api/notifications/user', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    console.log('API Response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Success:', data);
    }
    else {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
    }
  }
  catch (error) {
    console.error('❌ API Request failed:', error);
  }
}

// 4. ทดสอบ WebSocket
function testWebSocket() {
  console.log('🔌 Testing WebSocket...');

  const cookies = checkCookies();
  const token = cookies['auth_token'] || cookies['accessToken'];

  if (!token) {
    console.error('❌ No token found for WebSocket test');
    return;
  }

  const backendUrl = 'http://localhost:5000';
  const wsProtocol = backendUrl.startsWith('https:') ? 'wss:' : 'ws:';
  const wsHost = backendUrl.replace(/^https?:\/\//, '');
  const wsUrl = `${wsProtocol}//${wsHost}/v1/notifications/ws`;

  console.log('Connecting to:', wsUrl);

  const ws = new WebSocket(wsUrl);

  const timeout = setTimeout(() => {
    console.error('⏰ WebSocket connection timeout');
    ws.close();
  }, 10000);

  ws.onopen = () => {
    clearTimeout(timeout);
    console.log('✅ WebSocket connected');

    // Send auth message
    ws.send(JSON.stringify({
      type: 'auth',
      data: { token },
    }));
    console.log('🔐 Sent auth message');
  };

  ws.onmessage = event => {
    try {
      const message = JSON.parse(event.data);
      console.log('📨 WebSocket message:', message);
    }
    catch (e) {
      console.log('📨 WebSocket raw message:', event.data);
    }
  };

  ws.onclose = event => {
    clearTimeout(timeout);
    console.log('🔌 WebSocket closed:', event.code, event.reason);
  };

  ws.onerror = error => {
    clearTimeout(timeout);
    console.error('❌ WebSocket error:', error);
  };

  // Close after 30 seconds
  setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.close();
      console.log('🔌 WebSocket test completed');
    }
  }, 30000);
}

// 5. ทดสอบ notification store (ถ้าอยู่ใน SvelteKit context)
function testNotificationStore() {
  console.log('🏪 Testing notification store...');

  // ตรวจสอบว่ามี notificationStore หรือไม่
  if (typeof window !== 'undefined' && window.notificationStore) {
    console.log('Notification store available');
    console.log('Current state:', {
      notifications: window.notificationStore.notifications,
      unreadCount: window.notificationStore.unreadCount,
      isLoading: window.notificationStore.isLoading,
      error: window.notificationStore.error,
    });
  }
  else {
    console.log('Notification store not available in global scope');
  }
}

// รัน tests ทั้งหมด
async function runAllTests() {
  console.log('🚀 Running all notification tests...');

  checkCookies();
  testTokenHelper();
  testNotificationStore();

  await testNotificationAPI();
  testWebSocket();

  console.log('✅ All tests completed');
}

// Export functions for manual testing
window.notificationDebug = {
  checkCookies,
  testTokenHelper,
  testNotificationAPI,
  testWebSocket,
  testNotificationStore,
  runAllTests,
};

console.log('🔔 Notification debug tools loaded!');
console.log('Use: notificationDebug.runAllTests() to run all tests');
console.log('Or use individual functions like: notificationDebug.checkCookies()');
