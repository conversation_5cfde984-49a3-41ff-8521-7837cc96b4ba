<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notification Token</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Notification Token & WebSocket</h1>
    
    <div>
        <button onclick="checkToken()">Check Token</button>
        <button onclick="testNotificationAPI()">Test Notification API</button>
        <button onclick="testWebSocket()">Test WebSocket</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Token helper functions (copied from your token-helper.ts)
        function getAuthToken() {
            const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                if (key && value) {
                    acc[key] = decodeURIComponent(value);
                }
                return acc;
            }, {});

            let token = cookies['auth_token'] || cookies['accessToken'];
            if (token) {
                return { token, source: 'cookies', isValid: isTokenValid(token) };
            }

            try {
                token = localStorage.getItem('auth_token') || localStorage.getItem('accessToken');
                if (token) {
                    return { token, source: 'localStorage', isValid: isTokenValid(token) };
                }
            } catch (e) {
                // Ignore localStorage errors
            }

            return { token: null, source: 'none', isValid: false };
        }

        function isTokenValid(token) {
            if (!token) return false;

            try {
                const parts = token.split('.');
                if (parts.length !== 3) return false;

                const payload = JSON.parse(atob(parts[1]));
                const now = Math.floor(Date.now() / 1000);

                if (payload.exp && payload.exp < now) {
                    console.warn('🔐 Token expired');
                    return false;
                }

                return true;
            } catch (error) {
                console.warn('🔐 Invalid token format:', error);
                return false;
            }
        }

        function checkToken() {
            const tokenInfo = getAuthToken();
            
            addResult(`Token Info:`, 'info');
            addResult(`<pre>${JSON.stringify(tokenInfo, null, 2)}</pre>`, 'info');
            
            if (!tokenInfo.token) {
                addResult('❌ No token found! You need to login first.', 'error');
                addResult('Go to <a href="/signin">/signin</a> to login', 'warning');
            } else if (!tokenInfo.isValid) {
                addResult('⚠️ Token found but invalid or expired', 'warning');
            } else {
                addResult('✅ Valid token found', 'success');
                
                // Decode and show token payload
                try {
                    const payload = JSON.parse(atob(tokenInfo.token.split('.')[1]));
                    addResult(`Token Payload:`, 'info');
                    addResult(`<pre>${JSON.stringify(payload, null, 2)}</pre>`, 'info');
                } catch (e) {
                    addResult('Failed to decode token payload', 'error');
                }
            }
        }

        async function testNotificationAPI() {
            const tokenInfo = getAuthToken();
            
            if (!tokenInfo.token) {
                addResult('❌ No token available for API test', 'error');
                return;
            }

            try {
                addResult('🔄 Testing notification API...', 'info');
                
                const response = await fetch('/api/notifications/user', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${tokenInfo.token}`,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                addResult(`API Response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`API Data:`, 'success');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`API Error: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`API Test Error: ${error.message}`, 'error');
            }
        }

        function testWebSocket() {
            const tokenInfo = getAuthToken();
            
            if (!tokenInfo.token) {
                addResult('❌ No token available for WebSocket test', 'error');
                return;
            }

            try {
                addResult('🔄 Testing WebSocket connection...', 'info');
                
                const backendUrl = 'http://localhost:5000';
                const wsProtocol = backendUrl.startsWith('https:') ? 'wss:' : 'ws:';
                const wsHost = backendUrl.replace(/^https?:\/\//, '');
                const wsUrl = `${wsProtocol}//${wsHost}/v1/notifications/ws`;
                
                addResult(`Connecting to: ${wsUrl}`, 'info');
                
                const ws = new WebSocket(wsUrl);
                
                const timeout = setTimeout(() => {
                    addResult('⏰ WebSocket connection timeout', 'error');
                    ws.close();
                }, 10000);

                ws.onopen = () => {
                    clearTimeout(timeout);
                    addResult('✅ WebSocket connected successfully', 'success');
                    
                    // Send auth message
                    ws.send(JSON.stringify({
                        type: 'auth',
                        data: { token: tokenInfo.token }
                    }));
                    addResult('🔐 Sent authentication message', 'info');
                };

                ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        addResult(`📨 WebSocket Message:`, 'success');
                        addResult(`<pre>${JSON.stringify(message, null, 2)}</pre>`, 'info');
                    } catch (e) {
                        addResult(`📨 WebSocket Raw Message: ${event.data}`, 'info');
                    }
                };

                ws.onclose = (event) => {
                    clearTimeout(timeout);
                    addResult(`🔌 WebSocket closed: ${event.code} - ${event.reason}`, 'warning');
                };

                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    addResult(`❌ WebSocket error: ${error}`, 'error');
                };

                // Close connection after 30 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close();
                        addResult('🔌 WebSocket connection closed (test completed)', 'info');
                    }
                }, 30000);

            } catch (error) {
                addResult(`WebSocket Test Error: ${error.message}`, 'error');
            }
        }

        // Auto-check token on load
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>