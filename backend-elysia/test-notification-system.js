#!/usr/bin/env node

/**
 * ทดสอบระบบ Notification และ WebSocket
 * ใช้สำหรับตรวจสอบการทำงานของระบบแจ้งเตือน
 */

import fetch from 'node-fetch';
import WebSocket from 'ws';

// Configuration
const API_BASE_URL = 'http://localhost:5000/v1';
const WS_URL = 'ws://localhost:5000/v1/notifications/ws';

// Test data
const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123',
};

let authToken = null;
let userId = null;
let siteId = null;

async function main() {
  console.log('🚀 เริ่มทดสอบระบบ Notification');

  try {
    // 1. ทดสอบการ Login
    console.log('\n1️⃣ ทดสอบการ Login...');
    await testLogin();

    // 2. ทดสอบ API Endpoints
    console.log('\n2️⃣ ทดสอบ Notification API...');
    await testNotificationAPI();

    // 3. ทดสอบ WebSocket Connection
    console.log('\n3️⃣ ทดสอบ WebSocket Connection...');
    await testWebSocketConnection();

    console.log('\n✅ การทดสอบเสร็จสิ้น');
  }
  catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการทดสอบ:', error.message);
    process.exit(1);
  }
}

async function testLogin() {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      authToken = result.data.accessToken;
      userId = result.data.user._id;
      siteId = result.data.user.siteId;

      console.log('✅ Login สำเร็จ');
      console.log(`   User ID: ${userId}`);
      console.log(`   Site ID: ${siteId}`);
      console.log(`   Token: ${authToken ? 'มี' : 'ไม่มี'}`);
    }
    else {
      throw new Error(`Login ล้มเหลว: ${result.error || 'Unknown error'}`);
    }
  }
  catch (error) {
    throw new Error(`Login Error: ${error.message}`);
  }
}

async function testNotificationAPI() {
  if (!authToken) {
    throw new Error('ไม่มี auth token');
  }

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json',
  };

  try {
    // ทดสอบดึงการแจ้งเตือนส่วนตัว
    console.log('   📋 ทดสอบดึงการแจ้งเตือนส่วนตัว...');
    const userNotificationsResponse = await fetch(`${API_BASE_URL}/notifications/user`, {
      headers,
    });

    const userNotificationsResult = await userNotificationsResponse.json();

    if (userNotificationsResponse.ok) {
      console.log('   ✅ ดึงการแจ้งเตือนส่วนตัวสำเร็จ');
      console.log(`      จำนวน: ${userNotificationsResult.data?.notifications?.length || 0} รายการ`);
    }
    else {
      console.log('   ❌ ดึงการแจ้งเตือนส่วนตัวล้มเหลว:', userNotificationsResult.error);
    }

    // ทดสอบดึงจำนวนที่ยังไม่อ่าน
    console.log('   📊 ทดสอบดึงจำนวนที่ยังไม่อ่าน...');
    const unreadCountResponse = await fetch(`${API_BASE_URL}/notifications/user/unread-count`, {
      headers,
    });

    const unreadCountResult = await unreadCountResponse.json();

    if (unreadCountResponse.ok) {
      console.log('   ✅ ดึงจำนวนที่ยังไม่อ่านสำเร็จ');
      console.log(`      จำนวน: ${unreadCountResult.data?.unreadCount || 0} รายการ`);
    }
    else {
      console.log('   ❌ ดึงจำนวนที่ยังไม่อ่านล้มเหลว:', unreadCountResult.error);
    }

    // ทดสอบดึงการแจ้งเตือนของไซต์ (ถ้ามี siteId)
    if (siteId) {
      console.log('   🏢 ทดสอบดึงการแจ้งเตือนของไซต์...');
      const siteNotificationsResponse = await fetch(`${API_BASE_URL}/notifications?page=1&limit=10`, {
        headers,
      });

      const siteNotificationsResult = await siteNotificationsResponse.json();

      if (siteNotificationsResponse.ok) {
        console.log('   ✅ ดึงการแจ้งเตือนของไซต์สำเร็จ');
        console.log(`      จำนวน: ${siteNotificationsResult.data?.notifications?.length || 0} รายการ`);
      }
      else {
        console.log('   ❌ ดึงการแจ้งเตือนของไซต์ล้มเหลว:', siteNotificationsResult.error);
      }
    }
  }
  catch (error) {
    console.log('   ❌ API Test Error:', error.message);
  }
}

async function testWebSocketConnection() {
  if (!authToken) {
    throw new Error('ไม่มี auth token');
  }

  return new Promise((resolve, reject) => {
    console.log(`   🔌 เชื่อมต่อไปยัง: ${WS_URL}`);

    const ws = new WebSocket(WS_URL);
    let isAuthenticated = false;

    // Timeout สำหรับการเชื่อมต่อ
    const connectionTimeout = setTimeout(() => {
      console.log('   ⏰ WebSocket connection timeout');
      ws.close();
      resolve();
    }, 10000);

    ws.on('open', () => {
      console.log('   ✅ WebSocket เชื่อมต่อสำเร็จ');

      // ส่งข้อมูลการยืนยันตัวตน
      console.log('   🔐 ส่งข้อมูลการยืนยันตัวตน...');
      ws.send(JSON.stringify({
        type: 'auth',
        data: {
          token: authToken,
        },
      }));
    });

    ws.on('message', data => {
      try {
        const message = JSON.parse(data.toString());
        console.log(`   📨 ได้รับข้อความ: ${message.type}`);

        switch (message.type) {
          case 'auth_success':
            console.log('   ✅ การยืนยันตัวตนสำเร็จ');
            console.log(`      User ID: ${message.data?.userId}`);
            console.log(`      User Type: ${message.data?.userType}`);
            isAuthenticated = true;

            // ทดสอบส่ง ping
            setTimeout(() => {
              console.log('   🏓 ส่ง ping...');
              ws.send(JSON.stringify({ type: 'ping' }));
            }, 1000);

            break;

          case 'auth_error':
            console.log('   ❌ การยืนยันตัวตนล้มเหลว:', message.message);
            break;

          case 'pong':
            console.log('   🏓 ได้รับ pong');
            break;

          case 'notification':
            console.log('   🔔 ได้รับการแจ้งเตือน:', message.data?.title);
            break;

          case 'unread_count':
            console.log('   📊 อัปเดตจำนวนที่ยังไม่อ่าน:', message.data?.count);
            break;

          case 'error':
            console.log('   ❌ WebSocket Error:', message.message);
            break;

          default:
            console.log(`   ❓ ข้อความไม่รู้จัก: ${message.type}`);
        }
      }
      catch (error) {
        console.log('   ❌ Error parsing message:', error.message);
      }
    });

    ws.on('error', error => {
      console.log('   ❌ WebSocket Error:', error.message);
    });

    ws.on('close', (code, reason) => {
      clearTimeout(connectionTimeout);
      console.log(`   🔌 WebSocket ปิดการเชื่อมต่อ: ${code} - ${reason}`);

      if (isAuthenticated) {
        console.log('   ✅ WebSocket ทำงานปกติ');
      }
      else {
        console.log('   ❌ WebSocket ไม่สามารถยืนยันตัวตนได้');
      }

      resolve();
    });

    // ปิดการเชื่อมต่อหลังจาก 5 วินาที
    setTimeout(() => {
      if (ws.readyState === WebSocket.OPEN) {
        console.log('   🔌 ปิดการเชื่อมต่อ WebSocket...');
        ws.close(1000, 'Test completed');
      }
    }, 5000);
  });
}

// เรียกใช้การทดสอบ
main().catch(console.error);

export { testLogin, testNotificationAPI, testWebSocketConnection };
